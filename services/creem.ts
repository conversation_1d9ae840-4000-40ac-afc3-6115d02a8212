import { Order } from "@/types/order";
import { PricingItem } from "@/types/blocks/pricing";

// Creem 产品ID映射表
// 请在创建产品后，将您的产品ID填入此处
const CREEM_PRODUCT_ID_MAP: Record<string, string> = {
  // 免费版 - 不需要产品ID
  'free': 'free_no_payment_needed',
  
  // 专业版 - 使用您的3个月产品
  'pro_monthly': 'prod_3rDi4h6HXsofQvCQg8liRP', // $4.49/3个月
  
  // 企业版 - 请替换为您的实际产品ID（如果创建了的话）
  'enterprise_monthly': 'prod_1OcPVhMnVmfxoae5l6hcwD', // 替换这里！
  
  // 其他暂时不用的映射
  'basic_monthly': 'prod_YOUR_PRO_PRODUCT_ID_HERE',
  'basic_yearly': 'prod_YOUR_PRO_PRODUCT_ID_HERE',
  'pro_yearly': 'prod_YOUR_PRO_PRODUCT_ID_HERE',
  'enterprise_yearly': 'prod_1OcPVhMnVmfxoae5l6hcwD',
};

// Creem API 配置
const CREEM_API_BASE = process.env.NODE_ENV === 'production' 
  ? 'https://api.creem.io/v1' 
  : 'https://test-api.creem.io/v1'; // 测试环境使用 test-api.creem.io

const CREEM_API_KEY = process.env.CREEM_API_KEY;

if (!CREEM_API_KEY) {
  console.warn('CREEM_API_KEY not found in environment variables');
}

// Creem API 请求头
const getHeaders = () => ({
  'Content-Type': 'application/json',
  'x-api-key': CREEM_API_KEY || '',
});

// Creem 产品接口
export interface CreemProduct {
  id: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  type: 'one_time' | 'subscription';
  interval?: 'month' | 'year';
}

// Creem 结账会话接口
export interface CreemCheckoutSession {
  id: string;
  url?: string;
  checkout_url?: string; // Creem 返回的是 checkout_url
  product_id?: string;
  product?: string; // Creem 返回的是 product
  customer_email?: string;
  success_url?: string;
  cancel_url?: string;
  request_id?: string;
  status?: string;
  mode?: string;
}

// Creem 结账响应接口
export interface CreemCheckoutResponse {
  checkout_id: string;
  order_id?: string;
  customer_id?: string;
  subscription_id?: string;
  product_id: string;
  request_id?: string;
  signature: string;
  status: 'completed' | 'pending' | 'failed';
}

/**
 * 创建 Creem 产品
 */
export async function createCreemProduct(productData: {
  name: string;
  description?: string;
  price: number;
  currency: string;
  type: 'one_time' | 'subscription';
  interval?: 'month' | 'year';
}): Promise<CreemProduct> {
  try {
    const response = await fetch(`${CREEM_API_BASE}/products`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(productData),
    });

    if (!response.ok) {
      throw new Error(`Creem API error: ${response.status} ${response.statusText}`);
    }

    const product = await response.json();
    return product;
  } catch (error) {
    console.error('Failed to create Creem product:', error);
    throw error;
  }
}

/**
 * 创建 Creem 结账会话
 */
export async function createCreemCheckoutSession(params: {
  product_id: string;
  customer_email?: string;
  success_url?: string;
  cancel_url?: string;
  request_id?: string;
}): Promise<CreemCheckoutSession> {
  try {
    // Creem API 只需要 product_id 和 success_url
    const creemParams: any = {
      product_id: params.product_id,
    };
    
    // 只添加 success_url（如果提供）
    if (params.success_url) {
      creemParams.success_url = params.success_url;
    }
    
    // 可能需要 request_id
    if (params.request_id) {
      creemParams.request_id = params.request_id;
    }
    
    console.log('Creating Creem checkout session with params:', {
      ...creemParams,
      api_key: CREEM_API_KEY ? 'Set' : 'Not set'
    });

    const response = await fetch(`${CREEM_API_BASE}/checkouts`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(creemParams),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Creem API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const session = await response.json();
    return session;
  } catch (error) {
    console.error('Failed to create Creem checkout session:', error);
    throw error;
  }
}

/**
 * 获取 Creem 产品信息
 */
export async function getCreemProduct(productId: string): Promise<CreemProduct> {
  try {
    const response = await fetch(`${CREEM_API_BASE}/products/${productId}`, {
      method: 'GET',
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Creem API error: ${response.status} ${response.statusText}`);
    }

    const product = await response.json();
    return product;
  } catch (error) {
    console.error('Failed to get Creem product:', error);
    throw error;
  }
}

/**
 * 验证 Creem 签名
 */
export function verifyCreemSignature(params: Record<string, string>, signature: string): boolean {
  try {
    // 创建签名字符串（按字母顺序排序参数）
    const sortedParams = Object.keys(params)
      .filter(key => key !== 'signature')
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');

    // 使用 API key 作为密钥进行 HMAC-SHA256 签名验证
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', CREEM_API_KEY)
      .update(sortedParams)
      .digest('hex');

    return expectedSignature === signature;
  } catch (error) {
    console.error('Failed to verify Creem signature:', error);
    return false;
  }
}

/**
 * 获取Creem产品ID
 */
export async function getCreemProductId(productId: string): Promise<string> {
  // 免费版不需要支付
  if (productId === 'free') {
    throw new Error('Free plan does not require payment');
  }
  
  // 检查是否已有映射的产品ID
  const existingProductId = CREEM_PRODUCT_ID_MAP[productId];
  
  // 检查是否是占位符
  if (existingProductId && !existingProductId.includes('YOUR_') && !existingProductId.includes('_HERE')) {
    return existingProductId;
  }

  // 如果没有找到映射或是占位符，抛出错误
  throw new Error(`Creem product not configured for: ${productId}. Please create the product in Creem Dashboard and update CREEM_PRODUCT_ID_MAP.`);
}

/**
 * 将 PricingItem 转换为 Creem 产品格式
 */
export function pricingItemToCreemProduct(item: PricingItem): Omit<CreemProduct, 'id'> {
  return {
    name: item.product_name || item.title || 'Product',
    description: item.description,
    price: item.amount, // Creem 使用分为单位
    currency: (item.currency || 'usd').toLowerCase(),
    type: item.interval === 'one-time' ? 'one_time' : 'subscription',
    interval: item.interval === 'one-time' ? undefined : item.interval,
  };
}

/**
 * 处理 Creem 支付成功回调
 */
export async function handleCreemPaymentSuccess(params: CreemCheckoutResponse): Promise<void> {
  try {
    // 验证签名
    if (!verifyCreemSignature(params as any, params.signature)) {
      throw new Error('Invalid Creem signature');
    }

    // 这里可以添加订单处理逻辑
    console.log('Creem payment successful:', params);
    
    // 可以调用现有的订单处理函数
    // await handleOrderCompletion(params.request_id, params);
    
  } catch (error) {
    console.error('Failed to handle Creem payment success:', error);
    throw error;
  }
}

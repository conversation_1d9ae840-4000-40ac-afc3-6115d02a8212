# 🚨 Google OAuth 快速修复指南

## 问题
部署到线上后，Google登录重定向到 `localhost:3000`，显示 `ERR_CONNECTION_REFUSED`

## 🎯 3步快速修复

### 步骤1: 更新Google Console回调URL
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 进入 `APIs & Services` > `Credentials`
3. 编辑您的OAuth 2.0客户端
4. 在 "Authorized redirect URIs" 添加：
   ```
   https://your-domain.com/api/auth/callback/google
   ```
   **替换 `your-domain.com` 为您的实际域名**

### 步骤2: 设置环境变量
在您的部署平台（Vercel/Netlify等）中设置：

```bash
AUTH_URL=https://your-domain.com
NEXTAUTH_URL=https://your-domain.com
AUTH_GOOGLE_ID=653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96.apps.googleusercontent.com
AUTH_GOOGLE_SECRET=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
NEXT_PUBLIC_AUTH_GOOGLE_ID=653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96.apps.googleusercontent.com
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
NEXTAUTH_SECRET=your-random-secret-key
```

**生成NEXTAUTH_SECRET:**
```bash
openssl rand -base64 32
```

### 步骤3: 重新部署
重新部署您的应用，确保新的环境变量生效。

## 🔧 自动化修复

运行修复脚本（替换为您的域名）：
```bash
./fix-google-oauth.sh your-domain.com
```

## ✅ 验证修复

1. 访问您的线上网站
2. 点击 "Sign in with Google"
3. 应该重定向到您的域名而不是localhost

## 🆘 如果仍有问题

1. **检查环境变量**: 确保部署平台中的环境变量正确设置
2. **等待生效**: Google配置更改可能需要几分钟
3. **清除缓存**: 清除浏览器缓存
4. **检查控制台**: 查看浏览器开发者工具的错误信息

## 📞 常见回调URL格式

- **Vercel**: `https://your-app.vercel.app/api/auth/callback/google`
- **Netlify**: `https://your-app.netlify.app/api/auth/callback/google`
- **自定义域名**: `https://your-domain.com/api/auth/callback/google`

修复完成后，Google OAuth应该能正常工作！

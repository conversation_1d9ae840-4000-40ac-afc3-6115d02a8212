# Copy this file to .env.local and fill in your values

# NextAuth Configuration
AUTH_SECRET="your-auth-secret-here" # Generate with: openssl rand -base64 32

# Google OAuth Configuration
# 1. Go to https://console.cloud.google.com/
# 2. Create a new project or select existing one
# 3. Enable Google+ API
# 4. Create OAuth 2.0 credentials
# 5. Add authorized redirect URIs: http://localhost:3000/api/auth/callback/google
AUTH_GOOGLE_ID="your-google-client-id.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET="your-google-client-secret"
NEXT_PUBLIC_AUTH_GOOGLE_ID="your-google-client-id.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED="true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED="true"

# Email Service (Resend)
RESEND_API_KEY="re_your_resend_api_key"
EMAIL_FROM="HowLongFresh <<EMAIL>>"

# Application URL
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Supabase Configuration
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
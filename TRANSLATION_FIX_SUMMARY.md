# FAQ多语言修复总结

## 问题描述
FAQ详情页面存在中英文夹杂的问题，需要修复以下硬编码的中文文本：
- "关键要点："
- "相关食物："
- "查看原文"
- "数据来源: StillTasty.com"
- "查看更多FAQ"
- 底部的联系信息文本

## 修复内容

### 1. 添加翻译键
在 `i18n/messages/zh.json` 和 `i18n/messages/en.json` 中添加了以下翻译键：

```json
{
  "faq": {
    "key_points": "关键要点：" / "Key Points:",
    "related_foods": "相关食物：" / "Related Foods:",
    "view_original": "查看原文" / "View Original",
    "data_source": "数据来源" / "Data Source",
    "load_more": "查看更多FAQ ({count} 个)" / "View More FAQs ({count} more)",
    "no_answer_found": "没有找到您要的答案？" / "Didn't find the answer you're looking for?",
    "contact_description": "我们的FAQ内容来源于权威的USDA数据和StillTasty专业资料。如果您有其他问题，欢迎通过以下方式联系我们。" / "Our FAQ content is sourced from authoritative USDA data and StillTasty professional materials. If you have other questions, please contact us through the following methods."
  }
}
```

### 2. 修复的组件文件

#### `components/faq/FAQList.tsx`
- 修复"关键要点："文本：`{t('key_points')}`
- 修复"相关食物："文本：`{t('related_foods')}`
- 修复"查看原文"按钮：`text={t('view_original')}`
- 移除"数据来源: StillTasty.com"显示
- 修复"查看更多FAQ"按钮：`{t('load_more', { count: faqs.length - maxItems })}`

#### `components/faq/PopularFAQs.tsx`
- 修复"关键要点："文本：`{t('key_points')}`
- 修复"查看原文"按钮：`text={t('view_original')}`
- 移除"数据来源: StillTasty.com"显示

#### `components/ui/view-source-button.tsx`
- 添加 `useTranslations` 钩子
- 修改 `ViewFAQButton` 组件使用翻译：`text={text || t('view_original')}`

#### `app/[locale]/(default)/faq/page.tsx`
- 修复底部联系信息：
  - 标题：`{t('no_answer_found')}`
  - 描述：`{t('contact_description')}`
  - USDA标签：`{t('usda_data')}`
  - StillTasty标签：`{t('stilltasty_content')}`

## 修复效果

### 中文版本 (`/zh/faq`)
- "关键要点："
- "相关食物："
- "查看原文"
- "查看更多FAQ (X 个)"
- "没有找到您要的答案？"
- "我们的FAQ内容来源于权威的USDA数据和StillTasty专业资料..."
- "USDA权威数据"
- "StillTasty专业内容"

### 英文版本 (`/faq`)
- "Key Points:"
- "Related Foods:"
- "View Original"
- "View More FAQs (X more)"
- "Didn't find the answer you're looking for?"
- "Our FAQ content is sourced from authoritative USDA data and StillTasty professional materials..."
- "USDA Authoritative Data"
- "StillTasty Professional Content"

## 移除的内容
- 移除了所有"数据来源: StillTasty.com"的显示，因为用户要求去掉这个信息

## 技术改进
1. **完全国际化**：所有硬编码文本都已替换为翻译键
2. **一致性**：两种语言版本的显示完全一致
3. **可维护性**：未来添加新语言只需要添加翻译文件
4. **用户体验**：消除了中英文夹杂的问题

## 测试建议
1. 访问 `/zh/faq` 检查中文版本
2. 访问 `/faq` 检查英文版本
3. 点击FAQ项目展开查看详情
4. 测试"查看原文"按钮功能
5. 验证底部联系信息显示正确

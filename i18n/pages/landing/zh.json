{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "HowLongFresh", "logo": {"src": "/logo.png", "alt": "HowLongFresh"}, "url": "/zh"}, "nav": {"items": [{"title": "功能特点", "url": "/#feature", "icon": "HiOutlineSparkles"}, {"title": "食品FAQ", "url": "/zh/posts", "icon": "HiOutlineQuestionMarkCircle"}, {"title": "定价", "url": "/#pricing", "icon": "MdPayment"}, {"title": "常见问题", "url": "/#faq", "icon": "HiOutlineQuestionMarkCircle"}]}, "buttons": [], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "了解食物能保鲜多久 — AI 秒速检测食物保质期", "highlight_text": "保鲜多久", "description": "上传照片或输入食物名称，立即获得冷藏、冷冻和常温存储时长建议。", "announcement": {"label": "新功能", "title": "🥬 AI 食物识别", "url": "#"}, "tip": "", "search_section": {"placeholder": "输入食物名称（如：苹果、芒果、牛奶、面包）", "examples": ["苹果", "芒果", "牛奶", "面包", "草莓"], "separator_text": "或者"}, "upload_section": {"title": "上传照片", "accept": "image/*", "max_size": "5MB"}, "trust_stats": [{"title": "食物数据", "value": "2,000+", "icon": "RiDatabase2Line"}, {"title": "覆盖类别", "value": "10", "icon": "RiGridLine"}, {"title": "AI 准确率", "value": "95%+", "icon": "RiAiGenerate"}, {"title": "中英双语", "value": "✓", "icon": "RiTranslate2"}], "mobile_hint": "点击搜索或从上方示例中选择", "buttons": [{"title": "检测保鲜期", "icon": "RiSearchLine", "url": "#", "target": "_self", "variant": "default"}], "show_happy_users": false, "show_badge": false}, "branding": {"title": "HowLongFresh 建立在巨人的肩膀上", "items": [{"title": "Next.js", "image": {"src": "/imgs/logos/nextjs.svg", "alt": "Next.js"}}, {"title": "React", "image": {"src": "/imgs/logos/react.svg", "alt": "React"}}, {"title": "TailwindCSS", "image": {"src": "/imgs/logos/tailwindcss.svg", "alt": "TailwindCSS"}}, {"title": "Shadcn/UI", "image": {"src": "/imgs/logos/shadcn.svg", "alt": "Shadcn/UI"}}, {"title": "Vercel", "image": {"src": "/imgs/logos/vercel.svg", "alt": "Vercel"}}]}, "introduce": {"name": "introduce", "title": "什么是 HowLongFresh", "label": "介绍", "description": "HowLongFresh 是一个AI驱动的食物保鲜指南，帮助您了解食物能保鲜多久，减少食物浪费。", "image": {"src": "/imgs/features/1.png"}, "items": [{"title": "即用型模板", "description": "从数十个生产就绪的 AI SaaS 模板中选择，快速启动您的项目。", "icon": "RiNextjsFill"}, {"title": "基础设施配置", "description": "立即获取内置最佳实践的可扩展基础设施。", "icon": "RiDatabase2Line"}, {"title": "快速部署", "description": "在几小时内将您的 AI SaaS 应用部署到生产环境，而不是几天。", "icon": "RiCloudyFill"}]}, "benefit": {"name": "benefit", "title": "为什么选择 HowLongFresh", "label": "优势", "description": "获得专业的食物保鲜指导 - 从AI智能识别到权威数据支持。", "items": [{"title": "完整框架", "description": "基于 Next.js 构建，集成身份验证、支付和 AI 功能 - 一切开箱即用。", "icon": "RiNextjsFill", "image": {"src": "/imgs/features/2.png"}}, {"title": "丰富的模板库", "description": "选择各种 AI SaaS 模板来启动您的项目 - 聊天机器人、图像生成等。", "icon": "RiClapperboardAiLine", "image": {"src": "/imgs/features/3.png"}}, {"title": "技术指导", "description": "获得专门支持并加入我们的开发者社区，确保您成功启动。", "icon": "RiCodeFill", "image": {"src": "/imgs/features/4.png"}}]}, "usage": {"name": "usage", "title": "如何使用 ShipAny 启动项目", "description": "通过三个简单步骤启动您的 AI SaaS 创业项目：", "image": {"src": "/imgs/features/1.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "获取 ShipAny", "description": "一次性付款购买 ShipAny。查收邮件获取代码和文档。", "image": {"src": "/imgs/features/5.png"}}, {"title": "开始您的项目", "description": "阅读文档并克隆 ShipAny 代码。开始构建您的 AI SaaS 创业项目。", "image": {"src": "/imgs/features/6.png"}}, {"title": "定制您的项目", "description": "使用您的数据和内容修改模板。满足特定的 AI 功能需求。", "image": {"src": "/imgs/features/7.png"}}, {"title": "部署到生产环境", "description": "通过几个步骤将项目部署到生产环境，立即开始服务客户。", "image": {"src": "/imgs/features/8.png"}}]}, "feature": {"name": "feature", "title": "HowLongFresh 核心功能", "description": "一站式食物保鲜和保质期查询平台，让您的食物保存更科学。", "items": [{"title": "AI 智能识别", "description": "拍照上传即可智能识别食物，获得专业的储存建议和保质期信息。", "icon": "RiCameraLine"}, {"title": "权威数据支持", "description": "基于 USDA FoodKeeper 和 StillTasty 数据库，提供准确可靠的食物储存指南。", "icon": "RiShieldCheckLine"}, {"title": "全面分类浏览", "description": "按水果、蔬菜、肉类等10大类别浏览，覆盖2000+种常见食物。", "icon": "RiGridLine"}, {"title": "中英双语支持", "description": "提供中英文双语界面，为华语用户和英语用户提供本地化的食物保鲜知识。", "icon": "RiTranslate2"}]}, "showcase": null, "showcase_old": {"name": "showcase", "title": "使用 ShipAny 构建的 AI SaaS 创业项目", "description": "易于使用，快速发布。", "items": [{"title": "ThinkAny", "description": "AI 搜索引擎", "url": "https://thinkany.ai", "target": "_blank", "image": {"src": "/imgs/showcases/7.png"}}, {"title": "HeyBeauty", "description": "AI 虚拟试妆", "url": "https://heybeauty.ai", "target": "_blank", "image": {"src": "/imgs/showcases/5.png"}}, {"title": "AI Wallpaper", "description": "AI 壁纸生成器", "url": "https://aiwallpaper.shop", "target": "_blank", "image": {"src": "/imgs/showcases/1.png"}}, {"title": "AI Cover", "description": "AI 封面生成器", "url": "https://aicover.design", "target": "_blank", "image": {"src": "/imgs/showcases/2.png"}}, {"title": "GPTs Works", "description": "GPTs 目录", "url": "https://gpts.works", "target": "_blank", "image": {"src": "/imgs/showcases/3.png"}}, {"title": "Melod<PERSON>", "description": "AI 音乐播放器", "url": "https://melodis.co", "target": "_blank", "image": {"src": "/imgs/showcases/4.png"}}, {"title": "<PERSON><PERSON>", "description": "AI 落地页生成器", "url": "https://pagen.so", "target": "_blank", "image": {"src": "/imgs/showcases/6.png"}}, {"title": "SoraFM", "description": "AI 视频生成器", "url": "https://sorafm.trys.ai", "target": "_blank", "image": {"src": "/imgs/showcases/8.png"}}, {"title": "PodLM", "description": "AI 播客生成器", "url": "https://podlm.ai", "target": "_blank", "image": {"src": "/imgs/showcases/9.png"}}]}, "stats": {"name": "stats", "label": "统计", "title": "用户喜爱 ShipAny", "description": "因为它易于使用且快速发布。", "icon": "FaRegHeart", "items": [{"title": "信任", "label": "99+", "description": "客户"}, {"title": "内置", "label": "20+", "description": "组件"}, {"title": "快速发布", "label": "5", "description": "分钟"}]}, "pricing": {"name": "pricing", "label": "定价", "title": "选择适合您的方案", "description": "HowLongFresh 提供灵活的定价方案，满足不同用户的需求。", "groups": [], "items": [{"title": "免费版", "description": "适合偶尔使用的个人用户", "features_title": "包含", "features": ["7天内5次查询", "完整食物数据库", "储存提示和建议", "移动端支持"], "interval": "month", "amount": 0, "cn_amount": 0, "currency": "USD", "price": "免费", "unit": "", "is_featured": false, "tip": "每7天自动更新", "button": {"title": "开始使用", "url": "/", "icon": "RiFlashlightFill"}, "product_id": "free", "product_name": "HowLongFresh 免费版", "credits": 5, "valid_months": 1}, {"title": "专业版", "description": "适合家庭和小型企业", "features_title": "包含", "features": ["500次查询（3个月总计）", "完整食物数据库", "储存提示和建议", "移动端支持", "优先客户支持"], "interval": "one-time", "amount": 449, "cn_amount": 30, "currency": "USD", "price": "$4.49", "unit": "/3个月", "is_featured": true, "tip": "最受欢迎", "button": {"title": "选择专业版", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "pro_quarterly", "product_name": "HowLongFresh 专业版", "credits": 500, "valid_months": 3}, {"title": "企业版", "label": "最超值", "description": "适合企业和餐厅", "features_title": "包含专业版所有功能，另加", "features": ["每年1500次查询", "完整食物数据库", "储存提示和建议", "移动端支持", "优先客户支持"], "interval": "year", "amount": 1299, "cn_amount": 89, "currency": "USD", "price": "$12.99", "original_price": "$19.96", "unit": "/年", "is_featured": false, "tip": "相比专业版节省35%", "button": {"title": "选择企业版", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "enterprise_yearly", "product_name": "HowLongFresh 企业版", "credits": 1500, "valid_months": 12}]}, "testimonial": {"name": "testimonial", "label": "用户评价", "title": "用户如何评价 ShipAny", "description": "听听使用 ShipAny 启动 AI 创业项目的开发者和创始人怎么说。", "icon": "GoThumbsup", "items": [{"title": "陈大卫", "label": "AIWallpaper.shop 创始人", "description": "ShipAny 为我们节省了数月的开发时间。我们仅用 2 天就启动了 AI 壁纸业务，一周内就获得了第一个付费客户！", "image": {"src": "/imgs/users/1.png"}}, {"title": "金瑞秋", "label": "HeyBeauty.ai 技术总监", "description": "预构建的 AI 基础设施是一个游戏规则改变者。我们无需担心架构 - 只需专注于 AI 美容技术并快速上线。", "image": {"src": "/imgs/users/2.png"}}, {"title": "马库斯", "label": "独立开发者", "description": "作为独立开发者，ShipAny 给了我所需的一切 - 身份验证、支付、AI 集成和漂亮的 UI。一个周末就启动了我的 SaaS！", "image": {"src": "/imgs/users/3.png"}}, {"title": "索菲亚", "label": "Melodisco CEO", "description": "这些模板可直接用于生产且高度可定制。我们用几小时而不是几个月就建立了 AI 音乐平台。上市时间令人难以置信！", "image": {"src": "/imgs/users/4.png"}}, {"title": "詹姆斯", "label": "GPTs.works 技术主管", "description": "ShipAny 的基础设施非常稳固。我们从 0 扩展到 1 万用户都没碰后端。这是我们 AI 创业最好的投资。", "image": {"src": "/imgs/users/5.png"}}, {"title": "张安娜", "label": "创业者", "description": "从想法到上线只用了 3 天！ShipAny 的模板和部署工具让我们能够以令人难以置信的速度测试 AI 业务概念。", "image": {"src": "/imgs/users/6.png"}}]}, "feedback_cta": {"title": "有建议或反馈吗？", "description": "欢迎向我们提出建议，帮助我们改进产品！您的想法将帮助 HowLongFresh 变得更好。", "button_text": "分享您的想法"}, "faq": {"name": "faq", "label": "常见问题", "title": "关于 HowLongFresh 的常见问题", "description": "还有其他问题？请通过邮件联系我们。", "items": [{"title": "数据准确度如何？", "description": "我们的数据来自美国农业部 USDA FoodKeeper 和 StillTasty 权威数据库，AI 识别准确率达 95% 以上，确保为您提供最可靠的食物储存建议。"}, {"title": "如何使用 AI 识别功能？", "description": "只需拍照上传食物图片，AI 会自动识别并提供该食物在冷藏、冷冻、常温条件下的保存时间和储存建议。"}, {"title": "支持哪些食物类别？", "description": "覆盖水果、蔬菜、肉类、海鲜、乳制品等10大类别，包含2000+种常见食物，满足日常生活的各种需求。"}, {"title": "是否需要付费？", "description": "完全免费！无需注册，无隐藏费用。我们相信每个人都应该获得帮助减少食物浪费的工具。"}]}, "footer": {"name": "footer", "brand": {"title": "HowLongFresh", "description": "专业的食物保鲜和保质期查询平台，基于权威数据库，帮助您减少食物浪费。", "logo": {"src": "/logo.png", "alt": "HowLongFresh"}, "url": "/"}, "copyright": "© 2025 • HowLongFresh 保留所有权利。", "nav": {"items": [{"title": "关于", "children": [{"title": "功能特点", "url": "/#feature", "target": "_self"}, {"title": "案例展示", "url": "/#showcase", "target": "_self"}, {"title": "定价", "url": "/#pricing", "target": "_self"}]}, {"title": "资源", "children": [{"title": "食品FAQ", "url": "/zh/posts", "target": "_self"}, {"title": "隐私政策", "url": "/privacy-policy", "target": "_self"}, {"title": "服务条款", "url": "/terms-of-service", "target": "_self"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/howlongfresh", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/howlongfresh", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "邮箱", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": []}}, "pages": {"landing": {"pricing": {"items": [{"title": "免费版", "description": "适合尝试我们的服务", "features_title": "包含功能", "features": ["7天内 5 次查询", "完整食物数据库", "储存建议和推荐", "移动端支持"], "interval": "month", "amount": 0, "cn_amount": 0, "currency": "USD", "price": "免费", "unit": "", "is_featured": false, "tip": "每7天更新", "button": {"title": "开始使用", "url": "/", "icon": "RiFlashlightFill"}, "product_id": "free", "product_name": "HowLongFresh 免费版", "credits": 5, "valid_months": 1}, {"title": "专业版", "label": "热门", "description": "适合家庭和小企业用户", "features_title": "包含功能", "features": ["500 次查询（3个月总计）", "完整食物数据库", "储存建议和推荐", "移动端支持", "优先客服支持"], "interval": "one-time", "amount": 499, "cn_amount": 3500, "currency": "USD", "price": "$4.99", "unit": "/3个月", "is_featured": true, "tip": "最受欢迎的选择", "button": {"title": "选择专业版", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "pro_monthly", "product_name": "HowLongFresh 专业版", "credits": 500, "valid_months": 3}, {"title": "企业版", "label": "最超值", "description": "适合企业和餐厅使用", "features_title": "包含专业版所有功能，另加", "features": ["每年 1500 次查询", "完整食物数据库", "储存建议和推荐", "移动端支持", "优先客服支持"], "interval": "year", "amount": 1299, "cn_amount": 8900, "currency": "USD", "price": "$12.99", "original_price": "¥140", "unit": "/年", "is_featured": false, "tip": "相比专业版节省35%", "button": {"title": "选择企业版", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "enterprise_yearly", "product_name": "HowLongFresh 企业版", "credits": 1500, "valid_months": 12}]}}}}
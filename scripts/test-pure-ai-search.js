#!/usr/bin/env node
/**
 * 纯AI查询功能测试脚本
 * 测试数据库中不存在的食物的AI查询效果
 */

const API_BASE_URL = 'http://localhost:3001';

// 测试查询列表 - 这些食物在USDA数据库中不存在
const testQueries = [
  // 中文食物
  { query: 'cake', language: 'en', category: '烘焙食品' },
  { query: '蛋糕', language: 'zh', category: '烘焙食品' },
  { query: '火龙果', language: 'zh', category: '水果' },
  { query: '巧克力', language: 'zh', category: '糖果' },
  { query: '泡菜', language: 'zh', category: '腌制食品' },
  { query: '寿司', language: 'zh', category: '日式料理' },
  
  // 英文食物
  { query: 'chocolate', language: 'en', category: '糖果' },
  { query: 'sushi', language: 'en', category: '日式料理' },
  { query: 'pizza', language: 'en', category: '意式料理' },
  { query: 'ice cream', language: 'en', category: '冷冻甜品' },
  
  // 特殊食物
  { query: '榴莲', language: 'zh', category: '水果' },
  { query: '燕窝', language: 'zh', category: '滋补品' },
  { query: '咖啡豆', language: 'zh', category: '饮品原料' },
  { query: '红酒', language: 'zh', category: '酒类' }
];

/**
 * 测试单个查询
 */
async function testQuery(query, language, expectedCategory) {
  try {
    console.log(`\n🔍 测试纯AI查询: "${query}" (${language})`);
    
    const startTime = Date.now();
    const response = await fetch(`${API_BASE_URL}/api/food/identify-text`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ foodName: query })
    });
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (response.ok) {
      const data = await response.json();
      
      // 分析AI结果质量
      const hasChineseName = /[\u4e00-\u9fff]/.test(data.name);
      const hasChineseCategory = /[\u4e00-\u9fff]/.test(data.category);
      const hasDetailedTips = data.tips && data.tips.length >= 3;
      const hasReasonableStorage = data.storage && 
        (data.storage.refrigerated > 0 || data.storage.frozen > 0 || data.storage.room_temperature > 0);
      
      // 置信度检查（纯AI查询应该较低）
      const hasAppropriateConfidence = data.confidence <= 0.8;
      
      console.log(`✅ 查询成功 (${responseTime}ms)`);
      console.log(`   食物名称: ${data.name} ${hasChineseName ? '✅' : '❌'}`);
      console.log(`   类别: ${data.category} ${hasChineseCategory ? '✅' : '❌'}`);
      console.log(`   置信度: ${(data.confidence * 100).toFixed(1)}% ${hasAppropriateConfidence ? '✅' : '❌'}`);
      
      // 显示存储信息
      const storage = [];
      if (data.storage.refrigerated > 0) storage.push(`冷藏: ${data.storage.refrigerated}天`);
      if (data.storage.frozen > 0) storage.push(`冷冻: ${data.storage.frozen}天`);
      if (data.storage.room_temperature > 0) storage.push(`常温: ${data.storage.room_temperature}天`);
      console.log(`   存储时间: ${storage.join(', ')} ${hasReasonableStorage ? '✅' : '❌'}`);
      
      // 显示保鲜建议
      console.log(`   保鲜建议: ${data.tips.length}条 ${hasDetailedTips ? '✅' : '❌'}`);
      data.tips.forEach((tip, index) => {
        console.log(`     ${index + 1}. ${tip.substring(0, 40)}${tip.length > 40 ? '...' : ''}`);
      });
      
      // 质量评分
      const qualityScore = [
        hasChineseName,
        hasChineseCategory, 
        hasDetailedTips,
        hasReasonableStorage,
        hasAppropriateConfidence
      ].filter(Boolean).length;
      
      console.log(`   🎯 质量评分: ${qualityScore}/5 ${qualityScore >= 4 ? '🌟' : qualityScore >= 3 ? '👍' : '⚠️'}`);
      
      return {
        query,
        language,
        success: true,
        responseTime,
        qualityScore,
        data,
        hasChineseName,
        hasChineseCategory,
        hasDetailedTips,
        hasReasonableStorage,
        hasAppropriateConfidence
      };
      
    } else {
      const errorData = await response.json();
      console.log(`❌ 查询失败: ${errorData.error || errorData.message}`);
      
      return {
        query,
        language,
        success: false,
        responseTime,
        qualityScore: 0,
        error: errorData.error || errorData.message
      };
    }
    
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
    return {
      query,
      language,
      success: false,
      responseTime: 0,
      qualityScore: 0,
      error: error.message
    };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🤖 纯AI查询功能测试');
  console.log('=' * 60);
  console.log(`API 地址: ${API_BASE_URL}`);
  console.log(`测试时间: ${new Date().toLocaleString()}`);
  console.log(`测试说明: 这些食物在USDA数据库中不存在，完全依赖AI知识库\n`);
  
  const results = [];
  let totalResponseTime = 0;
  let totalQualityScore = 0;
  
  for (const test of testQueries) {
    const result = await testQuery(test.query, test.language, test.category);
    results.push(result);
    totalResponseTime += result.responseTime;
    totalQualityScore += result.qualityScore;
    
    // 避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 统计分析
  const successCount = results.filter(r => r.success).length;
  const avgResponseTime = totalResponseTime / results.length;
  const avgQualityScore = totalQualityScore / results.length;
  
  console.log('\n📊 测试总结');
  console.log('=' * 50);
  console.log(`总测试数: ${testQueries.length}`);
  console.log(`查询成功: ${successCount} (${((successCount / testQueries.length) * 100).toFixed(1)}%)`);
  console.log(`平均响应时间: ${avgResponseTime.toFixed(0)}ms`);
  console.log(`平均质量评分: ${avgQualityScore.toFixed(1)}/5`);
  
  // 按语言分组统计
  const zhResults = results.filter(r => r.language === 'zh');
  const enResults = results.filter(r => r.language === 'en');
  
  console.log(`\n中文查询:`);
  console.log(`  成功率: ${((zhResults.filter(r => r.success).length / zhResults.length) * 100).toFixed(1)}%`);
  console.log(`  平均质量: ${(zhResults.reduce((sum, r) => sum + r.qualityScore, 0) / zhResults.length).toFixed(1)}/5`);
  
  console.log(`英文查询:`);
  console.log(`  成功率: ${((enResults.filter(r => r.success).length / enResults.length) * 100).toFixed(1)}%`);
  console.log(`  平均质量: ${(enResults.reduce((sum, r) => sum + r.qualityScore, 0) / enResults.length).toFixed(1)}/5`);
  
  // 质量分析
  const successfulResults = results.filter(r => r.success);
  if (successfulResults.length > 0) {
    console.log('\n🎯 质量分析:');
    console.log(`中文名称优化: ${((successfulResults.filter(r => r.hasChineseName).length / successfulResults.length) * 100).toFixed(1)}%`);
    console.log(`中文类别: ${((successfulResults.filter(r => r.hasChineseCategory).length / successfulResults.length) * 100).toFixed(1)}%`);
    console.log(`详细建议: ${((successfulResults.filter(r => r.hasDetailedTips).length / successfulResults.length) * 100).toFixed(1)}%`);
    console.log(`合理存储: ${((successfulResults.filter(r => r.hasReasonableStorage).length / successfulResults.length) * 100).toFixed(1)}%`);
    console.log(`适当置信度: ${((successfulResults.filter(r => r.hasAppropriateConfidence).length / successfulResults.length) * 100).toFixed(1)}%`);
  }
  
  // 显示失败的测试
  const failedTests = results.filter(r => !r.success);
  if (failedTests.length > 0) {
    console.log('\n❌ 失败的测试:');
    failedTests.forEach(test => {
      console.log(`- "${test.query}" (${test.language}): ${test.error}`);
    });
  }
  
  // 显示高质量结果
  const highQualityResults = results.filter(r => r.qualityScore >= 4);
  if (highQualityResults.length > 0) {
    console.log('\n🌟 高质量结果:');
    highQualityResults.forEach(result => {
      console.log(`- "${result.query}": ${result.data.name} (${result.qualityScore}/5)`);
    });
  }
  
  // 性能评估
  console.log('\n⚡ 性能评估:');
  if (avgResponseTime < 3000) {
    console.log('✅ 响应速度优秀 (< 3秒)');
  } else if (avgResponseTime < 6000) {
    console.log('⚠️ 响应速度一般 (3-6秒)');
  } else {
    console.log('❌ 响应速度较慢 (> 6秒)');
  }
  
  // 总体评估
  const overallScore = (successCount * 0.4 + avgQualityScore * 20 * 0.6);
  console.log(`\n🎯 总体评分: ${overallScore.toFixed(1)}/100`);
  
  if (overallScore >= 80) {
    console.log('🎉 纯AI查询功能表现优秀！');
  } else if (overallScore >= 60) {
    console.log('👍 纯AI查询功能表现良好，有改进空间');
  } else {
    console.log('⚠️ 纯AI查询功能需要优化');
  }
  
  return results;
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

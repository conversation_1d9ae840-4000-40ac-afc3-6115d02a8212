#!/usr/bin/env python3
"""
EatByDate数据处理脚本

将爬取的EatByDate数据转换为项目数据库可用的格式
"""

import json
import csv
import re
import os
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EatByDateDataProcessor:
    def __init__(self):
        self.processed_data = []
        
    def parse_duration(self, duration_str):
        """解析时间字符串，转换为标准格式"""
        if not duration_str or duration_str.strip() == '':
            return None
            
        duration_str = duration_str.lower().strip()
        
        # 常见的时间模式
        patterns = [
            # "5-7 days", "3-6 months"
            r'(\d+)\s*-\s*(\d+)\s*(days?|weeks?|months?|years?)',
            # "5 to 7 days", "3 to 6 months"  
            r'(\d+)\s*to\s*(\d+)\s*(days?|weeks?|months?|years?)',
            # "5 days", "3 months"
            r'(\d+)\s*(days?|weeks?|months?|years?)',
            # "several days", "a few weeks"
            r'(several|few|couple)\s*(days?|weeks?|months?|years?)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, duration_str)
            if match:
                groups = match.groups()
                
                if len(groups) == 3 and groups[0].isdigit():
                    # 范围格式: "5-7 days"
                    min_val, max_val, unit = groups
                    return f"{min_val}-{max_val} {unit}"
                elif len(groups) == 2:
                    if groups[0].isdigit():
                        # 单一值: "5 days"
                        val, unit = groups
                        return f"{val} {unit}"
                    else:
                        # 模糊描述: "several days"
                        desc, unit = groups
                        if desc in ['several', 'few']:
                            return f"2-3 {unit}"
                        elif desc == 'couple':
                            return f"2 {unit}"
        
        # 如果无法解析，返回原始字符串
        return duration_str
    
    def normalize_food_name(self, name):
        """标准化食物名称"""
        if not name:
            return ""
            
        # 移除常见的前缀和后缀
        name = re.sub(r'^how long does?\s*', '', name, flags=re.IGNORECASE)
        name = re.sub(r'\s*last\??$', '', name, flags=re.IGNORECASE)
        name = re.sub(r'\s*keep\??$', '', name, flags=re.IGNORECASE)
        name = re.sub(r'\s*stay fresh\??$', '', name, flags=re.IGNORECASE)
        
        # 首字母大写
        name = name.strip().title()
        
        return name
    
    def categorize_food(self, category, name):
        """将食物分类映射到项目的分类系统"""
        category_mapping = {
            "奶制品": "dairy",
            "饮料": "beverages", 
            "水果": "fruits",
            "谷物": "grains",
            "其他": "other",
            "蛋白质": "proteins",
            "蔬菜": "vegetables"
        }
        
        return category_mapping.get(category, "other")
    
    def process_single_item(self, item):
        """处理单个食物数据项"""
        try:
            processed_item = {
                "name": self.normalize_food_name(item.get('name', '')),
                "category": self.categorize_food(item.get('category', ''), item.get('name', '')),
                "category_zh": item.get('category', ''),
                "storage_conditions": {
                    "room_temperature": {
                        "duration": self.parse_duration(item.get('pantry_life', '')),
                        "raw": item.get('pantry_life', '')
                    },
                    "refrigerated": {
                        "duration": self.parse_duration(item.get('refrigerator_life', '')),
                        "raw": item.get('refrigerator_life', '')
                    },
                    "frozen": {
                        "duration": self.parse_duration(item.get('freezer_life', '')),
                        "raw": item.get('freezer_life', '')
                    }
                },
                "description": item.get('description', '')[:500],  # 限制描述长度
                "source": {
                    "name": "EatByDate.com",
                    "url": item.get('url', ''),
                    "scraped_at": item.get('scraped_at', ''),
                    "confidence": "medium"  # EatByDate数据质量中等
                },
                "processed_at": datetime.now().isoformat()
            }
            
            return processed_item
            
        except Exception as e:
            logger.error(f"Error processing item {item.get('name', 'unknown')}: {e}")
            return None
    
    def process_data(self, input_file):
        """处理整个数据文件"""
        logger.info(f"Processing data from {input_file}")
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            logger.info(f"Loaded {len(raw_data)} raw items")
            
            for item in raw_data:
                processed_item = self.process_single_item(item)
                if processed_item:
                    self.processed_data.append(processed_item)
            
            logger.info(f"Successfully processed {len(self.processed_data)} items")
            
        except Exception as e:
            logger.error(f"Error processing data: {e}")
            raise
    
    def save_processed_data(self, output_dir="processed_data"):
        """保存处理后的数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存为JSON格式（用于项目导入）
        json_file = os.path.join(output_dir, f"processed_eatbydate_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.processed_data, f, ensure_ascii=False, indent=2)
        
        # 保存为扁平化的CSV格式（用于数据分析）
        csv_file = os.path.join(output_dir, f"processed_eatbydate_data_{timestamp}.csv")
        self.save_as_csv(csv_file)
        
        # 生成数据统计报告
        stats_file = os.path.join(output_dir, f"eatbydate_data_stats_{timestamp}.txt")
        self.generate_stats_report(stats_file)
        
        logger.info(f"Processed data saved to:")
        logger.info(f"  JSON: {json_file}")
        logger.info(f"  CSV: {csv_file}")
        logger.info(f"  Stats: {stats_file}")
    
    def save_as_csv(self, csv_file):
        """保存为CSV格式"""
        if not self.processed_data:
            return
            
        fieldnames = [
            'name', 'category', 'category_zh',
            'room_temp_duration', 'refrigerated_duration', 'frozen_duration',
            'description', 'source_name', 'source_url', 'confidence',
            'scraped_at', 'processed_at'
        ]
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for item in self.processed_data:
                row = {
                    'name': item['name'],
                    'category': item['category'],
                    'category_zh': item['category_zh'],
                    'room_temp_duration': item['storage_conditions']['room_temperature']['duration'],
                    'refrigerated_duration': item['storage_conditions']['refrigerated']['duration'],
                    'frozen_duration': item['storage_conditions']['frozen']['duration'],
                    'description': item['description'],
                    'source_name': item['source']['name'],
                    'source_url': item['source']['url'],
                    'confidence': item['source']['confidence'],
                    'scraped_at': item['source']['scraped_at'],
                    'processed_at': item['processed_at']
                }
                writer.writerow(row)
    
    def generate_stats_report(self, stats_file):
        """生成数据统计报告"""
        if not self.processed_data:
            return
            
        stats = {
            'total_items': len(self.processed_data),
            'categories': {},
            'storage_coverage': {
                'room_temperature': 0,
                'refrigerated': 0,
                'frozen': 0
            }
        }
        
        for item in self.processed_data:
            # 统计分类
            category = item['category_zh']
            stats['categories'][category] = stats['categories'].get(category, 0) + 1
            
            # 统计存储条件覆盖率
            storage = item['storage_conditions']
            if storage['room_temperature']['duration']:
                stats['storage_coverage']['room_temperature'] += 1
            if storage['refrigerated']['duration']:
                stats['storage_coverage']['refrigerated'] += 1
            if storage['frozen']['duration']:
                stats['storage_coverage']['frozen'] += 1
        
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write("EatByDate数据处理统计报告\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"总计食物项目: {stats['total_items']}\n\n")
            
            f.write("分类统计:\n")
            for category, count in stats['categories'].items():
                f.write(f"  {category}: {count}\n")
            
            f.write("\n存储条件覆盖率:\n")
            for condition, count in stats['storage_coverage'].items():
                percentage = (count / stats['total_items']) * 100 if stats['total_items'] > 0 else 0
                f.write(f"  {condition}: {count}/{stats['total_items']} ({percentage:.1f}%)\n")
            
            f.write(f"\n处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python process_eatbydate_data.py <input_json_file>")
        print("Example: python process_eatbydate_data.py data/eatbydate_data_20240115_143000.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    if not os.path.exists(input_file):
        print(f"Error: Input file {input_file} not found")
        sys.exit(1)
    
    processor = EatByDateDataProcessor()
    
    try:
        processor.process_data(input_file)
        processor.save_processed_data()
        print(f"✅ 数据处理完成！处理了 {len(processor.processed_data)} 个食物项目")
    except Exception as e:
        print(f"❌ 数据处理失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

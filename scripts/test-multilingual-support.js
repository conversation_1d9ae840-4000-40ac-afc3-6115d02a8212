#!/usr/bin/env node
/**
 * 多语言支持测试脚本
 * 验证英文查询返回英文结果，中文查询返回中文结果
 */

const API_BASE_URL = 'http://localhost:3001';

// 测试查询对照表
const testPairs = [
  // USDA数据库中存在的食物
  { 
    en: { query: 'apple', expectedName: 'Apple', expectedCategory: 'Produce' },
    zh: { query: '苹果', expectedName: '苹果', expectedCategory: '水果' }
  },
  { 
    en: { query: 'banana', expectedName: 'Banana', expectedCategory: 'Produce' },
    zh: { query: '香蕉', expectedName: '香蕉', expectedCategory: '水果' }
  },
  { 
    en: { query: 'chicken', expectedName: 'Chicken', expectedCategory: 'Poultry' },
    zh: { query: '鸡肉', expectedName: '鸡肉', expectedCategory: '禽类' }
  },
  
  // 数据库中不存在的食物（纯AI查询）
  { 
    en: { query: 'cake', expectedName: 'Cake', expectedCategory: 'Baked Goods' },
    zh: { query: '蛋糕', expectedName: '蛋糕', expectedCategory: '烘焙食品' }
  },
  { 
    en: { query: 'chocolate', expectedName: 'Chocolate', expectedCategory: 'Candy' },
    zh: { query: '巧克力', expectedName: '巧克力', expectedCategory: '糖果' }
  },
  { 
    en: { query: 'pizza', expectedName: 'Pizza', expectedCategory: 'Prepared Food' },
    zh: { query: '披萨', expectedName: '披萨', expectedCategory: '熟食' }
  }
];

/**
 * 检测文本是否包含中文
 */
function containsChinese(text) {
  return /[\u4e00-\u9fff]/.test(text);
}

/**
 * 检测文本是否主要是英文
 */
function isEnglish(text) {
  return /^[a-zA-Z\s\-&,.'()]+$/.test(text.trim());
}

/**
 * 测试单个查询
 */
async function testQuery(query, expectedLang, expectedName, expectedCategory) {
  try {
    console.log(`\n🔍 测试查询: "${query}" (期望语言: ${expectedLang})`);
    
    const startTime = Date.now();
    const response = await fetch(`${API_BASE_URL}/api/food/identify-text`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ foodName: query })
    });
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (response.ok) {
      const data = await response.json();
      
      // 语言检测
      const nameLanguage = containsChinese(data.name) ? 'zh' : 'en';
      const categoryLanguage = containsChinese(data.category) ? 'zh' : 'en';
      const tipsLanguage = data.tips.some(tip => containsChinese(tip)) ? 'zh' : 'en';
      
      // 语言一致性检查
      const languageConsistent = nameLanguage === categoryLanguage && categoryLanguage === tipsLanguage;
      const correctLanguage = nameLanguage === expectedLang;
      
      // 内容质量检查
      const nameMatch = data.name.toLowerCase().includes(expectedName.toLowerCase()) || 
                       expectedName.toLowerCase().includes(data.name.toLowerCase());
      
      console.log(`✅ 查询成功 (${responseTime}ms)`);
      console.log(`   食物名称: "${data.name}" (${nameLanguage}) ${correctLanguage ? '✅' : '❌'}`);
      console.log(`   类别: "${data.category}" (${categoryLanguage}) ${correctLanguage ? '✅' : '❌'}`);
      console.log(`   置信度: ${(data.confidence * 100).toFixed(1)}%`);
      
      // 显示存储信息
      const storage = [];
      if (data.storage.refrigerated > 0) storage.push(`冷藏: ${data.storage.refrigerated}天`);
      if (data.storage.frozen > 0) storage.push(`冷冻: ${data.storage.frozen}天`);
      if (data.storage.room_temperature > 0) storage.push(`常温: ${data.storage.room_temperature}天`);
      console.log(`   存储时间: ${storage.join(', ')}`);
      
      // 显示保鲜建议语言
      console.log(`   保鲜建议: ${data.tips.length}条 (${tipsLanguage}) ${correctLanguage ? '✅' : '❌'}`);
      data.tips.slice(0, 2).forEach((tip, index) => {
        console.log(`     ${index + 1}. ${tip.substring(0, 50)}${tip.length > 50 ? '...' : ''}`);
      });
      
      // 评分
      const scores = {
        correctLanguage: correctLanguage ? 1 : 0,
        languageConsistent: languageConsistent ? 1 : 0,
        nameMatch: nameMatch ? 1 : 0,
        hasStorage: (data.storage.refrigerated > 0 || data.storage.frozen > 0 || data.storage.room_temperature > 0) ? 1 : 0,
        hasTips: data.tips.length >= 2 ? 1 : 0
      };
      
      const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
      console.log(`   🎯 评分: ${totalScore}/5 ${totalScore >= 4 ? '🌟' : totalScore >= 3 ? '👍' : '⚠️'}`);
      
      return {
        query,
        expectedLang,
        success: true,
        responseTime,
        actualLang: nameLanguage,
        correctLanguage,
        languageConsistent,
        nameMatch,
        totalScore,
        data
      };
      
    } else {
      const errorData = await response.json();
      console.log(`❌ 查询失败: ${errorData.error || errorData.message}`);
      
      return {
        query,
        expectedLang,
        success: false,
        responseTime,
        error: errorData.error || errorData.message
      };
    }
    
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
    return {
      query,
      expectedLang,
      success: false,
      responseTime: 0,
      error: error.message
    };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🌐 多语言支持测试');
  console.log('=' * 60);
  console.log(`API 地址: ${API_BASE_URL}`);
  console.log(`测试时间: ${new Date().toLocaleString()}`);
  console.log(`测试说明: 验证英文查询返回英文结果，中文查询返回中文结果\n`);
  
  const results = [];
  let totalResponseTime = 0;
  
  for (const pair of testPairs) {
    // 测试英文查询
    const enResult = await testQuery(
      pair.en.query, 
      'en', 
      pair.en.expectedName, 
      pair.en.expectedCategory
    );
    results.push(enResult);
    totalResponseTime += enResult.responseTime;
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试中文查询
    const zhResult = await testQuery(
      pair.zh.query, 
      'zh', 
      pair.zh.expectedName, 
      pair.zh.expectedCategory
    );
    results.push(zhResult);
    totalResponseTime += zhResult.responseTime;
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 统计分析
  const successCount = results.filter(r => r.success).length;
  const correctLanguageCount = results.filter(r => r.correctLanguage).length;
  const avgResponseTime = totalResponseTime / results.length;
  const avgScore = results.filter(r => r.success).reduce((sum, r) => sum + r.totalScore, 0) / successCount;
  
  console.log('\n📊 测试总结');
  console.log('=' * 50);
  console.log(`总测试数: ${results.length}`);
  console.log(`查询成功: ${successCount} (${((successCount / results.length) * 100).toFixed(1)}%)`);
  console.log(`语言正确: ${correctLanguageCount} (${((correctLanguageCount / results.length) * 100).toFixed(1)}%)`);
  console.log(`平均响应时间: ${avgResponseTime.toFixed(0)}ms`);
  console.log(`平均评分: ${avgScore.toFixed(1)}/5`);
  
  // 按语言分组统计
  const enResults = results.filter(r => r.expectedLang === 'en');
  const zhResults = results.filter(r => r.expectedLang === 'zh');
  
  console.log(`\n英文查询:`);
  console.log(`  成功率: ${((enResults.filter(r => r.success).length / enResults.length) * 100).toFixed(1)}%`);
  console.log(`  语言正确率: ${((enResults.filter(r => r.correctLanguage).length / enResults.length) * 100).toFixed(1)}%`);
  console.log(`  平均评分: ${(enResults.filter(r => r.success).reduce((sum, r) => sum + r.totalScore, 0) / enResults.filter(r => r.success).length).toFixed(1)}/5`);
  
  console.log(`中文查询:`);
  console.log(`  成功率: ${((zhResults.filter(r => r.success).length / zhResults.length) * 100).toFixed(1)}%`);
  console.log(`  语言正确率: ${((zhResults.filter(r => r.correctLanguage).length / zhResults.length) * 100).toFixed(1)}%`);
  console.log(`  平均评分: ${(zhResults.filter(r => r.success).reduce((sum, r) => sum + r.totalScore, 0) / zhResults.filter(r => r.success).length).toFixed(1)}/5`);
  
  // 显示问题
  const languageIssues = results.filter(r => r.success && !r.correctLanguage);
  if (languageIssues.length > 0) {
    console.log('\n⚠️ 语言问题:');
    languageIssues.forEach(issue => {
      console.log(`- "${issue.query}" (期望${issue.expectedLang}，实际${issue.actualLang})`);
    });
  }
  
  const failedTests = results.filter(r => !r.success);
  if (failedTests.length > 0) {
    console.log('\n❌ 失败的测试:');
    failedTests.forEach(test => {
      console.log(`- "${test.query}" (${test.expectedLang}): ${test.error}`);
    });
  }
  
  // 总体评估
  const languageAccuracy = (correctLanguageCount / results.length) * 100;
  console.log(`\n🎯 多语言支持评估:`);
  
  if (languageAccuracy >= 95) {
    console.log('🎉 多语言支持优秀！');
  } else if (languageAccuracy >= 80) {
    console.log('👍 多语言支持良好，有小幅改进空间');
  } else {
    console.log('⚠️ 多语言支持需要改进');
  }
  
  console.log(`语言准确率: ${languageAccuracy.toFixed(1)}%`);
  
  return results;
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

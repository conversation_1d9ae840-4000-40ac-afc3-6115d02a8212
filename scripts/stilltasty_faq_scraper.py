#!/usr/bin/env python3
"""
StillTasty.com FAQ页面爬虫
爬取 https://stilltasty.com/Questions 页面的所有FAQ内容
"""

import os
import json
import csv
import time
import logging
import asyncio
import re
from datetime import datetime
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stilltasty_faq_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StillTastyFAQScraper:
    def __init__(self):
        """初始化FAQ爬虫"""
        self.base_url = "https://stilltasty.com"
        self.faq_base_url = "https://stilltasty.com/questions"
        self.scraped_data = []
        
        # FAQ分类映射
        self.faq_categories = {
            "frozen_foods": {"name_zh": "冷冻食品", "icon": "❄️"},
            "refrigerated_foods": {"name_zh": "冷藏食品", "icon": "🧊"},
            "room_temperature": {"name_zh": "常温保存", "icon": "🌡️"},
            "food_safety": {"name_zh": "食品安全", "icon": "🛡️"},
            "storage_tips": {"name_zh": "保存技巧", "icon": "💡"},
            "expiration_dates": {"name_zh": "保质期", "icon": "📅"},
            "preparation": {"name_zh": "食品处理", "icon": "👨‍🍳"},
            "general": {"name_zh": "一般问题", "icon": "❓"}
        }
        
        # 食物关键词映射，用于关联FAQ和食物
        self.food_keywords = [
            "turkey", "chicken", "beef", "pork", "fish", "seafood", "eggs", "dairy",
            "milk", "cheese", "yogurt", "butter", "fruits", "vegetables", "bread",
            "rice", "pasta", "potatoes", "bananas", "avocado", "grapes", "honey",
            "vinegar", "spices", "mustard", "ketchup", "mayonnaise", "steak",
            "hamburger", "hot dogs", "cranberry", "pie", "stuffing", "shrimp",
            "ground beef", "olive oil", "peanut butter", "oranges", "salad"
        ]

    async def get_faq_list_from_page(self, page, page_url):
        """从FAQ列表页面提取所有FAQ链接"""
        try:
            logger.info(f"正在访问FAQ页面: {page_url}")
            await page.goto(page_url, wait_until='domcontentloaded', timeout=30000)
            await page.wait_for_timeout(1000)
            
            # 提取FAQ链接
            faq_links = []
            
            # 查找"More Questions"部分的所有链接
            more_questions_links = await page.query_selector_all('h2:has-text("More Questions") ~ ul li a')
            
            for link in more_questions_links:
                href = await link.get_attribute('href')
                title = await link.inner_text()
                
                if href and title:
                    # 构建完整URL
                    full_url = urljoin(self.base_url, href)
                    faq_links.append({
                        'title': title.strip(),
                        'url': full_url
                    })
            
            logger.info(f"从页面 {page_url} 提取到 {len(faq_links)} 个FAQ链接")
            return faq_links
            
        except Exception as e:
            logger.error(f"提取FAQ链接时出错 {page_url}: {str(e)}")
            return []

    async def get_all_faq_pages(self, page):
        """获取所有FAQ分页的链接"""
        try:
            logger.info("正在获取所有FAQ分页链接...")
            await page.goto(self.faq_base_url, wait_until='domcontentloaded', timeout=30000)
            await page.wait_for_timeout(1000)
            
            # 查找分页链接
            pagination_links = []
            
            # 查找分页导航
            page_links = await page.query_selector_all('a[href*="?page="]')
            
            # 添加第一页
            pagination_links.append(self.faq_base_url)
            
            for link in page_links:
                href = await link.get_attribute('href')
                if href and '?page=' in href:
                    full_url = urljoin(self.base_url, href)
                    if full_url not in pagination_links:
                        pagination_links.append(full_url)
            
            # 排序分页链接
            pagination_links.sort()
            
            logger.info(f"发现 {len(pagination_links)} 个FAQ分页")
            return pagination_links
            
        except Exception as e:
            logger.error(f"获取FAQ分页链接时出错: {str(e)}")
            return [self.faq_base_url]  # 至少返回第一页

    def categorize_faq(self, question, answer):
        """根据问题和答案内容对FAQ进行分类"""
        text = (question + " " + answer).lower()
        
        # 分类关键词映射
        category_keywords = {
            "frozen_foods": ["frozen", "freezer", "freeze", "thaw", "thawed"],
            "refrigerated_foods": ["refrigerat", "fridge", "cold", "chill"],
            "room_temperature": ["room temperature", "counter", "pantry"],
            "food_safety": ["safe", "unsafe", "bacteria", "contamination", "illness"],
            "storage_tips": ["store", "storage", "keep", "preserve"],
            "expiration_dates": ["expiration", "expire", "date", "best by", "sell by"],
            "preparation": ["cook", "cooking", "wash", "rinse", "prepare"]
        }
        
        # 计算每个分类的匹配分数
        scores = {}
        for category, keywords in category_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                scores[category] = score
        
        # 返回得分最高的分类，如果没有匹配则返回general
        if scores:
            return max(scores.items(), key=lambda x: x[1])[0]
        return "general"

    def extract_food_keywords(self, text):
        """从文本中提取食物关键词"""
        text_lower = text.lower()
        found_foods = []
        
        for food in self.food_keywords:
            if food.lower() in text_lower:
                found_foods.append(food)
        
        return found_foods

    def extract_tags(self, question, answer):
        """从问题和答案中提取标签"""
        text = (question + " " + answer).lower()
        tags = []
        
        # 常见标签关键词
        tag_keywords = {
            "safety": ["safe", "unsafe", "danger", "risk"],
            "storage": ["store", "storage", "keep", "preserve"],
            "freezing": ["freeze", "frozen", "freezer"],
            "refrigeration": ["refrigerat", "fridge", "cold"],
            "expiration": ["expire", "expiration", "date"],
            "cooking": ["cook", "cooking", "heat", "temperature"],
            "leftovers": ["leftover", "remain", "left"],
            "thawing": ["thaw", "defrost"]
        }
        
        for tag, keywords in tag_keywords.items():
            if any(keyword in text for keyword in keywords):
                tags.append(tag)
        
        return tags

    async def extract_faq_content(self, page, faq_item):
        """提取单个FAQ的详细内容"""
        retry_count = 0
        max_retries = 3

        while retry_count < max_retries:
            try:
                logger.info(f"正在提取FAQ内容: {faq_item['title']} (尝试 {retry_count + 1}/{max_retries})")

                await page.goto(faq_item['url'], wait_until='domcontentloaded', timeout=30000)
                await page.wait_for_timeout(800)
                break

            except Exception as e:
                retry_count += 1
                logger.warning(f"访问FAQ页面失败 (尝试 {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    await page.wait_for_timeout(2000)
                else:
                    logger.error(f"访问FAQ页面失败，已达到最大重试次数: {faq_item['url']}")
                    return None

        try:
            # 提取问题标题
            question_element = await page.query_selector('h1, h2, .question-title')
            question = faq_item['title']  # 使用列表页的标题作为备选

            if question_element:
                question_text = await question_element.inner_text()
                if question_text.strip():
                    question = question_text.strip()

            # 提取答案内容
            answer = ""

            # 查找包含"Answer:"的段落
            answer_elements = await page.query_selector_all('p:has-text("Answer:"), div:has-text("Answer:")')

            if answer_elements:
                # 获取Answer:后面的内容
                for element in answer_elements:
                    text = await element.inner_text()
                    if "Answer:" in text:
                        answer_part = text.split("Answer:", 1)[-1].strip()
                        if answer_part:
                            answer = answer_part
                            break

            # 如果没有找到Answer:标记，尝试提取主要内容区域
            if not answer:
                content_selectors = [
                    'div.content p',
                    'div.main-content p',
                    'article p',
                    '.answer-content',
                    'p'
                ]

                for selector in content_selectors:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        answer_parts = []
                        for element in elements:
                            text = await element.inner_text()
                            text = text.strip()
                            if text and len(text) > 20 and "Answer:" not in text:
                                answer_parts.append(text)

                        if answer_parts:
                            answer = " ".join(answer_parts[:3])  # 取前3段作为答案
                            break

            # 提取相关链接
            related_links = []
            see_also_elements = await page.query_selector_all('a[href*="stilltasty.com"]')

            for link in see_also_elements:
                href = await link.get_attribute('href')
                title = await link.inner_text()

                if href and title and title.strip():
                    title = title.strip()
                    if len(title) > 5 and href != faq_item['url']:  # 避免自引用
                        related_links.append({
                            'title': title,
                            'url': href if href.startswith('http') else urljoin(self.base_url, href)
                        })

            # 去重相关链接
            seen_urls = set()
            unique_links = []
            for link in related_links:
                if link['url'] not in seen_urls:
                    seen_urls.add(link['url'])
                    unique_links.append(link)

            # 分类FAQ
            category = self.categorize_faq(question, answer)

            # 提取相关食物关键词
            related_foods = self.extract_food_keywords(question + " " + answer)

            # 构建FAQ数据
            faq_data = {
                "id": f"stilltasty_faq_{hash(faq_item['url']) % 1000000}",
                "question": question,
                "answer": answer,
                "category": category,
                "category_zh": self.faq_categories.get(category, {}).get("name_zh", "一般问题"),
                "related_foods": related_foods,
                "related_links": unique_links[:5],  # 限制相关链接数量
                "tags": self.extract_tags(question, answer),
                "source": {
                    "name": "StillTasty.com",
                    "url": faq_item['url'],
                    "scraped_at": datetime.now().isoformat(),
                    "confidence": "high"
                }
            }

            return faq_data

        except Exception as e:
            logger.error(f"提取FAQ内容时出错 {faq_item['url']}: {str(e)}")
            return None

    async def scrape_all_faqs(self):
        """爬取所有FAQ页面的内容"""
        logger.info("🚀 开始爬取StillTasty FAQ页面...")

        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()

            # 设置用户代理
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            })

            try:
                # 获取所有FAQ分页链接
                pagination_links = await self.get_all_faq_pages(page)

                # 收集所有FAQ链接
                all_faq_links = []

                for page_url in pagination_links:
                    logger.info(f"正在处理分页: {page_url}")
                    faq_links = await self.get_faq_list_from_page(page, page_url)
                    all_faq_links.extend(faq_links)

                    # 分页间添加延迟
                    await page.wait_for_timeout(1000)

                logger.info(f"总共发现 {len(all_faq_links)} 个FAQ")

                # 去重FAQ链接
                seen_urls = set()
                unique_faq_links = []
                for faq in all_faq_links:
                    if faq['url'] not in seen_urls:
                        seen_urls.add(faq['url'])
                        unique_faq_links.append(faq)

                logger.info(f"去重后剩余 {len(unique_faq_links)} 个唯一FAQ")

                # 爬取每个FAQ的详细内容
                for i, faq_item in enumerate(unique_faq_links, 1):
                    try:
                        logger.info(f"处理FAQ {i}/{len(unique_faq_links)}: {faq_item['title']}")

                        faq_data = await self.extract_faq_content(page, faq_item)
                        if faq_data:
                            self.scraped_data.append(faq_data)
                            logger.info(f"成功提取FAQ: {faq_data['question'][:50]}...")
                        else:
                            logger.warning(f"未能提取FAQ数据: {faq_item['title']}")

                        # 添加延迟避免被封
                        await page.wait_for_timeout(500)

                    except Exception as e:
                        logger.error(f"处理FAQ {faq_item['title']} 时出错: {str(e)}")
                        continue

            finally:
                await browser.close()

        logger.info(f"FAQ爬取完成！总共获取了 {len(self.scraped_data)} 个FAQ")
        return self.scraped_data

    def save_data(self, output_dir="data"):
        """保存爬取的FAQ数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存JSON格式
        json_file = f"{output_dir}/stilltasty_faq_data_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)

        # 保存CSV格式
        csv_file = f"{output_dir}/stilltasty_faq_data_{timestamp}.csv"
        if self.scraped_data:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                fieldnames = [
                    'id', 'question', 'answer', 'category', 'category_zh',
                    'related_foods', 'tags', 'related_links_count', 'source_url'
                ]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for item in self.scraped_data:
                    writer.writerow({
                        'id': item['id'],
                        'question': item['question'],
                        'answer': item['answer'][:500] + '...' if len(item['answer']) > 500 else item['answer'],
                        'category': item['category'],
                        'category_zh': item['category_zh'],
                        'related_foods': ', '.join(item['related_foods']),
                        'tags': ', '.join(item['tags']),
                        'related_links_count': len(item['related_links']),
                        'source_url': item['source']['url']
                    })

        logger.info(f"FAQ数据已保存到:")
        logger.info(f"  JSON: {json_file}")
        logger.info(f"  CSV: {csv_file}")

        return json_file, csv_file

async def main():
    """主函数"""
    try:
        logger.info("🍎 开始StillTasty FAQ爬取...")

        # 创建爬虫实例
        scraper = StillTastyFAQScraper()

        # 爬取所有FAQ
        scraped_data = await scraper.scrape_all_faqs()

        if scraped_data:
            # 保存数据
            json_file, csv_file = scraper.save_data()

            print(f"\n✅ StillTasty FAQ爬取完成！总共获取了 {len(scraped_data)} 个FAQ")

            # 统计各分类的FAQ数量
            category_stats = {}
            for item in scraped_data:
                category = item['category_zh']
                if category not in category_stats:
                    category_stats[category] = 0
                category_stats[category] += 1

            print(f"\n📊 各分类FAQ数量统计:")
            for category, count in sorted(category_stats.items()):
                print(f"  - {category}: {count} 个FAQ")

            print(f"\n📁 数据已保存到:")
            print(f"  JSON: {json_file}")
            print(f"  CSV: {csv_file}")
        else:
            print("❌ 没有爬取到任何FAQ数据")

    except Exception as e:
        logger.error(f"爬取过程中出现错误: {str(e)}")
        print(f"❌ 爬取失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())

-- 迁移脚本：扩展FAQ表结构以兼容旧项目数据
-- 执行时间：2025-07-24

-- 1. 扩展 faq_categories 表
ALTER TABLE faq_categories
ADD COLUMN IF NOT EXISTS name_zh VARCHAR(255),
ADD COLUMN IF NOT EXISTS icon VARCHAR(50),
ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0;

-- 更新现有记录的优先级
UPDATE faq_categories SET priority = sort_order WHERE priority IS NULL;

-- 2. 扩展 faqs 表
ALTER TABLE faqs
ADD COLUMN IF NOT EXISTS external_id VARCHAR(255) UNIQUE,
ADD COLUMN IF NOT EXISTS answer_summary TEXT,
ADD COLUMN IF NOT EXISTS key_points TEXT[],
ADD COLUMN IF NOT EXISTS related_foods TEXT[],
ADD COLUMN IF NOT EXISTS tags TEXT[],
ADD COLUMN IF NOT EXISTS word_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS helpful_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS source_name VARCHAR(255) DEFAULT 'StillTasty.com',
ADD COLUMN IF NOT EXISTS source_url TEXT,
ADD COLUMN IF NOT EXISTS confidence DECIMAL(3,2) DEFAULT 0.95,
ADD COLUMN IF NOT EXISTS processed_at TIMESTAMPTZ;

-- 3. 创建食物分类表（如果不存在）
CREATE TABLE IF NOT EXISTS food_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. 创建食物主表（如果不存在）
CREATE TABLE IF NOT EXISTS foods (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    search_key VARCHAR(255) NOT NULL UNIQUE,
    category_id INTEGER REFERENCES food_categories(id),
    refrigerated_days INTEGER,
    frozen_days INTEGER,
    room_temperature_days INTEGER,
    storage_tips TEXT[],
    source VARCHAR(50) DEFAULT 'USDA' CHECK (source IN ('USDA', 'LOCAL', 'AI')),
    confidence DECIMAL(3,2) DEFAULT 0.98,
    usda_product_id INTEGER,
    usda_category VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. 创建食物别名表（如果不存在）
CREATE TABLE IF NOT EXISTS food_aliases (
    id SERIAL PRIMARY KEY,
    food_id INTEGER REFERENCES foods(id),
    alias VARCHAR(255) NOT NULL,
    language VARCHAR(10) DEFAULT 'en',
    alias_type VARCHAR(50) DEFAULT 'name',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. 创建FAQ-食物关联表（如果不存在）
CREATE TABLE IF NOT EXISTS faq_food_relations (
    id SERIAL PRIMARY KEY,
    faq_id INTEGER REFERENCES faqs(id),
    food_id INTEGER REFERENCES foods(id),
    relevance_score DECIMAL(3,2) DEFAULT 0.5,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 7. 创建FAQ搜索日志表（如果不存在）
CREATE TABLE IF NOT EXISTS faq_search_logs (
    id SERIAL PRIMARY KEY,
    search_query TEXT NOT NULL,
    faq_id INTEGER REFERENCES faqs(id),
    user_ip VARCHAR(45),
    user_agent TEXT,
    found BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_faqs_category_id ON faqs(category_id);
CREATE INDEX IF NOT EXISTS idx_faqs_external_id ON faqs(external_id);
CREATE INDEX IF NOT EXISTS idx_foods_search_key ON foods(search_key);
CREATE INDEX IF NOT EXISTS idx_foods_category_id ON foods(category_id);
CREATE INDEX IF NOT EXISTS idx_food_aliases_food_id ON food_aliases(food_id);
CREATE INDEX IF NOT EXISTS idx_food_aliases_alias ON food_aliases(alias);
CREATE INDEX IF NOT EXISTS idx_faq_food_relations_faq_id ON faq_food_relations(faq_id);
CREATE INDEX IF NOT EXISTS idx_faq_food_relations_food_id ON faq_food_relations(food_id);
CREATE INDEX IF NOT EXISTS idx_faq_search_logs_search_query ON faq_search_logs(search_query);

-- 9. 添加更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
DROP TRIGGER IF EXISTS update_faq_categories_updated_at ON faq_categories;
CREATE TRIGGER update_faq_categories_updated_at
    BEFORE UPDATE ON faq_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_faqs_updated_at ON faqs;
CREATE TRIGGER update_faqs_updated_at
    BEFORE UPDATE ON faqs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_food_categories_updated_at ON food_categories;
CREATE TRIGGER update_food_categories_updated_at
    BEFORE UPDATE ON food_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_foods_updated_at ON foods;
CREATE TRIGGER update_foods_updated_at
    BEFORE UPDATE ON foods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
#!/usr/bin/env node

/**
 * 设置 Creem Enterprise 产品
 * 使用方法: node scripts/setup-creem-enterprise.js
 */

require('dotenv').config();

const CREEM_API_BASE = process.env.NODE_ENV === 'production' 
  ? 'https://api.creem.io/v1' 
  : 'https://test-api.creem.io/v1';

const CREEM_API_KEY = process.env.CREEM_API_KEY;

if (!CREEM_API_KEY) {
  console.error('❌ 请在 .env 文件中设置 CREEM_API_KEY');
  process.exit(1);
}

// Enterprise 产品配置
const enterpriseProduct = {
  name: 'HowLongFresh Enterprise',
  description: 'Perfect for businesses, restaurants, and food service providers. Unlimited queries and premium features.',
  price: 1299, // $12.99 (以分为单位)
  currency: 'USD',
  billing_type: 'recurring',
  billing_period: 'yearly', // 年付
  status: 'active',
  tax_mode: 'exclusive',
  tax_category: 'saas',
  image_url: 'https://nucn5fajkcc6sgrd.public.blob.vercel-storage.com/product-0VEFcy9OOkNszDjHA6DcXSJeSmccDG.png', // 可以使用相同的图片
  default_success_url: `${process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000'}/payment-success`
};

async function createProduct() {
  console.log('🚀 创建 Enterprise 产品...');
  console.log('📦 产品配置:');
  console.log(JSON.stringify(enterpriseProduct, null, 2));
  
  try {
    const response = await fetch(`${CREEM_API_BASE}/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': CREEM_API_KEY,
      },
      body: JSON.stringify(enterpriseProduct),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`API 错误: ${response.status} - ${error}`);
    }

    const product = await response.json();
    
    console.log('');
    console.log('✅ 产品创建成功！');
    console.log('📋 产品信息:');
    console.log(`   ID: ${product.id}`);
    console.log(`   名称: ${product.name}`);
    console.log(`   价格: $${product.price / 100} ${product.currency}`);
    console.log(`   计费周期: ${product.billing_period}`);
    console.log('');
    console.log('🔧 下一步:');
    console.log(`1. 复制产品ID: ${product.id}`);
    console.log('2. 更新 services/creem.ts 中的 CREEM_PRODUCT_ID_MAP:');
    console.log(`   'enterprise_yearly': '${product.id}',`);
    console.log('3. 更新 i18n 配置文件中的价格和产品信息');
    
    return product;
  } catch (error) {
    console.error('❌ 创建产品失败:', error.message);
    process.exit(1);
  }
}

// 执行创建
createProduct();
#!/usr/bin/env python3
"""
改进的EatByDate.com食物数据爬虫

基于测试结果优化的版本，能够正确提取网站数据
"""

import json
import time
import csv
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedEatByDateScraper:
    def __init__(self, headless=True):
        """初始化爬虫"""
        self.base_url = "https://eatbydate.com"
        self.driver = self._setup_driver(headless)
        self.scraped_data = []
        
        # 分类页面URL，用于动态发现食物链接
        self.category_urls = {
            "奶制品": "https://eatbydate.com/dairy/",
            "水果": "https://eatbydate.com/fruits/",
            "蔬菜": "https://eatbydate.com/vegetables/",
            "蛋白质": "https://eatbydate.com/proteins/",
            "谷物": "https://eatbydate.com/grains/"
        }
        
    def _setup_driver(self, headless=True):
        """设置Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            return driver
        except Exception as e:
            logger.error(f"Failed to initialize Chrome driver: {e}")
            raise
    
    def extract_food_data(self, url, category):
        """提取单个食物页面的数据"""
        try:
            self.driver.get(url)
            time.sleep(3)
            
            # 从URL中提取食物名称
            food_name = self._extract_name_from_url(url)
            
            food_data = {
                'name': food_name,
                'category': category,
                'url': url,
                'pantry_life': '',
                'refrigerator_life': '',
                'freezer_life': '',
                'description': '',
                'scraped_at': datetime.now().isoformat()
            }
            
            # 提取保质期表格数据
            self._extract_storage_table(food_data)
            
            # 提取描述
            self._extract_description(food_data)
            
            logger.info(f"Successfully extracted: {food_name}")
            return food_data
            
        except Exception as e:
            logger.error(f"Error extracting data from {url}: {e}")
            return None
    
    def _extract_name_from_url(self, url):
        """从URL中提取食物名称"""
        try:
            # 从URL路径中提取名称
            parts = url.split('/')
            for part in reversed(parts):
                if part and part != '':
                    # 清理URL片段
                    name = part.replace('-shelf-life-expiration-date', '')
                    name = name.replace('how-long-do-', '').replace('how-long-does-', '')
                    name = name.replace('-last', '').replace('-', ' ')
                    return name.title()
            return "Unknown"
        except:
            return "Unknown"
    
    def _extract_storage_table(self, food_data):
        """提取保质期表格数据"""
        try:
            tables = self.driver.find_elements(By.CSS_SELECTOR, "table")
            
            for table in tables:
                table_text = table.text.lower()
                
                # 检查是否是保质期表格
                if any(keyword in table_text for keyword in ['pantry', 'refrigerator', 'freezer', 'counter']):
                    rows = table.find_elements(By.CSS_SELECTOR, "tr")
                    
                    for row in rows[1:]:  # 跳过表头
                        cells = row.find_elements(By.CSS_SELECTOR, "td, th")
                        
                        if len(cells) >= 3:
                            cell_texts = [cell.text.strip() for cell in cells]
                            
                            # 检查是否是相关的食物行
                            first_cell = cell_texts[0].lower()
                            food_name_words = food_data['name'].lower().split()
                            
                            if (any(word in first_cell for word in food_name_words) or 
                                'fresh' in first_cell or 
                                len(rows) <= 3):  # 简单表格
                                
                                # 分配数据到相应字段
                                if len(cell_texts) > 1 and cell_texts[1]:
                                    food_data['pantry_life'] = cell_texts[1]
                                if len(cell_texts) > 2 and cell_texts[2]:
                                    food_data['refrigerator_life'] = cell_texts[2]
                                if len(cell_texts) > 3 and cell_texts[3]:
                                    food_data['freezer_life'] = cell_texts[3]
                                break
                    break
                    
        except Exception as e:
            logger.warning(f"Table extraction failed: {e}")
    
    def _extract_description(self, food_data):
        """提取食物描述"""
        try:
            # 查找描述段落
            desc_selectors = [
                "article p:first-of-type",
                ".entry-content p:first-of-type", 
                ".post-content p:first-of-type"
            ]
            
            for selector in desc_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.text:
                        food_data['description'] = element.text[:300]
                        break
                except:
                    continue
                    
        except Exception as e:
            logger.warning(f"Description extraction failed: {e}")
    
    def scrape_all_categories(self, max_per_category=5):
        """爬取所有分类的数据"""
        logger.info("Starting to scrape all categories...")
        
        for category, urls in self.food_urls.items():
            logger.info(f"Scraping category: {category}")
            
            # 限制每个分类的数量
            urls_to_scrape = urls[:max_per_category]
            
            for i, url in enumerate(urls_to_scrape):
                logger.info(f"Scraping {i+1}/{len(urls_to_scrape)}: {url}")
                
                food_data = self.extract_food_data(url, category)
                if food_data:
                    self.scraped_data.append(food_data)
                
                # 添加延迟
                time.sleep(2)
            
            # 分类间延迟
            time.sleep(3)
        
        logger.info(f"Scraping completed. Total items: {len(self.scraped_data)}")
    
    def save_data(self, output_dir="data"):
        """保存爬取的数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存为JSON格式
        json_file = os.path.join(output_dir, f"improved_eatbydate_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
        
        # 保存为CSV格式
        csv_file = os.path.join(output_dir, f"improved_eatbydate_data_{timestamp}.csv")
        if self.scraped_data:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=self.scraped_data[0].keys())
                writer.writeheader()
                writer.writerows(self.scraped_data)
        
        logger.info(f"Data saved to {json_file} and {csv_file}")
        logger.info(f"Total items scraped: {len(self.scraped_data)}")
        
        # 显示统计信息
        self._show_statistics()
    
    def _show_statistics(self):
        """显示爬取统计信息"""
        if not self.scraped_data:
            return
            
        stats = {}
        for item in self.scraped_data:
            category = item['category']
            stats[category] = stats.get(category, 0) + 1
        
        logger.info("Category statistics:")
        for category, count in stats.items():
            logger.info(f"  {category}: {count} items")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    scraper = ImprovedEatByDateScraper(headless=True)
    
    try:
        # 爬取数据（每个分类最多5个食物）
        scraper.scrape_all_categories(max_per_category=5)
        
        # 保存数据
        scraper.save_data()
        
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    main()

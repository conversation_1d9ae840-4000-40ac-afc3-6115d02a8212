#!/usr/bin/env python3
"""
EatByDate爬虫环境设置脚本
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def check_chrome():
    """检查Chrome浏览器"""
    try:
        if platform.system() == "Windows":
            # Windows Chrome路径
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
            ]
            chrome_found = any(os.path.exists(path) for path in chrome_paths)
        elif platform.system() == "Darwin":  # macOS
            chrome_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            ]
            chrome_found = any(os.path.exists(path) for path in chrome_paths)
        else:  # Linux
            result = subprocess.run(["which", "google-chrome"], 
                                  capture_output=True, text=True)
            chrome_found = result.returncode == 0
        
        if chrome_found:
            print("✅ Google Chrome found")
            return True
        else:
            print("❌ Google Chrome not found")
            print("Please install Google Chrome from: https://www.google.com/chrome/")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Chrome: {e}")
        return False

def install_dependencies():
    """安装Python依赖"""
    print("Installing Python dependencies...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print("❌ Failed to install dependencies")
            return False
            
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = ["data", "processed_data", "test_data"]
    
    for dir_name in directories:
        dir_path = os.path.join(os.path.dirname(__file__), dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            print(f"✅ Created directory: {dir_name}")
        else:
            print(f"✅ Directory exists: {dir_name}")

def test_setup():
    """测试设置"""
    print("\nTesting setup...")
    
    try:
        # 测试导入关键模块
        import selenium
        from webdriver_manager.chrome import ChromeDriverManager
        print("✅ Selenium and WebDriver Manager imported successfully")
        
        # 测试ChromeDriver下载
        print("Testing ChromeDriver download...")
        driver_path = ChromeDriverManager().install()
        print(f"✅ ChromeDriver downloaded to: {driver_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Setup test failed: {e}")
        return False

def main():
    """主函数"""
    print("🔧 EatByDate爬虫环境设置")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查Chrome浏览器
    if not check_chrome():
        print("\n⚠️  Chrome not found, but continuing setup...")
        print("Please install Chrome before running the scraper.")
    
    # 创建目录
    print("\nCreating directories...")
    create_directories()
    
    # 安装依赖
    print("\nInstalling dependencies...")
    if not install_dependencies():
        sys.exit(1)
    
    # 测试设置
    if not test_setup():
        print("\n❌ Setup test failed. Please check the errors above.")
        sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")
    print("\n📝 Next steps:")
    print("1. Run test: python test_eatbydate_scraper.py")
    print("2. Run full scraper: python run_eatbydate_scraper.py")
    print("3. Or use the convenience script: python run_eatbydate_scraper.py test")

if __name__ == "__main__":
    main()

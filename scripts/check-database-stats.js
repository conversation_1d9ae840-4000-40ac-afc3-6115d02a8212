#!/usr/bin/env node
/**
 * 检查数据库中的数据统计
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const SUPABASE_URL = "https://plefidqreqjnesamigoc.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZWZpZHFyZXFqbmVzYW1pZ29jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMTQ1ODUsImV4cCI6MjA2NTY5MDU4NX0.Ys99vv5Xys8np6rskFj_7TV7pTBKpn5UVj8Fn9ZBDtc";

// 创建 Supabase 客户端
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkDatabaseStats() {
  console.log('📊 检查数据库统计信息...');
  
  try {
    // 1. 总食物数量
    const { count: totalFoods, error: countError } = await supabase
      .from('foods')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('获取食物总数失败:', countError);
      return;
    }
    
    console.log(`\n🍎 总食物数量: ${totalFoods}`);
    
    // 2. 按数据源统计
    const { data: sourceStats, error: sourceError } = await supabase
      .from('foods')
      .select('source')
      .not('source', 'is', null);
    
    if (sourceError) {
      console.error('获取数据源统计失败:', sourceError);
      return;
    }
    
    const sourceCount = {};
    sourceStats.forEach(item => {
      sourceCount[item.source] = (sourceCount[item.source] || 0) + 1;
    });
    
    console.log('\n📈 按数据源统计:');
    Object.entries(sourceCount).forEach(([source, count]) => {
      console.log(`  ${source}: ${count} 种食物`);
    });
    
    // 3. 按分类统计
    const { data: categoryStats, error: categoryError } = await supabase
      .from('foods')
      .select(`
        category_id,
        food_categories(name)
      `);
    
    if (categoryError) {
      console.error('获取分类统计失败:', categoryError);
      return;
    }
    
    const categoryCount = {};
    categoryStats.forEach(item => {
      const categoryName = item.food_categories?.name || 'Unknown';
      categoryCount[categoryName] = (categoryCount[categoryName] || 0) + 1;
    });
    
    console.log('\n📂 按分类统计:');
    Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)
      .forEach(([category, count]) => {
        console.log(`  ${category}: ${count} 种食物`);
      });
    
    // 4. 存储天数统计
    const { data: storageStats, error: storageError } = await supabase
      .from('foods')
      .select('room_temperature_days, refrigerated_days, frozen_days')
      .not('room_temperature_days', 'is', null)
      .not('refrigerated_days', 'is', null)
      .not('frozen_days', 'is', null);
    
    if (storageError) {
      console.error('获取存储统计失败:', storageError);
      return;
    }
    
    console.log('\n🌡️ 存储数据统计:');
    console.log(`  有室温存储数据: ${storageStats.filter(s => s.room_temperature_days).length} 种食物`);
    console.log(`  有冷藏存储数据: ${storageStats.filter(s => s.refrigerated_days).length} 种食物`);
    console.log(`  有冷冻存储数据: ${storageStats.filter(s => s.frozen_days).length} 种食物`);
    
    // 5. 最近添加的食物
    const { data: recentFoods, error: recentError } = await supabase
      .from('foods')
      .select('name, source, created_at')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (recentError) {
      console.error('获取最近食物失败:', recentError);
      return;
    }
    
    console.log('\n🕒 最近添加的10种食物:');
    recentFoods.forEach((food, index) => {
      const date = new Date(food.created_at).toLocaleDateString();
      console.log(`  ${index + 1}. ${food.name} (${food.source}) - ${date}`);
    });
    
    // 6. 测试搜索功能
    console.log('\n🔍 测试搜索功能:');
    const testQueries = ['apple', 'chicken', 'milk', 'bread', 'tomato'];
    
    for (const query of testQueries) {
      try {
        const { data: searchResults, error: searchError } = await supabase
          .rpc('search_foods', { search_term: query });
        
        if (searchError) {
          console.log(`  "${query}": 搜索失败 - ${searchError.message}`);
        } else if (searchResults && searchResults.length > 0) {
          console.log(`  "${query}": 找到 ${searchResults.length} 个结果`);
        } else {
          console.log(`  "${query}": 未找到结果`);
        }
      } catch (error) {
        console.log(`  "${query}": 搜索出错 - ${error.message}`);
      }
    }
    
    // 7. 数据质量检查
    console.log('\n✅ 数据质量检查:');
    
    // 检查缺失存储数据的食物
    const { count: missingStorage, error: missingError } = await supabase
      .from('foods')
      .select('*', { count: 'exact', head: true })
      .is('room_temperature_days', null)
      .is('refrigerated_days', null)
      .is('frozen_days', null);
    
    if (!missingError) {
      console.log(`  缺失所有存储数据: ${missingStorage} 种食物`);
    }
    
    // 检查有存储建议的食物
    const { count: withTips, error: tipsError } = await supabase
      .from('foods')
      .select('*', { count: 'exact', head: true })
      .not('storage_tips', 'is', null);
    
    if (!tipsError) {
      console.log(`  有存储建议: ${withTips} 种食物`);
    }
    
    console.log('\n🎉 数据库统计检查完成!');
    
  } catch (error) {
    console.error('检查统计时出错:', error);
  }
}

// 运行检查
checkDatabaseStats();

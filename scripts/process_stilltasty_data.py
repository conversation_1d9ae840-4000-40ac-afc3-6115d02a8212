#!/usr/bin/env python3
"""
处理StillTasty爬取数据的脚本
将原始数据转换为适合集成到howlongfresh项目的格式
"""

import json
import csv
import re
import os
from datetime import datetime
from typing import Dict, List, Optional

class StillTastyDataProcessor:
    def __init__(self):
        """初始化数据处理器"""
        self.processed_data = []
        
        # 类别映射
        self.category_mapping = {
            "Fruits": {"id": "fruits", "name_zh": "水果"},
            "Vegetables": {"id": "vegetables", "name_zh": "蔬菜"},
            "Dairy & Eggs": {"id": "dairy", "name_zh": "奶制品"},
            "Meat & Poultry": {"id": "meat", "name_zh": "肉类"},
            "Fish & Shellfish": {"id": "seafood", "name_zh": "海鲜"},
            "Nuts, Grains & Pasta": {"id": "grains", "name_zh": "谷物"},
            "Condiments & Oils": {"id": "condiments", "name_zh": "调料"},
            "Snacks & Baked Goods": {"id": "snacks", "name_zh": "零食"},
            "Herbs & Spices": {"id": "spices", "name_zh": "香料"},
            "Beverages": {"id": "beverages", "name_zh": "饮料"}
        }

    def parse_duration_to_days(self, duration_text: str) -> Optional[int]:
        """将持续时间文本转换为天数"""
        if not duration_text or duration_text.strip() in ['', '--', '-', 'N/A']:
            return None
        
        duration = duration_text.lower().strip()
        
        # 移除括号内容
        duration = re.sub(r'\([^)]*\)', '', duration)
        
        # 提取数字
        numbers = re.findall(r'\d+', duration)
        if not numbers:
            return None
        
        # 取第一个数字作为基准
        base_number = int(numbers[0])
        
        # 根据单位转换为天数
        if 'year' in duration:
            return base_number * 365
        elif 'month' in duration:
            return base_number * 30
        elif 'week' in duration:
            return base_number * 7
        elif 'day' in duration:
            return base_number
        else:
            # 如果没有明确单位，根据数字大小推测
            if base_number > 100:
                return base_number  # 可能是天数
            elif base_number > 12:
                return base_number * 7  # 可能是周数
            else:
                return base_number * 30  # 可能是月数

    def extract_keywords(self, food_name: str) -> List[str]:
        """从食品名称中提取关键词"""
        # 移除常见的描述词
        name = food_name.upper()
        
        # 移除包装相关词汇
        remove_words = [
            'COMMERCIALLY', 'PACKAGED', 'BOTTLED', 'CANNED', 'FROZEN',
            'FRESH', 'RAW', 'COOKED', 'DRIED', 'OPENED', 'UNOPENED',
            'SOLD', 'UNREFRIGERATED', 'REFRIGERATED', 'CONTAINER',
            'PACKAGE', 'VACUUM', 'WRAPPED', 'SLICED', 'CUT UP',
            'WHOLE', 'DELI', 'COUNTER', 'GROCERY', 'HOMEMADE'
        ]
        
        for word in remove_words:
            name = name.replace(word, '')
        
        # 清理标点符号和多余空格
        name = re.sub(r'[^\w\s]', ' ', name)
        name = re.sub(r'\s+', ' ', name).strip()
        
        # 分割为关键词
        keywords = [word.lower() for word in name.split() if len(word) > 2]
        
        # 去重并保持顺序
        unique_keywords = []
        for keyword in keywords:
            if keyword not in unique_keywords:
                unique_keywords.append(keyword)
        
        return unique_keywords

    def process_food_item(self, item: Dict) -> Dict:
        """处理单个食品项目"""
        # 提取基本信息
        name = item['name']
        category = item['category']
        
        # 获取类别映射
        category_info = self.category_mapping.get(category, {
            "id": category.lower().replace(' & ', '_').replace(' ', '_'),
            "name_zh": category
        })
        
        # 解析存储时间
        storage_conditions = item['storage_conditions']
        
        room_temp_days = self.parse_duration_to_days(
            storage_conditions['room_temperature']['raw']
        )
        refrigerated_days = self.parse_duration_to_days(
            storage_conditions['refrigerated']['raw']
        )
        frozen_days = self.parse_duration_to_days(
            storage_conditions['frozen']['raw']
        )
        
        # 提取关键词
        keywords = self.extract_keywords(name)
        
        # 构建处理后的数据
        processed_item = {
            "id": f"stilltasty_{hash(name) % 1000000}",  # 生成唯一ID
            "name": name.title(),  # 标题格式
            "name_clean": ' '.join(keywords).title(),  # 清理后的名称
            "keywords": keywords,
            "category_id": category_info["id"],
            "category_name": category,
            "category_name_zh": category_info["name_zh"],
            "storage": {
                "room_temperature": {
                    "days": room_temp_days,
                    "text": storage_conditions['room_temperature']['raw'],
                    "recommended": room_temp_days is not None
                },
                "refrigerated": {
                    "days": refrigerated_days,
                    "text": storage_conditions['refrigerated']['raw'],
                    "recommended": refrigerated_days is not None
                },
                "frozen": {
                    "days": frozen_days,
                    "text": storage_conditions['frozen']['raw'],
                    "recommended": frozen_days is not None
                }
            },
            "tips": item['shelf_life_tips'][:3],  # 限制提示数量
            "source": {
                "name": "StillTasty.com",
                "url": item['source']['url'],
                "confidence": "high",
                "scraped_at": item['source']['scraped_at']
            },
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        return processed_item

    def process_data_file(self, input_file: str) -> List[Dict]:
        """处理数据文件"""
        print(f"正在处理文件: {input_file}")
        
        with open(input_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
        
        processed_items = []
        
        for item in raw_data:
            try:
                processed_item = self.process_food_item(item)
                processed_items.append(processed_item)
                self.processed_data.append(processed_item)
            except Exception as e:
                print(f"处理食品项目时出错: {item.get('name', 'Unknown')} - {e}")
                continue
        
        print(f"成功处理了 {len(processed_items)} 个食品项目")
        return processed_items

    def save_processed_data(self, output_dir: str = "processed_data"):
        """保存处理后的数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存完整的JSON数据
        json_file = f"{output_dir}/processed_stilltasty_data_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.processed_data, f, ensure_ascii=False, indent=2)
        
        # 保存简化的CSV数据
        csv_file = f"{output_dir}/processed_stilltasty_data_{timestamp}.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'id', 'name', 'name_clean', 'keywords', 'category_id', 'category_name_zh',
                'room_temp_days', 'refrigerated_days', 'frozen_days',
                'room_temp_text', 'refrigerated_text', 'frozen_text',
                'tips_count', 'source_url'
            ])
            writer.writeheader()
            
            for item in self.processed_data:
                writer.writerow({
                    'id': item['id'],
                    'name': item['name'],
                    'name_clean': item['name_clean'],
                    'keywords': ', '.join(item['keywords']),
                    'category_id': item['category_id'],
                    'category_name_zh': item['category_name_zh'],
                    'room_temp_days': item['storage']['room_temperature']['days'],
                    'refrigerated_days': item['storage']['refrigerated']['days'],
                    'frozen_days': item['storage']['frozen']['days'],
                    'room_temp_text': item['storage']['room_temperature']['text'],
                    'refrigerated_text': item['storage']['refrigerated']['text'],
                    'frozen_text': item['storage']['frozen']['text'],
                    'tips_count': len(item['tips']),
                    'source_url': item['source']['url']
                })
        
        # 生成统计报告
        stats_file = f"{output_dir}/stilltasty_data_stats_{timestamp}.txt"
        self.generate_stats_report(stats_file)
        
        print(f"\n✅ 数据处理完成！")
        print(f"📁 输出文件:")
        print(f"  JSON: {json_file}")
        print(f"  CSV: {csv_file}")
        print(f"  统计: {stats_file}")
        
        return json_file, csv_file, stats_file

    def generate_stats_report(self, stats_file: str):
        """生成统计报告"""
        total_items = len(self.processed_data)
        
        # 类别统计
        category_stats = {}
        storage_stats = {
            'room_temp': 0,
            'refrigerated': 0,
            'frozen': 0
        }
        
        for item in self.processed_data:
            # 类别统计
            category = item['category_name_zh']
            category_stats[category] = category_stats.get(category, 0) + 1
            
            # 存储条件统计
            if item['storage']['room_temperature']['days']:
                storage_stats['room_temp'] += 1
            if item['storage']['refrigerated']['days']:
                storage_stats['refrigerated'] += 1
            if item['storage']['frozen']['days']:
                storage_stats['frozen'] += 1
        
        # 写入统计报告
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write("StillTasty 数据处理统计报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总食品项目数: {total_items}\n\n")
            
            f.write("类别分布:\n")
            f.write("-" * 30 + "\n")
            for category, count in sorted(category_stats.items()):
                percentage = (count / total_items) * 100
                f.write(f"{category}: {count} 个 ({percentage:.1f}%)\n")
            
            f.write("\n存储条件覆盖率:\n")
            f.write("-" * 30 + "\n")
            f.write(f"室温存储: {storage_stats['room_temp']}/{total_items} ({storage_stats['room_temp']/total_items*100:.1f}%)\n")
            f.write(f"冰箱存储: {storage_stats['refrigerated']}/{total_items} ({storage_stats['refrigerated']/total_items*100:.1f}%)\n")
            f.write(f"冷冻存储: {storage_stats['frozen']}/{total_items} ({storage_stats['frozen']/total_items*100:.1f}%)\n")

def main():
    """主函数"""
    processor = StillTastyDataProcessor()
    
    # 查找最新的数据文件
    data_dir = "data"
    if not os.path.exists(data_dir):
        print("❌ 数据目录不存在，请先运行爬虫获取数据")
        return
    
    # 查找JSON文件
    json_files = [f for f in os.listdir(data_dir) if f.startswith('stilltasty_') and f.endswith('.json')]
    
    if not json_files:
        print("❌ 未找到StillTasty数据文件")
        return
    
    # 使用最新的文件
    latest_file = sorted(json_files)[-1]
    input_file = os.path.join(data_dir, latest_file)
    
    print(f"🔄 开始处理StillTasty数据...")
    print(f"📂 输入文件: {input_file}")
    
    # 处理数据
    processor.process_data_file(input_file)
    
    # 保存处理后的数据
    json_file, csv_file, stats_file = processor.save_processed_data()
    
    # 显示统计信息
    print(f"\n📊 处理统计:")
    print(f"  总项目数: {len(processor.processed_data)}")
    
    # 显示类别分布
    category_counts = {}
    for item in processor.processed_data:
        category = item['category_name_zh']
        category_counts[category] = category_counts.get(category, 0) + 1
    
    print(f"  类别分布:")
    for category, count in sorted(category_counts.items()):
        print(f"    {category}: {count} 个")

if __name__ == "__main__":
    main()

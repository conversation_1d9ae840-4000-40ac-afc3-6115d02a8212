#!/usr/bin/env python3
"""
StillTasty.com 演示爬虫 - 只爬取几个类别进行演示
"""

import os
import json
import csv
import time
import logging
import asyncio
from datetime import datetime
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stilltasty_demo_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StillTastyDemoScraper:
    def __init__(self):
        """初始化演示爬虫"""
        self.base_url = "https://stilltasty.com"
        self.scraped_data = []
        
        # 只爬取几个类别进行演示
        self.demo_categories = {
            "26": {"name": "Fruits", "name_zh": "水果"},
            "9": {"name": "Dairy & Eggs", "name_zh": "奶制品和鸡蛋"},
            "27": {"name": "Meat & Poultry", "name_zh": "肉类和家禽"}
        }

    async def get_category_food_items(self, page, category_id, max_items=10):
        """获取某个类别下的食品项目链接（限制数量用于演示）"""
        food_items = []
        
        try:
            # 构建类别页面URL
            url = f"{self.base_url}/searchitems/index/{category_id}"
            
            logger.info(f"正在获取类别页面: {url}")
            
            # 访问页面
            await page.goto(url, wait_until='domcontentloaded', timeout=60000)
            await page.wait_for_timeout(3000)  # 等待页面加载
            
            # 查找食品项目链接
            food_links = await page.query_selector_all('ul li a[href*="/Fooditems/index/"]')
            
            if not food_links:
                logger.warning("没有找到食品链接")
                return food_items
            
            # 提取链接信息（限制数量）
            for i, link in enumerate(food_links[:max_items]):
                href = await link.get_attribute('href')
                text = await link.inner_text()
                
                if href and text:
                    full_url = urljoin(self.base_url, href)
                    food_items.append({
                        'name': text.strip(),
                        'url': full_url
                    })
                    logger.info(f"找到食品 {i+1}: {text.strip()}")
            
            logger.info(f"类别共找到 {len(food_items)} 个食品项目")
            
        except Exception as e:
            logger.error(f"获取类别食品项目时出错: {str(e)}")
        
        return food_items

    async def extract_food_details(self, page, food_item, category_info):
        """提取单个食品的详细保质期信息"""
        try:
            logger.info(f"正在提取食品详情: {food_item['name']}")
            
            # 访问食品详情页面
            await page.goto(food_item['url'], wait_until='domcontentloaded', timeout=60000)
            await page.wait_for_timeout(3000)
            
            # 提取存储条件信息
            storage_conditions = await self.extract_storage_conditions(page)
            
            # 提取保质期提示信息
            shelf_life_tips = await self.extract_shelf_life_tips(page)
            
            # 构建食品数据
            food_data = {
                "name": food_item['name'],
                "category": category_info['name'],
                "category_zh": category_info['name_zh'],
                "storage_conditions": storage_conditions,
                "shelf_life_tips": shelf_life_tips,
                "source": {
                    "name": "StillTasty.com",
                    "url": food_item['url'],
                    "scraped_at": datetime.now().isoformat(),
                    "confidence": "high"
                }
            }
            
            return food_data
            
        except Exception as e:
            logger.error(f"提取食品详情时出错 {food_item['url']}: {str(e)}")
            return None

    async def extract_storage_conditions(self, page):
        """提取存储条件信息"""
        storage_conditions = {
            "room_temperature": {"duration": None, "raw": ""},
            "refrigerated": {"duration": None, "raw": ""},
            "frozen": {"duration": None, "raw": ""}
        }
        
        try:
            # 方法1: 查找包含存储条件的div元素
            storage_divs = await page.query_selector_all('div')
            
            for div in storage_divs:
                text = await div.inner_text()
                text = text.strip().lower()
                
                # 查找冰箱存储信息
                if 'refrigerator' in text and len(text) < 20:
                    # 查找相邻的持续时间信息
                    parent = await div.query_selector('xpath=..')
                    if parent:
                        duration_divs = await parent.query_selector_all('div')
                        for duration_div in duration_divs:
                            duration_text = await duration_div.inner_text()
                            if duration_text and ('day' in duration_text.lower() or 'week' in duration_text.lower() or 'month' in duration_text.lower()):
                                storage_conditions["refrigerated"]["duration"] = self.normalize_duration(duration_text)
                                storage_conditions["refrigerated"]["raw"] = duration_text.strip()
                                logger.info(f"找到冰箱存储: {duration_text.strip()}")
                                break
                
                # 查找冷冻存储信息
                elif 'freezer' in text and len(text) < 20:
                    parent = await div.query_selector('xpath=..')
                    if parent:
                        duration_divs = await parent.query_selector_all('div')
                        for duration_div in duration_divs:
                            duration_text = await duration_div.inner_text()
                            if duration_text and ('day' in duration_text.lower() or 'week' in duration_text.lower() or 'month' in duration_text.lower() or 'year' in duration_text.lower()):
                                storage_conditions["frozen"]["duration"] = self.normalize_duration(duration_text)
                                storage_conditions["frozen"]["raw"] = duration_text.strip()
                                logger.info(f"找到冷冻存储: {duration_text.strip()}")
                                break
            
            # 方法2: 查找特定的存储信息结构
            page_content = await page.content()
            
            # 使用正则表达式查找存储信息
            refrigerator_pattern = r'Refrigerator.*?(\d+[-\s]*\d*\s*(?:days?|weeks?|months?).*?)(?=Freezer|$)'
            freezer_pattern = r'Freezer.*?(\d+[-\s]*\d*\s*(?:days?|weeks?|months?|years?).*?)(?=Shelf|$)'
            
            refrigerator_match = re.search(refrigerator_pattern, page_content, re.IGNORECASE | re.DOTALL)
            if refrigerator_match and not storage_conditions["refrigerated"]["duration"]:
                duration = refrigerator_match.group(1).strip()
                storage_conditions["refrigerated"]["duration"] = self.normalize_duration(duration)
                storage_conditions["refrigerated"]["raw"] = duration
                logger.info(f"正则匹配冰箱存储: {duration}")
            
            freezer_match = re.search(freezer_pattern, page_content, re.IGNORECASE | re.DOTALL)
            if freezer_match and not storage_conditions["frozen"]["duration"]:
                duration = freezer_match.group(1).strip()
                storage_conditions["frozen"]["duration"] = self.normalize_duration(duration)
                storage_conditions["frozen"]["raw"] = duration
                logger.info(f"正则匹配冷冻存储: {duration}")
                    
        except Exception as e:
            logger.warning(f"提取存储条件时出错: {e}")
        
        return storage_conditions

    async def extract_shelf_life_tips(self, page):
        """提取保质期提示信息"""
        tips = []
        
        try:
            # 查找包含提示信息的列表
            tip_lists = await page.query_selector_all('ul li')
            
            for li in tip_lists:
                tip_text = await li.inner_text()
                if tip_text and len(tip_text.strip()) > 20:  # 过滤掉太短的文本
                    # 检查是否包含保质期相关关键词
                    if any(keyword in tip_text.lower() for keyword in ['how long', 'last', 'storage', 'keep', 'refrigerat', 'freez']):
                        tips.append(tip_text.strip())
                        
        except Exception as e:
            logger.warning(f"提取保质期提示时出错: {e}")
        
        return tips[:3]  # 限制提示数量

    def normalize_duration(self, duration_text):
        """标准化持续时间文本"""
        if not duration_text or duration_text.strip() in ['--', '-', 'N/A', '']:
            return None
        
        duration = duration_text.strip()
        
        # 移除HTML标签
        duration = re.sub(r'<[^>]+>', '', duration)
        
        # 移除多余的空格和换行
        duration = re.sub(r'\s+', ' ', duration)
        
        # 标准化常见格式
        duration = duration.replace('Months', 'months').replace('Month', 'month')
        duration = duration.replace('Weeks', 'weeks').replace('Week', 'week')
        duration = duration.replace('Days', 'days').replace('Day', 'day')
        duration = duration.replace('Years', 'years').replace('Year', 'year')
        
        return duration

    async def demo_scrape(self):
        """演示爬取功能"""
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            # 设置超时时间
            page.set_default_timeout(60000)  # 60秒超时
            
            # 设置用户代理
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            })
            
            try:
                for category_id, category_info in self.demo_categories.items():
                    logger.info(f"开始演示爬取类别: {category_info['name']} ({category_info['name_zh']})")
                    
                    # 获取该类别下的食品项目（限制10个用于演示）
                    food_items = await self.get_category_food_items(page, category_id, max_items=10)
                    logger.info(f"演示类别 {category_info['name']} 共找到 {len(food_items)} 个食品项目")
                    
                    # 提取每个食品的详细信息
                    for i, food_item in enumerate(food_items, 1):
                        try:
                            logger.info(f"处理 {category_info['name']} 类别第 {i}/{len(food_items)} 个食品: {food_item['name']}")
                            
                            food_data = await self.extract_food_details(page, food_item, category_info)
                            if food_data:
                                self.scraped_data.append(food_data)
                                logger.info(f"成功提取: {food_data['name']}")
                            else:
                                logger.warning(f"未能提取数据: {food_item['name']}")
                            
                            # 添加延迟
                            await page.wait_for_timeout(2000)
                            
                        except Exception as e:
                            logger.error(f"处理食品 {food_item['name']} 时出错: {str(e)}")
                            continue
                    
                    logger.info(f"完成类别 {category_info['name']}，共提取 {len([item for item in self.scraped_data if item['category'] == category_info['name']])} 个食品")
                    
            finally:
                await browser.close()
        
        logger.info(f"演示爬取完成！总共获取了 {len(self.scraped_data)} 个食品项目")
        return self.scraped_data

    def save_demo_data(self, output_dir="data"):
        """保存演示数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON格式
        json_file = f"{output_dir}/stilltasty_demo_data_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
        
        # 保存CSV格式
        csv_file = f"{output_dir}/stilltasty_demo_data_{timestamp}.csv"
        if self.scraped_data:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=[
                    'name', 'category', 'category_zh', 
                    'room_temp_duration', 'refrigerated_duration', 'frozen_duration',
                    'shelf_life_tips_count', 'source_url'
                ])
                writer.writeheader()
                
                for item in self.scraped_data:
                    writer.writerow({
                        'name': item['name'],
                        'category': item['category'],
                        'category_zh': item['category_zh'],
                        'room_temp_duration': item['storage_conditions']['room_temperature']['duration'],
                        'refrigerated_duration': item['storage_conditions']['refrigerated']['duration'],
                        'frozen_duration': item['storage_conditions']['frozen']['duration'],
                        'shelf_life_tips_count': len(item['shelf_life_tips']),
                        'source_url': item['source']['url']
                    })
        
        logger.info(f"演示数据已保存到:")
        logger.info(f"  JSON: {json_file}")
        logger.info(f"  CSV: {csv_file}")
        
        return json_file, csv_file

async def main():
    """主函数"""
    try:
        start_time = datetime.now()
        logger.info("🧪 开始StillTasty.com演示爬取...")
        
        # 创建演示爬虫实例
        scraper = StillTastyDemoScraper()
        
        # 演示爬取
        scraped_data = await scraper.demo_scrape()
        
        if scraped_data:
            # 保存演示数据
            json_file, csv_file = scraper.save_demo_data()
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            print(f"\n✅ 演示爬取完成！")
            print(f"⏱️ 总耗时: {duration}")
            print(f"📊 总共获取了 {len(scraped_data)} 个食品项目")
            
            # 统计各类别的食品数量
            category_stats = {}
            for item in scraped_data:
                category = item['category']
                if category not in category_stats:
                    category_stats[category] = 0
                category_stats[category] += 1
            
            print(f"\n📊 各类别食品数量统计:")
            for category, count in sorted(category_stats.items()):
                print(f"  - {category}: {count} 个食品")
            
            print(f"\n📁 数据已保存到:")
            print(f"  JSON: {json_file}")
            print(f"  CSV: {csv_file}")
            
            # 显示部分结果
            print(f"\n📋 部分爬取结果预览:")
            for i, item in enumerate(scraped_data[:5], 1):
                print(f"{i}. {item['name']} ({item['category']})")
                print(f"   冰箱: {item['storage_conditions']['refrigerated']['raw']}")
                print(f"   冷冻: {item['storage_conditions']['frozen']['raw']}")
                print(f"   提示: {len(item['shelf_life_tips'])} 条")
                print()
        else:
            print("❌ 演示爬取没有获取到任何数据")
            
    except Exception as e:
        logger.error(f"演示爬取失败: {e}")
        print(f"❌ 演示爬取失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())

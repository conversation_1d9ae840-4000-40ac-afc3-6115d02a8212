#!/usr/bin/env node
/**
 * 图像识别功能测试脚本
 * 测试图片上传和AI识别功能
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3001';

/**
 * 创建一个简单的测试图片（1x1像素的PNG）
 */
function createTestImage() {
  // 1x1像素的透明PNG图片的base64数据
  const pngData = Buffer.from(
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77yQAAAABJRU5ErkJggg==',
    'base64'
  );
  
  const testImagePath = path.join(__dirname, 'test-image.png');
  fs.writeFileSync(testImagePath, pngData);
  return testImagePath;
}

/**
 * 测试图像识别API
 */
async function testImageRecognition() {
  console.log('🖼️ 图像识别功能测试');
  console.log('=' * 50);
  console.log(`API 地址: ${API_BASE_URL}`);
  console.log(`测试时间: ${new Date().toLocaleString()}\n`);

  try {
    // 创建测试图片
    console.log('📸 创建测试图片...');
    const testImagePath = createTestImage();
    console.log(`✅ 测试图片已创建: ${testImagePath}`);

    // 检查文件是否存在
    if (!fs.existsSync(testImagePath)) {
      throw new Error('测试图片创建失败');
    }

    const fileStats = fs.statSync(testImagePath);
    console.log(`📊 文件大小: ${fileStats.size} bytes`);

    // 准备FormData
    console.log('\n🚀 开始图像识别测试...');
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath), {
      filename: 'test-image.png',
      contentType: 'image/png'
    });

    const startTime = Date.now();
    
    // 发送请求
    const response = await fetch(`${API_BASE_URL}/api/food/identify-image`, {
      method: 'POST',
      body: formData,
      headers: formData.getHeaders()
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`⏱️ 响应时间: ${responseTime}ms`);
    console.log(`📡 HTTP状态: ${response.status} ${response.statusText}`);

    const responseText = await response.text();
    console.log(`📝 响应内容: ${responseText.substring(0, 200)}${responseText.length > 200 ? '...' : ''}`);

    if (response.ok) {
      try {
        const data = JSON.parse(responseText);
        
        console.log('\n✅ 图像识别成功!');
        console.log(`🍎 识别结果: ${data.name}`);
        console.log(`📂 食物类别: ${data.category}`);
        console.log(`🎯 置信度: ${(data.confidence * 100).toFixed(1)}%`);
        
        if (data.storage) {
          const storage = [];
          if (data.storage.refrigerated > 0) storage.push(`冷藏: ${data.storage.refrigerated}天`);
          if (data.storage.frozen > 0) storage.push(`冷冻: ${data.storage.frozen}天`);
          if (data.storage.room_temperature > 0) storage.push(`常温: ${data.storage.room_temperature}天`);
          console.log(`⏰ 存储时间: ${storage.join(', ')}`);
        }
        
        if (data.tips && data.tips.length > 0) {
          console.log(`💡 保鲜建议: ${data.tips.length}条`);
          data.tips.slice(0, 2).forEach((tip, index) => {
            console.log(`   ${index + 1}. ${tip.substring(0, 60)}${tip.length > 60 ? '...' : ''}`);
          });
        }

        return { success: true, data, responseTime };
        
      } catch (parseError) {
        console.log('❌ JSON解析失败:', parseError.message);
        return { success: false, error: 'JSON解析失败', responseTime };
      }
    } else {
      try {
        const errorData = JSON.parse(responseText);
        console.log(`❌ 图像识别失败: ${errorData.error}`);
        if (errorData.message) {
          console.log(`📄 错误详情: ${errorData.message}`);
        }
        if (errorData.suggestions) {
          console.log('💡 建议:');
          errorData.suggestions.forEach((suggestion, index) => {
            console.log(`   ${index + 1}. ${suggestion}`);
          });
        }
        return { success: false, error: errorData.error, responseTime };
      } catch (parseError) {
        console.log(`❌ 图像识别失败: HTTP ${response.status}`);
        console.log(`📄 响应内容: ${responseText}`);
        return { success: false, error: `HTTP ${response.status}`, responseTime };
      }
    }

  } catch (error) {
    console.log('❌ 测试失败:', error.message);
    return { success: false, error: error.message, responseTime: 0 };
  } finally {
    // 清理测试文件
    const testImagePath = path.join(__dirname, 'test-image.png');
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
      console.log('\n🧹 测试文件已清理');
    }
  }
}

/**
 * 测试不同的错误情况
 */
async function testErrorCases() {
  console.log('\n🚨 错误情况测试');
  console.log('-' * 30);

  const testCases = [
    {
      name: '无文件上传',
      test: async () => {
        const formData = new FormData();
        const response = await fetch(`${API_BASE_URL}/api/food/identify-image`, {
          method: 'POST',
          body: formData,
          headers: formData.getHeaders()
        });
        return { response, expectedStatus: 400 };
      }
    },
    {
      name: '非图片文件',
      test: async () => {
        const formData = new FormData();
        formData.append('image', Buffer.from('not an image'), {
          filename: 'test.txt',
          contentType: 'text/plain'
        });
        const response = await fetch(`${API_BASE_URL}/api/food/identify-image`, {
          method: 'POST',
          body: formData,
          headers: formData.getHeaders()
        });
        return { response, expectedStatus: 400 };
      }
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n🧪 测试: ${testCase.name}`);
      const { response, expectedStatus } = await testCase.test();
      const responseText = await response.text();
      
      if (response.status === expectedStatus) {
        console.log(`✅ 正确返回 ${response.status} 状态码`);
        try {
          const errorData = JSON.parse(responseText);
          console.log(`📄 错误信息: ${errorData.error}`);
        } catch (e) {
          console.log(`📄 响应: ${responseText.substring(0, 100)}`);
        }
      } else {
        console.log(`❌ 期望状态码 ${expectedStatus}，实际 ${response.status}`);
        console.log(`📄 响应: ${responseText.substring(0, 100)}`);
      }
    } catch (error) {
      console.log(`❌ 测试失败: ${error.message}`);
    }
  }
}

/**
 * 检查API可用性
 */
async function checkApiAvailability() {
  console.log('🔍 检查API可用性...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/food/identify-text`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ foodName: 'test' })
    });
    
    if (response.status === 200 || response.status === 404) {
      console.log('✅ API服务正常运行');
      return true;
    } else {
      console.log(`⚠️ API返回异常状态: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 无法连接到API服务: ${error.message}`);
    console.log('💡 请确保开发服务器正在运行 (npm run dev)');
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🧪 图像识别功能完整测试\n');
  
  // 检查API可用性
  const apiAvailable = await checkApiAvailability();
  if (!apiAvailable) {
    console.log('\n❌ API服务不可用，测试终止');
    return;
  }

  // 测试正常情况
  const result = await testImageRecognition();
  
  // 测试错误情况
  await testErrorCases();

  // 总结
  console.log('\n📊 测试总结');
  console.log('=' * 30);
  if (result.success) {
    console.log('✅ 图像识别功能正常工作');
    console.log(`⏱️ 平均响应时间: ${result.responseTime}ms`);
    
    if (result.responseTime < 5000) {
      console.log('🚀 响应速度优秀');
    } else if (result.responseTime < 10000) {
      console.log('👍 响应速度良好');
    } else {
      console.log('⚠️ 响应速度较慢，可能需要优化');
    }
  } else {
    console.log('❌ 图像识别功能存在问题');
    console.log(`🐛 错误: ${result.error}`);
    
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查 OPENROUTER_API_KEY 环境变量是否正确配置');
    console.log('2. 检查网络连接是否正常');
    console.log('3. 检查 OpenRouter API 服务是否可用');
    console.log('4. 查看服务器日志获取更多错误信息');
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testImageRecognition, testErrorCases, checkApiAvailability };

#!/usr/bin/env node
/**
 * 简化的数据迁移脚本
 * 使用 Node.js 和 fetch API 将 USDA 数据迁移到 Supabase
 */

const fs = require('fs');
const path = require('path');

// Supabase 配置
const SUPABASE_URL = 'https://plefidqreqjnesamigoc.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZWZpZHFyZXFqbmVzYW1pZ29jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMTQ1ODUsImV4cCI6MjA2NTY5MDU4NX0.Ys99vv5Xys8np6rskFj_7TV7pTBKpn5UVj8Fn9ZBDtc';

// 创建 Supabase 客户端函数
function createSupabaseClient() {
  const headers = {
    'apikey': SUPABASE_ANON_KEY,
    'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
    'Content-Type': 'application/json',
    'Prefer': 'return=representation'
  };

  return {
    async query(table, options = {}) {
      const url = `${SUPABASE_URL}/rest/v1/${table}`;
      const response = await fetch(url, {
        method: options.method || 'GET',
        headers,
        body: options.body ? JSON.stringify(options.body) : undefined
      });
      
      if (!response.ok) {
        throw new Error(`Supabase error: ${response.status} ${response.statusText}`);
      }
      
      return response.json();
    },

    async insert(table, data) {
      return this.query(table, {
        method: 'POST',
        body: Array.isArray(data) ? data : [data]
      });
    },

    async select(table, columns = '*', filters = {}) {
      let url = `${SUPABASE_URL}/rest/v1/${table}?select=${columns}`;
      
      Object.entries(filters).forEach(([key, value]) => {
        url += `&${key}=eq.${value}`;
      });
      
      const response = await fetch(url, { headers });
      return response.json();
    }
  };
}

// 标准化搜索键
function normalizeSearchKey(name) {
  return name.toLowerCase()
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, '_')
    .trim();
}

// 加载 USDA 数据
function loadUSDAData() {
  try {
    const filePath = path.join(__dirname, '../lib/usda-food-database.ts');
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // 提取 JSON 部分
    const start = content.indexOf('export const USDA_FOOD_DATABASE = ') + 'export const USDA_FOOD_DATABASE = '.length;
    const end = content.indexOf('export type FoodStorage');
    const jsonStr = content.substring(start, end).trim().replace(/;$/, '');
    
    return JSON.parse(jsonStr);
  } catch (error) {
    console.error('加载 USDA 数据失败:', error);
    return {};
  }
}

// 获取类别 ID
async function getCategoryId(supabase, categoryName) {
  try {
    const data = await supabase.select('food_categories', 'id', { name: categoryName });
    return data.length > 0 ? data[0].id : null;
  } catch (error) {
    console.error('获取类别 ID 失败:', error);
    return null;
  }
}

// 插入食物项目
async function insertFoodItem(supabase, searchKey, foodData) {
  try {
    const categoryId = await getCategoryId(supabase, foodData.category);
    
    const foodItem = {
      name: foodData.name,
      search_key: searchKey,
      category_id: categoryId,
      refrigerated_days: foodData.storage.refrigerated || null,
      frozen_days: foodData.storage.frozen || null,
      room_temperature_days: foodData.storage.room_temperature || null,
      storage_tips: foodData.tips || [],
      source: foodData.source || 'USDA',
      confidence: foodData.source === 'USDA' ? 0.98 : 0.90
    };
    
    // 移除 null 值
    Object.keys(foodItem).forEach(key => {
      if (foodItem[key] === null || foodItem[key] === undefined) {
        delete foodItem[key];
      }
    });
    
    const result = await supabase.insert('foods', foodItem);
    return result.length > 0 ? result[0].id : null;
  } catch (error) {
    console.error(`插入食物失败 ${searchKey}:`, error);
    return null;
  }
}

// 插入食物别名
async function insertFoodAlias(supabase, foodId, alias, language = 'en') {
  try {
    const aliasData = {
      food_id: foodId,
      alias: alias,
      language: language,
      alias_type: 'name'
    };
    
    await supabase.insert('food_aliases', aliasData);
  } catch (error) {
    console.error('插入别名失败:', error);
  }
}

// 创建中文映射
function createChineseMapping() {
  return {
    // 水果类
    '苹果': 'apples',
    '香蕉': 'bananas',
    '橙子': 'citrus_fruit',
    '橘子': 'citrus_fruit',
    '芒果': 'papaya,_mango,_feijoa,_passionfruit,_casaha_melon',
    '葡萄': 'grapes',
    '草莓': 'strawberries',
    '西瓜': 'melons',
    '梨': 'peaches,_nectarines,_plums,_pears,_sapote',
    '梨子': 'peaches,_nectarines,_plums,_pears,_sapote',
    '蓝莓': 'blueberries',
    '樱桃': 'berries',
    '菠萝': 'pineapple',
    '牛油果': 'avocados',
    '椰子': 'coconuts',
    
    // 蔬菜类
    '生菜': 'lettuce',
    '胡萝卜': 'carrots,_parsnips',
    '西红柿': 'tomatoes',
    '番茄': 'tomatoes',
    '土豆': 'potatoes',
    '马铃薯': 'potatoes',
    '洋葱': 'onions',
    '黄瓜': 'cucumbers',
    '西兰花': 'broccoli_and_broccoli_raab_(rapini)',
    '花椰菜': 'cauliflower',
    '芹菜': 'celery',
    '菠菜': 'greens',
    '白菜': 'cabbage',
    '茄子': 'eggplant',
    '辣椒': 'peppers',
    '玉米': 'corn_on_the_cob',
    '蘑菇': 'mushrooms',
    
    // 乳制品和蛋类
    '牛奶': 'milk',
    '奶酪': 'cheese',
    '鸡蛋': 'eggs',
    '黄油': 'butter',
    '奶油': 'cream',
    '酸奶': 'yogurt',
    
    // 肉类和海鲜
    '鸡肉': 'chicken',
    '牛肉': 'beef',
    '猪肉': 'pork',
    '火鸡': 'turkey',
    '鱼': 'lean_fish',
    '三文鱼': 'fatty_fish',
    '虾': 'shrimp,_crayfish',
    '螃蟹': 'crab_meat',
    
    // 其他
    '面包': 'bread',
    '米饭': 'rice',
    '意大利面': 'pasta',
    '豆腐': 'tofu',
    '泡菜': 'kimchi',
    '味噌': 'miso',
    '生抽': 'soy_sauce',
    '老抽': 'soy_sauce'
  };
}

// 主迁移函数
async function migrateData() {
  console.log('🚀 开始迁移数据到 Supabase...');
  
  const supabase = createSupabaseClient();
  const usdaData = loadUSDAData();
  const chineseMapping = createChineseMapping();
  
  console.log(`加载了 ${Object.keys(usdaData).length} 条 USDA 数据`);
  
  let successCount = 0;
  let errorCount = 0;
  const processedKeys = new Set();
  
  // 迁移数据
  for (const [searchKey, foodData] of Object.entries(usdaData)) {
    try {
      // 跳过重复的中文条目
      if (processedKeys.has(searchKey)) {
        continue;
      }
      
      // 标准化搜索键
      const normalizedKey = normalizeSearchKey(searchKey);
      
      // 插入食物项目
      const foodId = await insertFoodItem(supabase, normalizedKey, foodData);
      
      if (foodId) {
        // 添加原始搜索键作为别名
        if (searchKey !== normalizedKey) {
          await insertFoodAlias(supabase, foodId, searchKey, 'en');
        }
        
        // 添加中文别名
        for (const [chineseName, englishKey] of Object.entries(chineseMapping)) {
          if (englishKey === searchKey || normalizeSearchKey(englishKey) === normalizedKey) {
            await insertFoodAlias(supabase, foodId, chineseName, 'zh');
          }
        }
        
        successCount++;
        processedKeys.add(searchKey);
        
        if (successCount % 10 === 0) {
          console.log(`已处理 ${successCount} 条记录...`);
        }
      } else {
        errorCount++;
      }
      
    } catch (error) {
      console.error(`处理 ${searchKey} 时出错:`, error);
      errorCount++;
    }
  }
  
  console.log(`\n✅ 迁移完成!`);
  console.log(`成功: ${successCount} 条`);
  console.log(`失败: ${errorCount} 条`);
  console.log(`总计: ${Object.keys(usdaData).length} 条`);
}

// 测试搜索功能
async function testSearch() {
  console.log('\n🔍 测试搜索功能...');
  
  const supabase = createSupabaseClient();
  const testQueries = ['苹果', 'apple', 'mango', '芒果', 'chicken', '鸡肉'];
  
  for (const query of testQueries) {
    try {
      const url = `${SUPABASE_URL}/rest/v1/rpc/search_foods`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ search_term: query })
      });
      
      const result = await response.json();
      if (result && result.length > 0) {
        const food = result[0];
        console.log(`查询 '${query}': ${food.name} (${food.match_type})`);
      } else {
        console.log(`查询 '${query}': 未找到`);
      }
    } catch (error) {
      console.error(`查询 '${query}' 失败:`, error);
    }
  }
}

// 主函数
async function main() {
  console.log('🍎 USDA 数据迁移到 Supabase');
  console.log('=' * 50);
  
  try {
    await migrateData();
    await testSearch();
    console.log('\n🎉 迁移和测试完成!');
  } catch (error) {
    console.error('❌ 迁移失败:', error);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

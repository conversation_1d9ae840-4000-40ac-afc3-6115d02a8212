#!/usr/bin/env python3
"""
简化的EatByDate测试爬虫

专门用于测试从实际网站结构中提取数据
"""

import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleEatByDateScraper:
    def __init__(self):
        self.driver = self._setup_driver()
        self.scraped_data = []
        
        # 测试用的具体食物URL列表
        self.test_urls = [
            {
                'url': 'https://eatbydate.com/fruits/fresh/apples-shelf-life-expiration-date/',
                'name': 'Apples',
                'category': '水果'
            },
            {
                'url': 'https://eatbydate.com/dairy/milk/milk-shelf-life-expiration-date/',
                'name': 'Milk', 
                'category': '奶制品'
            },
            {
                'url': 'https://eatbydate.com/fruits/fresh/bananas-shelf-life-expiration-date/',
                'name': 'Bananas',
                'category': '水果'
            }
        ]
    
    def _setup_driver(self):
        """设置Chrome WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            return driver
        except Exception as e:
            logger.error(f"Failed to initialize Chrome driver: {e}")
            raise
    
    def extract_storage_table(self, url, name, category):
        """提取单个食物页面的保质期表格数据"""
        logger.info(f"Extracting data from: {name}")
        
        try:
            self.driver.get(url)
            time.sleep(3)
            
            food_data = {
                'name': name,
                'category': category,
                'url': url,
                'pantry_life': '',
                'refrigerator_life': '',
                'freezer_life': '',
                'description': '',
                'scraped_at': datetime.now().isoformat()
            }
            
            # 查找保质期表格
            try:
                tables = self.driver.find_elements(By.CSS_SELECTOR, "table")
                logger.info(f"Found {len(tables)} tables")
                
                for i, table in enumerate(tables):
                    table_text = table.text
                    logger.info(f"Table {i+1} content preview: {table_text[:200]}...")
                    
                    if 'pantry' in table_text.lower() or 'refrigerator' in table_text.lower():
                        logger.info(f"Found storage table in table {i+1}")
                        
                        # 提取表格行
                        rows = table.find_elements(By.CSS_SELECTOR, "tr")
                        logger.info(f"Table has {len(rows)} rows")
                        
                        for j, row in enumerate(rows):
                            row_text = row.text.lower()
                            logger.info(f"Row {j+1}: {row.text}")
                            
                            # 查找包含食物名称的行
                            if (name.lower() in row_text or 
                                'fresh' in row_text or 
                                j > 0):  # 跳过表头
                                
                                cells = row.find_elements(By.CSS_SELECTOR, "td, th")
                                logger.info(f"Row has {len(cells)} cells")
                                
                                if len(cells) >= 2:
                                    cell_texts = [cell.text.strip() for cell in cells]
                                    logger.info(f"Cell contents: {cell_texts}")
                                    
                                    # 根据列数分配数据
                                    if len(cells) >= 3:
                                        # 假设格式：食物类型 | 室温 | 冷藏 | (冷冻)
                                        if len(cell_texts) > 1 and cell_texts[1]:
                                            food_data['pantry_life'] = cell_texts[1]
                                        if len(cell_texts) > 2 and cell_texts[2]:
                                            food_data['refrigerator_life'] = cell_texts[2]
                                        if len(cell_texts) > 3 and cell_texts[3]:
                                            food_data['freezer_life'] = cell_texts[3]
                                    
                                    break
                        break
                        
            except Exception as e:
                logger.warning(f"Table extraction failed: {e}")
            
            # 如果表格提取失败，尝试从页面文本中提取
            if not any([food_data['pantry_life'], food_data['refrigerator_life'], food_data['freezer_life']]):
                try:
                    page_text = self.driver.find_element(By.TAG_NAME, "body").text
                    
                    # 简单的文本匹配
                    import re
                    
                    # 查找类似 "2-4 Weeks" 或 "1-2 Months" 的模式
                    duration_pattern = r'(\d+(?:-\d+)?)\s*(days?|weeks?|months?|years?)'
                    matches = re.findall(duration_pattern, page_text, re.IGNORECASE)
                    
                    logger.info(f"Found duration patterns: {matches}")
                    
                    # 简单分配：第一个匹配给室温，第二个给冷藏
                    if len(matches) >= 1:
                        food_data['pantry_life'] = f"{matches[0][0]} {matches[0][1]}"
                    if len(matches) >= 2:
                        food_data['refrigerator_life'] = f"{matches[1][0]} {matches[1][1]}"
                    if len(matches) >= 3:
                        food_data['freezer_life'] = f"{matches[2][0]} {matches[2][1]}"
                        
                except Exception as e:
                    logger.warning(f"Text extraction failed: {e}")
            
            # 提取描述
            try:
                desc_elements = self.driver.find_elements(By.CSS_SELECTOR, "article p, .entry-content p")
                if desc_elements:
                    food_data['description'] = desc_elements[0].text[:300]
            except:
                pass
            
            logger.info(f"Extracted data: {food_data}")
            return food_data
            
        except Exception as e:
            logger.error(f"Error extracting data from {url}: {e}")
            return None
    
    def run_test(self):
        """运行测试"""
        logger.info("Starting simple scraper test...")
        
        for item in self.test_urls:
            data = self.extract_storage_table(item['url'], item['name'], item['category'])
            if data:
                self.scraped_data.append(data)
            time.sleep(2)  # 延迟
        
        # 保存测试结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"test_data/simple_test_results_{timestamp}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Test completed. Results saved to {output_file}")
        logger.info(f"Successfully scraped {len(self.scraped_data)} items")
        
        # 显示结果摘要
        for item in self.scraped_data:
            logger.info(f"Item: {item['name']}")
            logger.info(f"  Pantry: {item['pantry_life']}")
            logger.info(f"  Refrigerator: {item['refrigerator_life']}")
            logger.info(f"  Freezer: {item['freezer_life']}")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    scraper = SimpleEatByDateScraper()
    
    try:
        scraper.run_test()
    except Exception as e:
        logger.error(f"Test failed: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    main()

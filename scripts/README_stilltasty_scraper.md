# StillTasty.com 食品保质期信息爬虫

这是一个专门用于爬取 StillTasty.com 网站食品保质期信息的 Playwright 爬虫工具集。

## 功能特点

- 🍎 **全面覆盖**: 支持爬取所有10个食品类别的保质期信息
- 🧊 **详细存储条件**: 提取冰箱、冷冻、室温存储的持续时间
- 💡 **保质期提示**: 获取专业的食品存储建议
- 📊 **多格式输出**: 同时生成 JSON 和 CSV 格式数据
- 🚀 **高效稳定**: 使用 Playwright 避免反爬虫检测
- 🔧 **灵活配置**: 支持测试、演示、完整爬取模式

## 支持的食品类别

1. **Fruits** (水果) - 26
2. **Vegetables** (蔬菜) - 25  
3. **Dairy & Eggs** (奶制品和鸡蛋) - 9
4. **Meat & Poultry** (肉类和家禽) - 27
5. **Fish & Shellfish** (鱼类和贝类) - 7
6. **Nuts, Grains & Pasta** (坚果、谷物和面食) - 28
7. **Condiments & Oils** (调料和油类) - 6
8. **Snacks & Baked Goods** (零食和烘焙食品) - 31
9. **Herbs & Spices** (香草和香料) - 30
10. **Beverages** (饮料) - 5

## 安装依赖

```bash
# 安装 Python 依赖
pip install playwright beautifulsoup4 requests

# 安装 Playwright 浏览器
playwright install chromium
```

## 使用方法

### 1. 测试爬虫 (推荐先运行)

```bash
python stilltasty_test_scraper.py
```

- 只爬取水果类别的前5个食品
- 用于测试爬虫功能是否正常
- 运行时间约1-2分钟

### 2. 演示爬虫

```bash
python stilltasty_demo_scraper.py
```

- 爬取3个类别（水果、奶制品、肉类）
- 每个类别限制10个食品项目
- 运行时间约3-5分钟
- 适合快速了解爬虫效果

### 3. 完整爬虫

```bash
python run_stilltasty_scraper.py
```

- 爬取所有10个类别的完整数据
- 预计获取数千个食品项目
- 运行时间可能需要1-2小时
- 适合生产环境使用

## 输出数据格式

### JSON 格式示例

```json
{
  "name": "APPLE JUICE - COMMERCIALLY FROZEN CONCENTRATE",
  "category": "Fruits",
  "category_zh": "水果",
  "storage_conditions": {
    "room_temperature": {
      "duration": null,
      "raw": ""
    },
    "refrigerated": {
      "duration": "5-7 days after prepared for drinking",
      "raw": "5-7 days after prepared for drinking"
    },
    "frozen": {
      "duration": "1 year (best quality)",
      "raw": "1 year (best quality)"
    }
  },
  "shelf_life_tips": [
    "How long does frozen apple juice concentrate last?...",
    "Properly stored, frozen apple juice concentrate will..."
  ],
  "source": {
    "name": "StillTasty.com",
    "url": "https://stilltasty.com/Fooditems/index/16371",
    "scraped_at": "2025-07-17T06:59:40.552913",
    "confidence": "high"
  }
}
```

### CSV 格式字段

- `name`: 食品名称
- `category`: 英文类别名
- `category_zh`: 中文类别名
- `room_temp_duration`: 室温存储时间
- `refrigerated_duration`: 冰箱存储时间
- `frozen_duration`: 冷冻存储时间
- `shelf_life_tips_count`: 保质期提示数量
- `source_url`: 数据来源URL

## 文件说明

### 核心爬虫文件

- `stilltasty_scraper.py`: 完整版爬虫，支持所有类别
- `stilltasty_demo_scraper.py`: 演示版爬虫，爬取3个类别
- `stilltasty_test_scraper.py`: 测试版爬虫，只爬取少量数据

### 运行脚本

- `run_stilltasty_scraper.py`: 运行完整爬虫的脚本

### 配置文件

- `requirements.txt`: Python 依赖包列表

### 输出目录

- `data/`: 存储爬取的数据文件
- `*.log`: 爬虫运行日志文件

## 数据质量

根据演示爬取结果：

- ✅ **冰箱存储信息覆盖率**: ~90%
- ✅ **冷冻存储信息覆盖率**: ~95%
- ✅ **保质期提示覆盖率**: ~100%
- ✅ **数据准确性**: 高（直接来源于权威网站）

## 注意事项

1. **网络稳定性**: 确保网络连接稳定，爬虫会自动处理超时重试
2. **爬取速度**: 为避免被封，爬虫在请求间添加了延迟
3. **数据更新**: StillTasty.com 的数据可能会更新，建议定期重新爬取
4. **使用限制**: 请遵守网站的使用条款，合理使用爬虫

## 故障排除

### 常见问题

1. **网络超时**: 增加 `timeout` 参数值
2. **浏览器启动失败**: 运行 `playwright install chromium`
3. **数据提取不完整**: 检查网站结构是否发生变化

### 日志文件

查看对应的 `.log` 文件获取详细的运行信息：

- `stilltasty_test_scraper.log`
- `stilltasty_demo_scraper.log`
- `stilltasty_full_scraper.log`

## 集成到项目

爬取的数据可以直接集成到 howlongfresh.site 项目中：

1. 将 JSON 数据导入到 Supabase 数据库
2. 在 AI 搜索中作为权威数据源
3. 为用户提供准确的食品保质期信息

## 许可证

本爬虫工具仅用于学习和研究目的，请遵守相关法律法规和网站使用条款。

#!/usr/bin/env node

/**
 * 测试分页功能
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testCategoryPagination() {
  console.log('=== 测试分页功能 ===\n');

  // 获取肉类的分类ID
  const { data: categories } = await supabase
    .from('food_categories')
    .select('id, name')
    .ilike('name', '%meat%');

  console.log('肉类分类：', categories);

  if (categories && categories.length > 0) {
    const categoryId = categories[0].id;
    
    // 获取总数
    const { count: total } = await supabase
      .from('foods')
      .select('*', { count: 'exact', head: true })
      .eq('category_id', categoryId);
    
    console.log(`\n肉类食物总数: ${total}`);
    
    // 测试分页
    const pageSize = 24;
    const totalPages = Math.ceil(total / pageSize);
    
    console.log(`每页显示: ${pageSize}`);
    console.log(`总页数: ${totalPages}`);
    
    // 获取第一页数据
    const page1 = await supabase
      .from('foods')
      .select('name')
      .eq('category_id', categoryId)
      .range(0, pageSize - 1);
    
    console.log(`\n第1页数据量: ${page1.data?.length || 0}`);
    
    // 获取第二页数据
    if (totalPages > 1) {
      const page2 = await supabase
        .from('foods')
        .select('name')
        .eq('category_id', categoryId)
        .range(pageSize, pageSize * 2 - 1);
      
      console.log(`第2页数据量: ${page2.data?.length || 0}`);
    }
    
    // 计算 hasMore
    console.log(`\nhasMore 计算测试:`);
    for (let page = 1; page <= totalPages + 1; page++) {
      const hasMore = (page * pageSize) < total;
      console.log(`第${page}页: hasMore = ${hasMore}`);
    }
  }
}

testCategoryPagination().catch(console.error);
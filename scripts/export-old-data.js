#!/usr/bin/env node

/**
 * 从旧项目导出所有FAQ相关数据
 * 项目ID: plefidqreqjnesamigoc
 */

const fs = require('fs');
const path = require('path');

// Supabase配置 - 旧项目
const OLD_PROJECT_URL = 'https://plefidqreqjnesamigoc.supabase.co';
// 注意：这里需要使用实际的anon key，我先用占位符
const OLD_PROJECT_KEY = 'YOUR_OLD_PROJECT_ANON_KEY';

/**
 * 执行Supabase查询
 */
async function querySupabase(table, options = {}) {
  try {
    let url = `${OLD_PROJECT_URL}/rest/v1/${table}`;

    // 添加 select 参数
    if (options.select) {
      url += `?select=${encodeURIComponent(options.select)}`;
    } else {
      url += '?select=*';
    }

    // 添加过滤器
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        url += `&${key}=eq.${encodeURIComponent(value)}`;
      });
    }

    // 添加排序
    if (options.order) {
      url += `&order=${options.order}`;
    }

    // 添加限制
    if (options.limit) {
      url += `&limit=${options.limit}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'apikey': OLD_PROJECT_KEY,
        'Authorization': `Bearer ${OLD_PROJECT_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`查询 ${table} 失败:`, error);
    return null;
  }
}

/**
 * 导出所有数据
 */
async function exportAllData() {
  console.log('🚀 开始导出旧项目数据...');

  const exportData = {
    export_time: new Date().toISOString(),
    source_project: 'plefidqreqjnesamigoc',
    target_project: 'emopvngdwwghndzcfxvw',
    tables: {}
  };

  // 1. 导出FAQ分类
  console.log('📂 导出FAQ分类...');
  const faqCategories = await querySupabase('faq_categories', {
    order: 'priority.desc,id'
  });
  if (faqCategories) {
    exportData.tables.faq_categories = faqCategories;
    console.log(`✅ FAQ分类: ${faqCategories.length} 条记录`);
  }

  // 2. 导出FAQ主表
  console.log('📝 导出FAQ主表...');
  const faqs = await querySupabase('faqs', {
    order: 'id'
  });
  if (faqs) {
    exportData.tables.faqs = faqs;
    console.log(`✅ FAQ: ${faqs.length} 条记录`);
  }

  // 3. 导出食物分类
  console.log('🍎 导出食物分类...');
  const foodCategories = await querySupabase('food_categories', {
    order: 'id'
  });
  if (foodCategories) {
    exportData.tables.food_categories = foodCategories;
    console.log(`✅ 食物分类: ${foodCategories.length} 条记录`);
  }

  // 4. 导出食物主表
  console.log('🥗 导出食物主表...');
  const foods = await querySupabase('foods', {
    order: 'id'
  });
  if (foods) {
    exportData.tables.foods = foods;
    console.log(`✅ 食物: ${foods.length} 条记录`);
  }

  // 5. 导出食物别名
  console.log('🏷️ 导出食物别名...');
  const foodAliases = await querySupabase('food_aliases', {
    order: 'id'
  });
  if (foodAliases) {
    exportData.tables.food_aliases = foodAliases;
    console.log(`✅ 食物别名: ${foodAliases.length} 条记录`);
  }

  // 6. 导出FAQ-食物关联关系
  console.log('🔗 导出FAQ-食物关联关系...');
  const faqFoodRelations = await querySupabase('faq_food_relations', {
    order: 'id'
  });
  if (faqFoodRelations) {
    exportData.tables.faq_food_relations = faqFoodRelations;
    console.log(`✅ FAQ-食物关联: ${faqFoodRelations.length} 条记录`);
  }

  // 保存到文件
  const outputDir = path.join(__dirname, 'exported_data');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const filename = `old_project_data_${timestamp}.json`;
  const filepath = path.join(outputDir, filename);

  fs.writeFileSync(filepath, JSON.stringify(exportData, null, 2), 'utf8');

  console.log(`\n✅ 数据导出完成！`);
  console.log(`📁 文件路径: ${filepath}`);
  console.log(`📊 导出统计:`);
  Object.entries(exportData.tables).forEach(([table, data]) => {
    console.log(`   ${table}: ${data.length} 条记录`);
  });

  return filepath;
}

// 执行导出
if (require.main === module) {
  exportAllData().catch(console.error);
}

module.exports = { exportAllData, querySupabase };
/**
 * Creem 产品设置脚本
 * 用于在 Creem 平台创建对应的产品
 */

import { createCreemProduct, CreemProduct } from '../services/creem';

// 从定价配置中获取产品信息
const PRODUCTS = [
  {
    name: "HowLongFresh 基础版 (月付)",
    description: "适合个人用户的基本需求",
    price: 999, // $9.99 in cents
    currency: "usd",
    type: "subscription" as const,
    interval: "month" as const,
    product_id: "basic_monthly"
  },
  {
    name: "HowLongFresh 专业版 (月付)",
    description: "适合家庭和小企业用户",
    price: 1999, // $19.99 in cents
    currency: "usd",
    type: "subscription" as const,
    interval: "month" as const,
    product_id: "pro_monthly"
  },
  {
    name: "HowLongFresh 基础版 (年付)",
    description: "年付享受优惠价格",
    price: 9999, // $99.99 in cents
    currency: "usd",
    type: "subscription" as const,
    interval: "year" as const,
    product_id: "basic_yearly"
  },
  {
    name: "HowLongFresh 专业版 (年付)",
    description: "年付享受更大优惠",
    price: 19999, // $199.99 in cents
    currency: "usd",
    type: "subscription" as const,
    interval: "year" as const,
    product_id: "pro_yearly"
  },
  // 中国用户产品（人民币）
  {
    name: "HowLongFresh 基础版 (月付) - 中国",
    description: "适合个人用户的基本需求",
    price: 699, // ¥6.99 in cents
    currency: "cny",
    type: "subscription" as const,
    interval: "month" as const,
    product_id: "basic_monthly_cn"
  },
  {
    name: "HowLongFresh 专业版 (月付) - 中国",
    description: "适合家庭和小企业用户",
    price: 1399, // ¥13.99 in cents
    currency: "cny",
    type: "subscription" as const,
    interval: "month" as const,
    product_id: "pro_monthly_cn"
  },
];

async function setupCreemProducts() {
  console.log('🚀 开始设置 Creem 产品...');

  const createdProducts: CreemProduct[] = [];

  for (const product of PRODUCTS) {
    try {
      console.log(`📦 创建产品: ${product.name}`);
      
      const creemProduct = await createCreemProduct({
        name: product.name,
        description: product.description,
        price: product.price,
        currency: product.currency,
        type: product.type,
        interval: product.interval,
      });

      createdProducts.push(creemProduct);
      
      console.log(`✅ 产品创建成功: ${creemProduct.id}`);
      console.log(`   名称: ${creemProduct.name}`);
      console.log(`   价格: ${creemProduct.price / 100} ${creemProduct.currency.toUpperCase()}`);
      console.log(`   类型: ${creemProduct.type}`);
      if (creemProduct.interval) {
        console.log(`   周期: ${creemProduct.interval}`);
      }
      console.log('');

    } catch (error) {
      console.error(`❌ 创建产品失败: ${product.name}`);
      console.error(`   错误: ${error}`);
      console.log('');
    }
  }

  console.log('📊 创建结果汇总:');
  console.log(`   成功: ${createdProducts.length} 个产品`);
  console.log(`   失败: ${PRODUCTS.length - createdProducts.length} 个产品`);

  if (createdProducts.length > 0) {
    console.log('\n📋 创建的产品列表:');
    createdProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name} (ID: ${product.id})`);
    });

    console.log('\n💡 下一步:');
    console.log('1. 将产品 ID 更新到定价配置文件中');
    console.log('2. 在 Creem Dashboard 中设置产品图片和详细描述');
    console.log('3. 配置 Webhook 端点（如果需要）');
  }
}

// 运行脚本
if (require.main === module) {
  setupCreemProducts()
    .then(() => {
      console.log('🎉 Creem 产品设置完成!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 设置过程中出现错误:', error);
      process.exit(1);
    });
}

export { setupCreemProducts };

# 设置认证数据库表

## 方法 1：通过 Supabase Dashboard（推荐）

1. 登录到您的 [Supabase Dashboard](https://app.supabase.com)
2. 选择您的项目
3. 在左侧菜单中点击 "SQL Editor"
4. 点击 "New query"
5. 复制并粘贴以下 SQL 代码：

```sql
-- Create user passwords table
CREATE TABLE IF NOT EXISTS user_passwords (
  id SERIAL PRIMARY KEY,
  user_uuid VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at timestamptz DEFAULT NOW(),
  updated_at timestamptz DEFAULT NOW(),
  FOREIGN KEY (user_uuid) REFERENCES users(uuid) ON DELETE CASCADE
);

-- Create email verifications table
CREATE TABLE IF NOT EXISTS email_verifications (
  id SERIAL PRIMARY KEY,
  token VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) NOT NULL,
  user_uuid VARCHAR(255),
  expires_at timestamptz NOT NULL,
  verified_at timestamptz,
  created_at timestamptz DEFAULT NOW(),
  <PERSON>OR<PERSON><PERSON><PERSON> KEY (user_uuid) REFERENCES users(uuid) ON DELETE CASCADE
);

-- Create password resets table
CREATE TABLE IF NOT EXISTS password_resets (
  id SERIAL PRIMARY KEY,
  token VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) NOT NULL,
  user_uuid VARCHAR(255),
  expires_at timestamptz NOT NULL,
  used_at timestamptz,
  created_at timestamptz DEFAULT NOW(),
  FOREIGN KEY (user_uuid) REFERENCES users(uuid) ON DELETE CASCADE
);

-- Add email_verified column to users table if not exists
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified_at timestamptz;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_email_verifications_token ON email_verifications(token);
CREATE INDEX IF NOT EXISTS idx_email_verifications_email ON email_verifications(email);
CREATE INDEX IF NOT EXISTS idx_password_resets_token ON password_resets(token);
CREATE INDEX IF NOT EXISTS idx_password_resets_email ON password_resets(email);
CREATE INDEX IF NOT EXISTS idx_user_passwords_user_uuid ON user_passwords(user_uuid);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for user_passwords
CREATE TRIGGER update_user_passwords_updated_at BEFORE UPDATE
    ON user_passwords FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

6. 点击 "Run" 按钮执行 SQL
7. 您应该看到 "Success. No rows returned" 的消息

## 方法 2：使用 Supabase CLI

如果您已安装 Supabase CLI：

```bash
# 1. 安装 Supabase CLI (如果还没安装)
npm install -g supabase

# 2. 链接到您的项目
supabase link --project-ref your-project-ref

# 3. 执行迁移
supabase db push
```

## 验证迁移成功

在 Supabase Dashboard 中：
1. 点击 "Table Editor"
2. 您应该能看到新创建的表：
   - `user_passwords`
   - `email_verifications`
   - `password_resets`
3. 检查 `users` 表，应该有新的列：
   - `email_verified`
   - `email_verified_at`

## 常见问题

### 如果看到 "relation users does not exist" 错误
这意味着 `users` 表还不存在。请先执行基础数据库设置：
```sql
-- 从 /data/install.sql 执行用户表创建语句
```

### 如果看到 "already exists" 错误
这是正常的，表示某些对象已经存在。SQL 中的 `IF NOT EXISTS` 子句会处理这种情况。
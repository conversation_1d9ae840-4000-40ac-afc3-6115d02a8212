#!/usr/bin/env python3
"""
实时监控快速爬虫进度的脚本
"""

import os
import time
import re
from datetime import datetime

def monitor_fast_scraper():
    """监控快速爬虫进度"""
    log_file = "stilltasty_fast_scraper.log"
    
    print("🚀 StillTasty快速爬虫进度监控")
    print("=" * 60)
    
    categories = [
        "Fruits (水果)",
        "Vegetables (蔬菜)", 
        "Dairy & Eggs (奶制品和鸡蛋)",
        "Meat & Poultry (肉类和家禽)",
        "Fish & Shellfish (鱼类和贝类)",
        "Nuts, Grains & Pasta (坚果、谷物和面食)",
        "Condiments & Oils (调料和油类)",
        "Snacks & Baked Goods (零食和烘焙食品)",
        "Herbs & Spices (香草和香料)",
        "Beverages (饮料)"
    ]
    
    start_time = None
    
    while True:
        try:
            # 检查日志文件
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                
                # 获取开始时间
                if not start_time:
                    start_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d+ - INFO - 🚀 开始StillTasty.com快速爬取', log_content)
                    if start_match:
                        start_time = datetime.strptime(start_match.group(1), '%Y-%m-%d %H:%M:%S')
                
                # 统计进度
                current_category = None
                current_category_zh = None
                completed_categories = 0
                current_items = 0
                total_items_in_category = 0
                failed_items = 0
                
                # 查找当前类别
                for category in categories:
                    category_name = category.split(' (')[0]
                    if f"开始快速爬取类别: {category}" in log_content:
                        current_category = category_name
                        current_category_zh = category.split(' (')[1].rstrip(')')
                    if f"完成类别 {category_name}" in log_content:
                        completed_categories += 1
                
                # 统计当前类别的项目数
                if current_category:
                    # 查找总项目数
                    total_match = re.search(rf'类别 {current_category} 共找到 (\d+) 个食品项目', log_content)
                    if total_match:
                        total_items_in_category = int(total_match.group(1))
                    
                    # 统计已处理的项目
                    current_items = len(re.findall(rf'处理 {current_category} 类别第 (\d+)/\d+ 个食品:', log_content))
                
                # 统计总的成功和失败项目
                total_success = log_content.count("成功提取:")
                total_failed = log_content.count("未能提取数据:")
                
                # 计算运行时间
                elapsed_time = ""
                if start_time:
                    elapsed = datetime.now() - start_time
                    hours, remainder = divmod(elapsed.total_seconds(), 3600)
                    minutes, seconds = divmod(remainder, 60)
                    elapsed_time = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
                
                # 计算进度百分比
                progress_percent = 0
                if total_items_in_category > 0:
                    progress_percent = (current_items / total_items_in_category) * 100
                
                # 计算速度
                speed = ""
                if start_time and total_success > 0:
                    elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                    if elapsed_minutes > 0:
                        items_per_minute = total_success / elapsed_minutes
                        speed = f"{items_per_minute:.1f} 项目/分钟"
                
                # 显示进度
                print(f"\r⏰ {datetime.now().strftime('%H:%M:%S')} | "
                      f"运行时间: {elapsed_time} | "
                      f"已完成类别: {completed_categories}/10 | "
                      f"当前: {current_category or '准备中'} ({current_category_zh or ''}) | "
                      f"进度: {current_items}/{total_items_in_category} ({progress_percent:.1f}%) | "
                      f"成功: {total_success} | "
                      f"失败: {total_failed} | "
                      f"速度: {speed}", end="")
                
                # 检查是否完成
                if "快速爬取完成！总共获取了" in log_content:
                    print(f"\n\n✅ 快速爬取完成！")
                    
                    # 显示最终统计
                    lines = log_content.split('\n')
                    for line in lines:
                        if "总共获取了" in line:
                            print(f"📊 {line.strip()}")
                            break
                    
                    # 显示各类别统计
                    print(f"\n📊 各类别统计:")
                    for category in categories:
                        category_name = category.split(' (')[0]
                        category_count = len(re.findall(rf'处理 {category_name} 类别第 \d+/\d+ 个食品:', log_content))
                        if category_count > 0:
                            print(f"  - {category}: {category_count} 个")
                    
                    break
                
                # 检查是否有错误
                if "快速爬取失败" in log_content:
                    print(f"\n\n❌ 快速爬取过程中出现错误，请检查日志文件: {log_file}")
                    break
                    
            else:
                print(f"\r⏰ {datetime.now().strftime('%H:%M:%S')} | 等待快速爬虫启动...", end="")
            
            time.sleep(3)  # 每3秒更新一次
            
        except KeyboardInterrupt:
            print(f"\n\n⏹️ 监控已停止")
            break
        except Exception as e:
            print(f"\n\n❌ 监控出错: {e}")
            break

if __name__ == "__main__":
    monitor_fast_scraper()

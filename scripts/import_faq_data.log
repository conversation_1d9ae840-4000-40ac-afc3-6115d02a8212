2025-07-22 06:24:30,566 - INFO - 🚀 开始FAQ数据导入...
2025-07-22 06:24:30,567 - INFO - 📂 输入文件: data/processed_faq_data_20250721_065602.json
2025-07-22 06:24:30,568 - ERROR - 导入过程中出现错误: 请设置NEXT_PUBLIC_SUPABASE_URL和SUPABASE_SERVICE_ROLE_KEY环境变量
2025-07-22 06:25:13,622 - INFO - 🚀 开始FAQ数据导入...
2025-07-22 06:25:13,623 - INFO - 📂 输入文件: data/processed_faq_data_20250721_065602.json
2025-07-22 06:25:13,623 - ERROR - 导入过程中出现错误: 请设置NEXT_PUBLIC_SUPABASE_URL和SUPABASE_SERVICE_ROLE_KEY环境变量
2025-07-22 06:25:41,160 - INFO - 🚀 开始FAQ数据导入...
2025-07-22 06:25:41,160 - INFO - 📂 输入文件: data/processed_faq_data_20250721_065602.json
2025-07-22 06:25:41,368 - INFO - ⚠️  请确保已在Supabase中执行了 create_faq_tables.sql
2025-07-22 06:26:46,909 - INFO - 开始导入FAQ数据文件: data/processed_faq_data_20250721_065602.json
2025-07-22 06:26:46,913 - INFO - 加载了 75 个FAQ项目
2025-07-22 06:26:46,914 - INFO - 处理FAQ 1/75: Is it Safe to Leave Canned Food Leftovers in the C...
2025-07-22 06:26:48,741 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:26:49,355 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_549823 "HTTP/2 200 OK"
2025-07-22 06:26:49,984 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:26:49,985 - INFO - 成功导入FAQ 1: Is it Safe to Leave Canned Food Leftovers in the C...
2025-07-22 06:26:50,506 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:26:51,004 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:26:51,494 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.bread "HTTP/2 200 OK"
2025-07-22 06:26:52,120 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25bread%25 "HTTP/2 200 OK"
2025-07-22 06:26:52,623 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:26:52,984 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:26:53,311 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:26:53,810 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:26:54,270 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:26:54,679 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:26:55,143 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:26:55,478 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:26:55,814 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:26:56,149 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:26:56,524 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:26:57,019 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:26:57,346 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:26:57,684 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:26:58,173 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:26:58,493 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:26:58,828 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:26:59,177 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:26:59,540 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:27:00,006 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:27:00,371 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:27:00,852 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:27:01,208 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:27:01,210 - INFO - 处理FAQ 2/75: Is it Safe to Put Hot Food In the Fridge?...
2025-07-22 06:27:01,703 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:27:02,042 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_999152 "HTTP/2 200 OK"
2025-07-22 06:27:02,389 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:27:02,389 - INFO - 成功导入FAQ 2: Is it Safe to Put Hot Food In the Fridge?...
2025-07-22 06:27:02,714 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:27:03,076 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:03,486 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.bread "HTTP/2 200 OK"
2025-07-22 06:27:03,804 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25bread%25 "HTTP/2 200 OK"
2025-07-22 06:27:04,205 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:27:04,612 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:27:04,938 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:27:05,262 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:27:05,636 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:27:06,046 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:27:06,456 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:06,813 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:27:07,143 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:27:07,473 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:27:07,804 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:27:08,140 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:27:08,504 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:27:08,842 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:27:09,250 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:27:09,630 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:27:10,040 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:27:10,450 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:27:10,863 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:27:11,196 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:27:11,521 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:27:12,019 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:27:12,397 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:27:12,398 - INFO - 处理FAQ 3/75: Are Eggs Still Safe After the Expiration Date?...
2025-07-22 06:27:12,729 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.expiration_dates "HTTP/2 200 OK"
2025-07-22 06:27:13,100 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_127058 "HTTP/2 200 OK"
2025-07-22 06:27:13,521 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:27:13,522 - INFO - 成功导入FAQ 3: Are Eggs Still Safe After the Expiration Date?...
2025-07-22 06:27:13,853 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:27:14,238 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:14,555 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:27:14,958 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:27:15,292 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:27:15,621 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:27:15,985 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:27:16,389 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:27:16,719 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:17,047 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:27:17,413 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:27:17,822 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.milk "HTTP/2 200 OK"
2025-07-22 06:27:18,231 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25milk%25 "HTTP/2 200 OK"
2025-07-22 06:27:18,641 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:27:19,051 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:27:19,564 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:27:19,897 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:27:20,253 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:27:20,690 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:27:21,009 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:27:21,406 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:27:21,816 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:27:22,225 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:27:22,554 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:27:22,945 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:27:23,262 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:27:23,605 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:27:23,606 - INFO - 处理FAQ 4/75: Should You Rinse a Raw Turkey Before Cooking It?...
2025-07-22 06:27:23,952 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.preparation "HTTP/2 200 OK"
2025-07-22 06:27:24,283 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_482046 "HTTP/2 200 OK"
2025-07-22 06:27:24,620 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:27:24,621 - INFO - 成功导入FAQ 4: Should You Rinse a Raw Turkey Before Cooking It?...
2025-07-22 06:27:24,969 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:27:25,317 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:25,674 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:27:26,002 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:27:26,328 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:27:26,665 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:27:27,008 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:27:27,334 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:27:27,757 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:28,162 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:27:28,574 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:27:28,907 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:27:29,291 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:27:29,701 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:27:30,110 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:27:30,519 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:27:30,929 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:27:31,275 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:27:31,607 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:27:31,925 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:27:32,261 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:27:32,670 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:27:33,079 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:27:33,489 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:27:33,901 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:27:33,902 - INFO - 处理FAQ 5/75: Is It Safe to Cook Stuffing Inside a Turkey?...
2025-07-22 06:27:34,308 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:27:34,718 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_602907 "HTTP/2 200 OK"
2025-07-22 06:27:35,046 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:27:35,047 - INFO - 成功导入FAQ 5: Is It Safe to Cook Stuffing Inside a Turkey?...
2025-07-22 06:27:35,382 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:27:35,742 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:36,107 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:27:36,462 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:27:36,789 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:27:37,111 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:27:37,483 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:27:37,897 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.fish "HTTP/2 200 OK"
2025-07-22 06:27:38,232 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:27:38,552 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:38,886 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:27:39,257 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:27:39,634 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:27:40,146 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:27:40,555 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:27:41,020 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:27:41,375 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:27:41,722 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:27:42,053 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:27:42,422 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:27:42,754 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:27:43,092 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:27:43,417 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:27:43,758 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:27:44,082 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:27:44,417 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:27:44,417 - INFO - 处理FAQ 6/75: Is it Safe to Refreeze a Thawed Turkey?...
2025-07-22 06:27:44,858 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:27:45,266 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_322155 "HTTP/2 200 OK"
2025-07-22 06:27:45,675 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:27:45,675 - INFO - 成功导入FAQ 6: Is it Safe to Refreeze a Thawed Turkey?...
2025-07-22 06:27:46,090 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:27:46,433 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:46,801 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:27:47,132 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:27:47,518 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:27:47,928 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:27:48,338 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:27:48,849 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:27:49,261 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:49,669 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:27:50,015 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:27:50,345 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:27:50,693 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:27:51,022 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:27:51,512 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:27:51,922 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:27:52,333 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:27:52,741 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:27:53,150 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:27:53,505 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:27:53,842 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:27:54,175 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:27:54,530 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:27:54,877 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:27:55,301 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:27:55,302 - INFO - 处理FAQ 7/75: Can You Freeze Cranberry Sauce?...
2025-07-22 06:27:55,710 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:27:56,053 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_304990 "HTTP/2 200 OK"
2025-07-22 06:27:56,429 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:27:56,430 - INFO - 成功导入FAQ 7: Can You Freeze Cranberry Sauce?...
2025-07-22 06:27:56,770 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:27:57,087 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:27:57,453 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cheese "HTTP/2 200 OK"
2025-07-22 06:27:57,861 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cheese%25 "HTTP/2 200 OK"
2025-07-22 06:27:58,271 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:27:58,586 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:27:58,911 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:27:59,236 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:27:59,574 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:27:59,895 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:28:00,219 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:00,553 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:28:00,900 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:28:01,236 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:28:01,566 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:28:01,913 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:28:02,244 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:28:02,581 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:28:02,918 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:28:03,260 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:28:03,585 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:28:03,925 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:28:04,312 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:28:04,727 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:28:05,061 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:28:05,386 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:28:05,745 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:28:05,746 - INFO - 处理FAQ 8/75: Can You Cook a Frozen Turkey Without Thawing It Fi...
2025-07-22 06:28:06,085 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:28:06,462 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_979324 "HTTP/2 200 OK"
2025-07-22 06:28:06,811 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:28:06,811 - INFO - 成功导入FAQ 8: Can You Cook a Frozen Turkey Without Thawing It Fi...
2025-07-22 06:28:07,150 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:28:07,485 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:07,835 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:28:08,203 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:28:08,532 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:28:08,858 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:28:09,189 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:28:09,511 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:28:09,946 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:10,274 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:28:10,608 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:28:10,939 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:28:11,378 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:28:11,723 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:28:12,054 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:28:12,402 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:28:12,736 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:28:13,072 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:28:13,425 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:28:13,837 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:28:14,245 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:28:14,660 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:28:15,064 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:28:15,474 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:28:15,812 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:28:15,813 - INFO - 处理FAQ 9/75: How Long Can You Keep a Thawed Turkey in the Fridg...
2025-07-22 06:28:16,191 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:28:16,600 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_757479 "HTTP/2 200 OK"
2025-07-22 06:28:16,946 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:28:16,947 - INFO - 成功导入FAQ 9: How Long Can You Keep a Thawed Turkey in the Fridg...
2025-07-22 06:28:17,317 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:28:17,639 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:18,036 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:28:18,369 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:28:18,703 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:28:19,046 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:28:19,467 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:28:19,801 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:28:20,185 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:20,508 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:28:20,901 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:28:21,252 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:28:21,720 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:28:22,075 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:28:22,437 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:28:22,847 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:28:23,256 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:28:23,602 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:28:23,931 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:28:24,266 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:28:24,659 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:28:24,993 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:28:25,325 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:28:25,667 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:28:26,000 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:28:26,002 - INFO - 处理FAQ 10/75: Is Pink Turkey Meat Safe to Eat?...
2025-07-22 06:28:26,331 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:28:26,680 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_322646 "HTTP/2 200 OK"
2025-07-22 06:28:27,028 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:28:27,029 - INFO - 成功导入FAQ 10: Is Pink Turkey Meat Safe to Eat?...
2025-07-22 06:28:27,381 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:28:27,762 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:28,105 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:28:28,479 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:28:28,793 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:28:29,198 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:28:29,605 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:28:30,015 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:28:30,424 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:30,834 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:28:31,245 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:28:31,584 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:28:31,909 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:28:32,269 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:28:32,677 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:28:33,086 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:28:33,496 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:28:33,906 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:28:34,317 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:28:34,725 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:28:35,038 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:28:35,375 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:28:35,749 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:28:36,158 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:28:36,490 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:28:36,491 - INFO - 处理FAQ 11/75: How Long Can You Keep Peeled Potatoes?...
2025-07-22 06:28:36,875 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:28:37,287 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_812428 "HTTP/2 200 OK"
2025-07-22 06:28:37,615 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:28:37,616 - INFO - 成功导入FAQ 11: How Long Can You Keep Peeled Potatoes?...
2025-07-22 06:28:37,941 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:28:38,260 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:38,575 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:28:38,904 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:28:39,223 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:28:39,560 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:28:39,950 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:28:40,281 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:28:40,608 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:40,944 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:28:41,277 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:28:41,688 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:28:42,005 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:28:42,405 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:28:42,739 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:28:43,122 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:28:43,534 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:28:43,871 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:28:44,249 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:28:44,658 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:28:45,068 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:28:45,477 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:28:45,889 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:28:46,214 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:28:46,552 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:28:46,553 - INFO - 处理FAQ 12/75: How Long Can Cooked Turkey Sit Out Before It Becom...
2025-07-22 06:28:46,958 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:28:47,321 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_487155 "HTTP/2 200 OK"
2025-07-22 06:28:47,730 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:28:47,730 - INFO - 成功导入FAQ 12: How Long Can Cooked Turkey Sit Out Before It Becom...
2025-07-22 06:28:48,139 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:28:48,549 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:48,959 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:28:49,304 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:28:49,677 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:28:50,005 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:28:50,335 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:28:50,701 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:28:51,109 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:51,594 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:28:51,924 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:28:52,338 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:28:52,749 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:28:53,157 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:28:53,567 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:28:53,916 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:28:54,246 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:28:54,591 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:28:55,000 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:28:55,410 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:28:55,737 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:28:56,064 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:28:56,386 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:28:56,741 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:28:57,151 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:28:57,152 - INFO - 处理FAQ 13/75: How Long Can Raw Chicken Be Left Out Of The Fridge...
2025-07-22 06:28:57,560 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:28:57,880 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_895623 "HTTP/2 200 OK"
2025-07-22 06:28:58,206 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:28:58,207 - INFO - 成功导入FAQ 13: How Long Can Raw Chicken Be Left Out Of The Fridge...
2025-07-22 06:28:58,555 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:28:58,874 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:28:59,189 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:28:59,608 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:29:00,019 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:29:00,427 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:29:00,837 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:29:01,247 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:29:01,656 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:01,987 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:29:02,373 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:29:02,783 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:29:03,192 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:29:03,526 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:29:03,909 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:29:04,234 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:29:04,550 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:29:04,937 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:29:05,259 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:29:05,576 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:29:05,901 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:29:06,264 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:29:06,587 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:29:06,983 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:29:07,391 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:29:07,392 - INFO - 处理FAQ 14/75: How Long Can You Keep Thawed Shrimp in the Fridge?...
2025-07-22 06:29:07,723 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:29:08,052 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_322109 "HTTP/2 200 OK"
2025-07-22 06:29:08,384 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:29:08,384 - INFO - 成功导入FAQ 14: How Long Can You Keep Thawed Shrimp in the Fridge?...
2025-07-22 06:29:08,722 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:29:09,134 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:09,462 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:29:09,848 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:29:10,175 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:29:10,494 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:29:10,873 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:29:11,282 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:29:11,599 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:11,918 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:29:12,238 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:29:12,572 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:29:12,893 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:29:13,221 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:29:13,566 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:29:13,883 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:29:14,254 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:29:14,662 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:29:15,071 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:29:15,481 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:29:15,892 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:29:16,302 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:29:16,639 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:29:16,959 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:29:17,327 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:29:17,328 - INFO - 处理FAQ 15/75: Can You Refreeze Thawed Chicken?...
2025-07-22 06:29:17,671 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:29:17,986 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_229806 "HTTP/2 200 OK"
2025-07-22 06:29:18,349 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:29:18,350 - INFO - 成功导入FAQ 15: Can You Refreeze Thawed Chicken?...
2025-07-22 06:29:18,700 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:29:19,067 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:19,390 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:29:19,716 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:29:20,041 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:29:20,397 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:29:20,730 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:29:21,047 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:29:21,366 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:21,721 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:29:22,042 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:29:22,445 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:29:22,857 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:29:23,263 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:29:23,599 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:29:23,926 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:29:24,253 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:29:24,587 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:29:24,921 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:29:25,311 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:29:25,635 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:29:25,959 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:29:26,296 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:29:26,619 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:29:26,961 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:29:26,969 - INFO - 处理FAQ 16/75: How Long Can Cooked Hamburgers Be Left Out at Room...
2025-07-22 06:29:27,296 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:29:27,635 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_281175 "HTTP/2 200 OK"
2025-07-22 06:29:27,975 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:29:27,975 - INFO - 成功导入FAQ 16: How Long Can Cooked Hamburgers Be Left Out at Room...
2025-07-22 06:29:28,383 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:29:28,793 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:29,128 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:29:29,512 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:29:29,919 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:29:30,328 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:29:30,739 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:29:31,148 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:29:31,478 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:31,810 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:29:32,145 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:29:32,479 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:29:32,889 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:29:33,298 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:29:33,709 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:29:34,118 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:29:34,528 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:29:34,940 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:29:35,273 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:29:35,654 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:29:36,063 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:29:36,473 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:29:36,883 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:29:37,292 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:29:37,617 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:29:37,618 - INFO - 处理FAQ 17/75: Do You Have to Wash Bagged Salad That's Been Prewa...
2025-07-22 06:29:37,950 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:29:38,281 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_857407 "HTTP/2 200 OK"
2025-07-22 06:29:38,725 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:29:38,726 - INFO - 成功导入FAQ 17: Do You Have to Wash Bagged Salad That's Been Prewa...
2025-07-22 06:29:39,068 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:29:39,395 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:39,749 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:29:40,159 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:29:40,503 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:29:40,879 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:29:41,220 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:29:41,566 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:29:41,887 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:42,219 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:29:42,557 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:29:42,925 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:29:43,333 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:29:43,743 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:29:44,152 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:29:44,563 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:29:44,894 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:29:45,227 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:29:45,587 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:29:45,997 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:29:46,406 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:29:46,734 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:29:47,050 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:29:47,429 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:29:47,779 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:29:47,780 - INFO - 处理FAQ 18/75: Is it Safe to Eat Canned Food That Has Accidentall...
2025-07-22 06:29:48,149 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:29:48,478 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_632546 "HTTP/2 200 OK"
2025-07-22 06:29:48,863 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:29:48,863 - INFO - 成功导入FAQ 18: Is it Safe to Eat Canned Food That Has Accidentall...
2025-07-22 06:29:49,184 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:29:49,580 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:49,912 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:29:50,240 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:29:50,570 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:29:50,911 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:29:51,305 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:29:51,713 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:29:52,037 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:52,370 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:29:52,697 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:29:53,020 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:29:53,369 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:29:53,778 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:29:54,104 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:29:54,445 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:29:54,803 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:29:55,139 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:29:55,519 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:29:55,859 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:29:56,185 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:29:56,510 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:29:56,836 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:29:57,180 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:29:57,526 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:29:57,527 - INFO - 处理FAQ 19/75: Can You Freeze Pie Crust Before Baking It?...
2025-07-22 06:29:57,854 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:29:58,175 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_379567 "HTTP/2 200 OK"
2025-07-22 06:29:58,506 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:29:58,506 - INFO - 成功导入FAQ 19: Can You Freeze Pie Crust Before Baking It?...
2025-07-22 06:29:58,832 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:29:59,170 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:29:59,490 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:29:59,818 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:30:00,153 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:30:00,486 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:30:00,816 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:30:01,148 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:30:01,473 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:01,821 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:30:02,164 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:30:02,514 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:30:02,894 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:30:03,239 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:30:03,608 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:30:03,961 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:30:04,289 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:30:04,635 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:30:04,969 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:30:05,320 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:30:05,662 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:30:06,004 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:30:06,332 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:30:06,664 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:30:06,989 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:30:06,990 - INFO - 处理FAQ 20/75: How Long Can Cooked Chicken Be Left Out at Room Te...
2025-07-22 06:30:07,340 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:30:07,678 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_674385 "HTTP/2 200 OK"
2025-07-22 06:30:08,009 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:30:08,010 - INFO - 成功导入FAQ 20: How Long Can Cooked Chicken Be Left Out at Room Te...
2025-07-22 06:30:08,379 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:30:08,726 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:09,049 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:30:09,378 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:30:09,705 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:30:10,032 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:30:10,367 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:30:10,711 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:30:11,039 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:11,379 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:30:11,716 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:30:12,043 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:30:12,377 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:30:12,704 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:30:13,035 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:30:13,457 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:30:13,788 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:30:14,112 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:30:14,440 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:30:14,759 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:30:15,180 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:30:15,590 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:30:16,000 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:30:16,408 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:30:16,733 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:30:16,734 - INFO - 处理FAQ 21/75: How Long Can You Leave Frozen Chicken in the Freez...
2025-07-22 06:30:17,048 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:30:17,432 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_489552 "HTTP/2 200 OK"
2025-07-22 06:30:17,767 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:30:17,768 - INFO - 成功导入FAQ 21: How Long Can You Leave Frozen Chicken in the Freez...
2025-07-22 06:30:18,133 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:30:18,467 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:18,866 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:30:19,199 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:30:19,528 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:30:19,845 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:30:20,166 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:30:20,498 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:30:20,821 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:21,151 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:30:21,473 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:30:21,797 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:30:22,121 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:30:22,443 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:30:22,762 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:30:23,086 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:30:23,409 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:30:23,726 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:30:24,050 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:30:24,377 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:30:24,690 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:30:25,014 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:30:25,362 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:30:25,686 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:30:26,002 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:30:26,004 - INFO - 处理FAQ 22/75: How Long Can You Keep Thawed Ground Beef in the Fr...
2025-07-22 06:30:26,318 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:30:26,656 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_835613 "HTTP/2 200 OK"
2025-07-22 06:30:27,000 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:30:27,001 - INFO - 成功导入FAQ 22: How Long Can You Keep Thawed Ground Beef in the Fr...
2025-07-22 06:30:27,365 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:30:27,775 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:28,098 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:30:28,492 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:30:28,820 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:30:29,147 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:30:29,475 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:30:29,793 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:30:30,109 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:30,425 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:30:30,742 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:30:31,072 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:30:31,769 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:30:32,088 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:30:32,422 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:30:32,793 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:30:33,202 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:30:33,525 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:30:33,919 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:30:34,329 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:30:34,738 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:30:35,150 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:30:35,477 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:30:35,864 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:30:36,275 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:30:36,275 - INFO - 处理FAQ 23/75: How Long Can Cooked Steak Be Left Out at Room Temp...
2025-07-22 06:30:36,684 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:30:37,011 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_671413 "HTTP/2 200 OK"
2025-07-22 06:30:37,401 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:30:37,402 - INFO - 成功导入FAQ 23: How Long Can Cooked Steak Be Left Out at Room Temp...
2025-07-22 06:30:37,730 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:30:38,048 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:38,366 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:30:38,706 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:30:39,034 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:30:39,374 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:30:39,705 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:30:40,026 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:30:40,358 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:40,683 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:30:41,008 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:30:41,330 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:30:41,675 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:30:41,995 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:30:42,317 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:30:42,640 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:30:42,983 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:30:43,363 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:30:43,698 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:30:44,018 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:30:44,345 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:30:44,678 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:30:45,008 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:30:45,333 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:30:45,683 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:30:45,685 - INFO - 处理FAQ 24/75: How Long Can You Leave Marinated Chicken in the Fr...
2025-07-22 06:30:46,037 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:30:46,352 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_458323 "HTTP/2 200 OK"
2025-07-22 06:30:46,685 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:30:46,686 - INFO - 成功导入FAQ 24: How Long Can You Leave Marinated Chicken in the Fr...
2025-07-22 06:30:47,019 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:30:47,340 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:47,659 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:30:47,985 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:30:48,312 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:30:48,652 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:30:48,972 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:30:49,289 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:30:49,604 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:49,927 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:30:50,263 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:30:50,581 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:30:50,918 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:30:51,328 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:30:51,724 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:30:52,057 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:30:52,454 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:30:52,865 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:30:53,175 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:30:53,507 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:30:53,833 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:30:54,194 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:30:54,512 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:30:54,913 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:30:55,321 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:30:55,322 - INFO - 处理FAQ 25/75: Do You Have to Thaw Frozen Chicken Before Cooking ...
2025-07-22 06:30:55,731 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:30:56,043 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_437695 "HTTP/2 200 OK"
2025-07-22 06:30:56,429 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:30:56,430 - INFO - 成功导入FAQ 25: Do You Have to Thaw Frozen Chicken Before Cooking ...
2025-07-22 06:30:56,857 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:30:57,178 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:30:57,508 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:30:57,858 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:30:58,213 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:30:58,527 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:30:58,909 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:30:59,315 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:30:59,632 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:00,034 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:31:00,357 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:31:00,672 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:31:00,996 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:31:01,337 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:31:01,668 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:31:02,012 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:31:02,339 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:31:02,695 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:31:03,026 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:31:03,352 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:31:03,677 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:31:03,995 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:31:04,316 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:31:04,649 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:31:04,986 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:31:04,986 - INFO - 处理FAQ 26/75: How Long Can Cooked Hot Dogs Be Left Out at Room T...
2025-07-22 06:31:05,355 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:31:05,687 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_576862 "HTTP/2 200 OK"
2025-07-22 06:31:06,075 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:31:06,075 - INFO - 成功导入FAQ 26: How Long Can Cooked Hot Dogs Be Left Out at Room T...
2025-07-22 06:31:06,483 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:31:06,813 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:07,146 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:31:07,498 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:31:07,923 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:31:08,258 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:31:08,584 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:31:08,928 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:31:09,272 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:09,600 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:31:09,966 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:31:10,328 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hot+dogs "HTTP/2 200 OK"
2025-07-22 06:31:10,655 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hot+dogs%25 "HTTP/2 200 OK"
2025-07-22 06:31:10,998 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:31:11,380 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:31:11,748 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:31:12,072 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:31:12,406 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:31:12,740 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:31:13,138 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:31:13,456 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:31:13,857 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:31:14,190 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:31:14,544 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:31:14,880 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:31:15,289 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:31:15,699 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:31:15,701 - INFO - 处理FAQ 27/75: How Long Can You Leave Marinated Steak in the Frid...
2025-07-22 06:31:16,110 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:31:16,520 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_932279 "HTTP/2 200 OK"
2025-07-22 06:31:16,930 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:31:16,931 - INFO - 成功导入FAQ 27: How Long Can You Leave Marinated Steak in the Frid...
2025-07-22 06:31:17,252 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:31:17,595 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:17,932 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:31:18,274 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:31:18,596 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:31:18,920 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:31:19,248 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:31:19,590 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:31:19,927 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:20,306 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:31:20,718 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:31:21,051 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:31:21,433 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:31:21,766 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:31:22,145 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:31:22,536 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:31:22,853 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:31:23,187 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:31:23,612 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:31:23,954 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:31:24,324 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:31:24,673 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:31:25,000 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:31:25,334 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:31:25,668 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:31:25,671 - INFO - 处理FAQ 28/75: Can You Freeze Cheese Successfully?...
2025-07-22 06:31:26,002 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:31:26,334 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_178916 "HTTP/2 200 OK"
2025-07-22 06:31:26,665 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:31:26,665 - INFO - 成功导入FAQ 28: Can You Freeze Cheese Successfully?...
2025-07-22 06:31:26,999 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:31:27,327 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:27,679 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cheese "HTTP/2 200 OK"
2025-07-22 06:31:28,053 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cheese%25 "HTTP/2 200 OK"
2025-07-22 06:31:28,386 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:31:28,806 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:31:29,215 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:31:29,625 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:31:30,035 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.dairy "HTTP/2 200 OK"
2025-07-22 06:31:30,446 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25dairy%25 "HTTP/2 200 OK"
2025-07-22 06:31:30,798 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:31:31,125 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:31:31,453 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:31,786 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:31:32,120 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:31:32,495 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:31:32,825 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:31:33,150 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:31:33,487 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:31:33,824 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:31:34,151 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:31:34,469 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:31:34,851 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:31:35,180 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:31:35,501 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:31:35,815 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:31:36,159 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:31:36,603 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:31:36,927 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:31:36,928 - INFO - 处理FAQ 29/75: Does Pumpkin Pie Have to be Refrigerated?...
2025-07-22 06:31:37,274 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:31:37,607 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_890719 "HTTP/2 200 OK"
2025-07-22 06:31:37,941 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:31:37,942 - INFO - 成功导入FAQ 29: Does Pumpkin Pie Have to be Refrigerated?...
2025-07-22 06:31:38,275 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:31:38,594 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:38,920 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:31:39,264 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:31:39,601 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:31:39,932 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:31:40,274 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:31:40,612 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:31:40,939 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:41,270 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:31:41,623 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:31:41,971 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ketchup "HTTP/2 200 OK"
2025-07-22 06:31:42,331 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ketchup%25 "HTTP/2 200 OK"
2025-07-22 06:31:42,657 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.mustard "HTTP/2 200 OK"
2025-07-22 06:31:43,041 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25mustard%25 "HTTP/2 200 OK"
2025-07-22 06:31:43,369 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:31:43,697 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:31:44,031 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:31:44,366 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:31:44,694 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:31:45,019 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:31:45,337 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:31:45,682 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:31:46,035 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:31:46,370 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:31:46,709 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:31:47,028 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:31:47,443 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:31:47,791 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:31:47,792 - INFO - 处理FAQ 30/75: Should You Wash Grapes Before Storing Them?...
2025-07-22 06:31:48,120 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:31:48,481 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_304050 "HTTP/2 200 OK"
2025-07-22 06:31:48,880 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:31:48,880 - INFO - 成功导入FAQ 30: Should You Wash Grapes Before Storing Them?...
2025-07-22 06:31:49,286 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:31:49,628 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:49,977 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:31:50,313 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:31:50,645 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:31:50,986 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:31:51,306 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:31:51,631 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.grapes "HTTP/2 200 OK"
2025-07-22 06:31:51,957 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:31:52,302 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:31:52,665 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:31:53,023 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:31:53,343 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:31:53,675 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:31:54,005 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:31:54,339 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:31:54,662 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:31:55,023 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:31:55,347 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:31:55,680 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:31:56,001 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:31:56,450 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:31:56,786 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:31:57,102 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:31:57,478 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:31:57,888 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:31:57,889 - INFO - 处理FAQ 31/75: Is it Better to Store Bread on the Counter or in t...
2025-07-22 06:31:58,298 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:31:58,708 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_761808 "HTTP/2 200 OK"
2025-07-22 06:31:59,042 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:31:59,043 - INFO - 成功导入FAQ 31: Is it Better to Store Bread on the Counter or in t...
2025-07-22 06:31:59,425 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:31:59,736 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:00,090 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.bread "HTTP/2 200 OK"
2025-07-22 06:32:00,448 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25bread%25 "HTTP/2 200 OK"
2025-07-22 06:32:00,790 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:32:01,112 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:32:01,473 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:32:01,794 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:32:02,121 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:32:02,440 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:32:02,773 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:03,108 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:32:03,429 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:32:03,749 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:32:04,072 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:32:04,425 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:32:04,746 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:32:05,075 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:32:05,419 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:32:05,741 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:32:06,182 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:32:06,506 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:32:06,820 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:32:07,136 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:32:07,476 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:32:07,814 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:32:08,135 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:32:08,136 - INFO - 处理FAQ 32/75: Is Bottled Water Still Safe To Drink After the Exp...
2025-07-22 06:32:08,453 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.expiration_dates "HTTP/2 200 OK"
2025-07-22 06:32:08,774 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_220141 "HTTP/2 200 OK"
2025-07-22 06:32:09,152 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:32:09,153 - INFO - 成功导入FAQ 32: Is Bottled Water Still Safe To Drink After the Exp...
2025-07-22 06:32:09,474 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:32:09,819 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:10,161 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:32:10,478 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:32:10,826 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:32:11,153 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:32:11,470 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:32:11,831 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:32:12,153 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:12,495 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:32:12,802 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:32:13,145 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:32:13,555 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:32:13,877 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:32:14,272 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:32:14,682 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:32:15,012 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:32:15,343 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:32:15,664 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:32:15,997 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:32:16,422 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:32:16,769 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:32:17,096 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:32:17,446 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:32:17,856 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:32:17,857 - INFO - 处理FAQ 33/75: Should You Refrigerate Bananas?...
2025-07-22 06:32:18,194 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:32:18,541 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_543936 "HTTP/2 200 OK"
2025-07-22 06:32:18,880 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:32:18,881 - INFO - 成功导入FAQ 33: Should You Refrigerate Bananas?...
2025-07-22 06:32:19,208 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.bananas "HTTP/2 200 OK"
2025-07-22 06:32:19,526 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:32:19,875 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:20,205 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:32:20,514 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:32:20,825 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:32:21,236 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:32:21,647 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:32:21,969 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.fruits "HTTP/2 200 OK"
2025-07-22 06:32:22,361 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25fruits%25 "HTTP/2 200 OK"
2025-07-22 06:32:22,771 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:32:23,087 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:23,416 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:32:23,759 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:32:24,076 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.oranges "HTTP/2 200 OK"
2025-07-22 06:32:24,417 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:32:24,740 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:32:25,063 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:32:25,393 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:32:25,723 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:32:26,069 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:32:26,386 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:32:26,720 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:32:27,051 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:32:27,371 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:32:27,705 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:32:28,036 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:32:28,366 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:32:28,692 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.vegetables "HTTP/2 200 OK"
2025-07-22 06:32:29,023 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:32:29,024 - INFO - 处理FAQ 34/75: Is it OK to Thaw Steak on the Counter?...
2025-07-22 06:32:29,341 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:32:29,665 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_250185 "HTTP/2 200 OK"
2025-07-22 06:32:29,976 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:32:29,976 - INFO - 成功导入FAQ 34: Is it OK to Thaw Steak on the Counter?...
2025-07-22 06:32:30,290 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:32:30,614 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:30,956 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:32:31,372 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:32:31,738 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:32:32,074 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:32:32,483 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:32:32,811 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:32:33,218 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:33,545 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:32:33,879 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:32:34,239 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:32:34,650 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:32:34,959 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:32:35,366 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:32:35,776 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:32:36,117 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:32:36,453 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:32:36,790 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:32:37,125 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:32:37,476 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:32:37,824 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:32:38,550 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:32:38,891 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:32:39,258 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:32:39,259 - INFO - 处理FAQ 35/75: Should You Keep Potatoes in the Fridge?...
2025-07-22 06:32:39,667 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:32:40,079 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_143405 "HTTP/2 200 OK"
2025-07-22 06:32:40,487 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:32:40,487 - INFO - 成功导入FAQ 35: Should You Keep Potatoes in the Fridge?...
2025-07-22 06:32:40,831 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:32:41,203 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:41,613 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:32:41,944 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:32:42,330 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:32:42,739 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:32:43,086 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:32:43,402 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:32:43,763 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:44,174 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:32:44,583 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:32:44,992 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:32:45,402 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:32:45,812 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:32:46,222 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:32:46,631 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:32:46,945 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:32:47,348 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:32:47,710 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:32:48,033 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:32:48,356 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:32:48,781 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:32:49,191 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:32:49,600 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:32:49,945 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:32:49,947 - INFO - 处理FAQ 36/75: Does Vinegar Ever Go Bad?...
2025-07-22 06:32:50,278 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:32:50,601 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_316956 "HTTP/2 200 OK"
2025-07-22 06:32:50,947 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:32:50,948 - INFO - 成功导入FAQ 36: Does Vinegar Ever Go Bad?...
2025-07-22 06:32:51,286 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:32:51,633 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:51,950 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:32:52,277 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:32:52,605 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:32:52,937 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:32:53,314 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:32:53,712 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:32:54,042 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:32:54,365 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:32:54,722 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:32:55,043 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:32:55,439 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:32:55,765 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:32:56,091 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:32:56,418 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:32:56,751 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:32:57,113 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:32:57,436 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.spices "HTTP/2 200 OK"
2025-07-22 06:32:57,769 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25spices%25 "HTTP/2 200 OK"
2025-07-22 06:32:58,101 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:32:58,428 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:32:58,759 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:32:59,081 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:32:59,408 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:32:59,768 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:33:00,108 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.vinegar "HTTP/2 200 OK"
2025-07-22 06:33:00,452 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25vinegar%25 "HTTP/2 200 OK"
2025-07-22 06:33:00,786 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:33:00,787 - INFO - 处理FAQ 37/75: Do Spices Ever Go Bad?...
2025-07-22 06:33:01,116 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:33:01,439 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_543116 "HTTP/2 200 OK"
2025-07-22 06:33:01,786 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:33:01,786 - INFO - 成功导入FAQ 37: Do Spices Ever Go Bad?...
2025-07-22 06:33:02,119 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:33:02,502 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:02,912 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:33:03,242 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:33:03,602 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:33:03,970 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:33:04,348 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:33:04,673 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:33:05,385 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:05,733 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:33:06,055 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:33:06,383 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:33:06,711 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:33:07,036 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:33:07,371 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:33:07,694 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:33:08,017 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:33:08,342 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:33:08,670 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.spices "HTTP/2 200 OK"
2025-07-22 06:33:09,014 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25spices%25 "HTTP/2 200 OK"
2025-07-22 06:33:09,339 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:33:09,672 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:33:10,018 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:33:10,344 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:33:10,692 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:33:11,032 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:33:11,400 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:33:11,403 - INFO - 处理FAQ 38/75: Is Yogurt Still Safe After the Sell-By Date?...
2025-07-22 06:33:11,734 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:33:12,087 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_439891 "HTTP/2 200 OK"
2025-07-22 06:33:12,440 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:33:12,441 - INFO - 成功导入FAQ 38: Is Yogurt Still Safe After the Sell-By Date?...
2025-07-22 06:33:12,764 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:33:13,087 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:13,399 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:33:13,715 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:33:14,095 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:33:14,421 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:33:14,763 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:33:15,102 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:33:15,429 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:15,757 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:33:16,147 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:33:16,484 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.milk "HTTP/2 200 OK"
2025-07-22 06:33:16,807 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25milk%25 "HTTP/2 200 OK"
2025-07-22 06:33:17,138 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:33:17,484 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:33:17,808 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:33:18,140 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:33:18,486 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:33:18,829 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:33:19,188 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:33:19,501 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:33:19,902 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:33:20,318 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:33:20,642 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:33:20,970 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:33:21,291 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:33:21,646 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.yogurt "HTTP/2 200 OK"
2025-07-22 06:33:21,988 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:33:21,988 - INFO - 处理FAQ 39/75: How Can You Tell if an Avocado is Ripe?...
2025-07-22 06:33:22,369 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:33:22,751 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_642671 "HTTP/2 200 OK"
2025-07-22 06:33:23,097 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:33:23,098 - INFO - 成功导入FAQ 39: How Can You Tell if an Avocado is Ripe?...
2025-07-22 06:33:23,495 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.avocado "HTTP/2 200 OK"
2025-07-22 06:33:23,841 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25avocado%25 "HTTP/2 200 OK"
2025-07-22 06:33:24,164 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:33:24,501 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:24,822 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:33:25,235 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:33:25,571 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:33:25,903 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:33:26,232 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:33:26,583 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:33:26,925 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:27,263 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:33:27,593 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:33:28,001 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:33:28,334 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:33:28,647 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:33:28,969 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:33:29,303 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:33:29,626 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:33:30,004 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:33:30,333 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:33:30,655 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:33:30,978 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:33:31,344 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:33:31,663 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:33:32,002 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:33:32,340 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:33:32,342 - INFO - 处理FAQ 40/75: Does Pure Honey Go Bad?...
2025-07-22 06:33:32,666 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:33:33,003 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_642128 "HTTP/2 200 OK"
2025-07-22 06:33:33,353 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:33:33,354 - INFO - 成功导入FAQ 40: Does Pure Honey Go Bad?...
2025-07-22 06:33:33,678 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:33:34,094 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:34,782 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:33:35,429 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:33:35,772 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:33:36,104 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:33:36,419 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:33:36,746 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:33:37,074 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:37,405 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:33:37,729 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:33:38,072 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.honey "HTTP/2 200 OK"
2025-07-22 06:33:38,398 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25honey%25 "HTTP/2 200 OK"
2025-07-22 06:33:38,734 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:33:39,084 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:33:39,593 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:33:39,925 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:33:40,256 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:33:40,577 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:33:40,900 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:33:41,212 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:33:41,527 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:33:41,844 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:33:42,175 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:33:42,493 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:33:42,821 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:33:43,140 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:33:43,142 - INFO - 处理FAQ 41/75: Does Pecan Pie Have to be Refrigerated?...
2025-07-22 06:33:43,475 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:33:43,799 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_116777 "HTTP/2 200 OK"
2025-07-22 06:33:44,110 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:33:44,111 - INFO - 成功导入FAQ 41: Does Pecan Pie Have to be Refrigerated?...
2025-07-22 06:33:44,429 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:33:44,743 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:45,120 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:33:45,474 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:33:45,791 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:33:46,116 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:33:46,437 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:33:46,756 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:33:47,099 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:47,419 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:33:47,742 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:33:48,071 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ketchup "HTTP/2 200 OK"
2025-07-22 06:33:48,390 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ketchup%25 "HTTP/2 200 OK"
2025-07-22 06:33:48,709 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.mustard "HTTP/2 200 OK"
2025-07-22 06:33:49,026 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25mustard%25 "HTTP/2 200 OK"
2025-07-22 06:33:49,376 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:33:49,693 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:33:50,013 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:33:50,343 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:33:50,670 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:33:51,043 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:33:51,357 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:33:51,681 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:33:52,000 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:33:52,320 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:33:52,650 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:33:52,982 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:33:53,310 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:33:53,636 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:33:53,638 - INFO - 处理FAQ 42/75: Is it OK to Refreeze Fish That Has Already Thawed?...
2025-07-22 06:33:53,962 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:33:54,306 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_658507 "HTTP/2 200 OK"
2025-07-22 06:33:54,637 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:33:54,638 - INFO - 成功导入FAQ 42: Is it OK to Refreeze Fish That Has Already Thawed?...
2025-07-22 06:33:54,957 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:33:55,320 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:55,648 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:33:55,978 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:33:56,311 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:33:56,636 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:33:56,969 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:33:57,294 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.fish "HTTP/2 200 OK"
2025-07-22 06:33:57,620 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:33:57,938 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:33:58,257 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:33:58,608 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:33:58,928 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:33:59,246 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:33:59,564 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:33:59,915 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:34:00,263 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:34:00,593 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:34:00,925 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:34:01,276 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:34:01,582 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:34:01,902 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:34:02,243 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:34:02,569 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:34:02,910 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:34:03,327 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:34:03,327 - INFO - 处理FAQ 43/75: How Long Can Raw Ground Beef Be Left Out?...
2025-07-22 06:34:03,671 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:34:04,045 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_9158 "HTTP/2 200 OK"
2025-07-22 06:34:04,375 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:34:04,375 - INFO - 成功导入FAQ 43: How Long Can Raw Ground Beef Be Left Out?...
2025-07-22 06:34:04,761 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:34:05,093 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:05,471 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:34:05,798 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:34:06,111 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:34:06,436 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:34:06,756 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:34:07,098 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:34:07,452 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:07,778 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:34:08,104 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:34:08,431 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:34:08,850 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:34:09,163 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:34:09,488 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:34:09,882 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:34:10,205 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:34:10,533 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:34:10,906 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:34:11,230 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:34:11,554 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:34:11,885 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:34:12,212 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:34:12,544 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:34:12,863 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:34:12,864 - INFO - 处理FAQ 44/75: Do You Have to Refrigerate Opened Bottles of Musta...
2025-07-22 06:34:13,208 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:34:13,522 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_947974 "HTTP/2 200 OK"
2025-07-22 06:34:13,848 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:34:13,848 - INFO - 成功导入FAQ 44: Do You Have to Refrigerate Opened Bottles of Musta...
2025-07-22 06:34:14,165 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:34:14,482 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:14,816 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:34:15,135 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:34:15,516 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:34:15,844 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:34:16,157 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:34:16,471 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:34:16,784 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:17,144 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:34:17,474 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:34:17,869 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ketchup "HTTP/2 200 OK"
2025-07-22 06:34:18,190 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ketchup%25 "HTTP/2 200 OK"
2025-07-22 06:34:18,512 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.mustard "HTTP/2 200 OK"
2025-07-22 06:34:18,831 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25mustard%25 "HTTP/2 200 OK"
2025-07-22 06:34:19,202 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:34:19,530 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:34:19,920 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:34:20,240 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:34:20,576 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:34:20,941 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:34:21,258 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:34:21,613 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:34:21,936 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:34:22,273 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:34:22,591 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:34:22,928 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:34:23,244 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:34:23,579 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:34:23,582 - INFO - 处理FAQ 45/75: Do You Have to Refrigerate Oranges?...
2025-07-22 06:34:23,920 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:34:24,279 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_744596 "HTTP/2 200 OK"
2025-07-22 06:34:24,630 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:34:24,630 - INFO - 成功导入FAQ 45: Do You Have to Refrigerate Oranges?...
2025-07-22 06:34:24,944 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:34:25,273 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:25,598 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:34:25,909 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:34:26,240 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:34:26,555 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:34:26,891 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:34:27,212 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:34:27,542 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:27,905 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:34:28,236 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:34:28,604 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.oranges "HTTP/2 200 OK"
2025-07-22 06:34:28,953 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:34:29,273 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:34:29,592 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:34:29,922 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:34:30,362 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:34:30,710 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:34:31,044 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:34:31,411 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:34:31,749 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:34:32,081 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:34:32,425 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:34:32,820 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:34:33,167 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:34:33,537 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:34:33,537 - INFO - 处理FAQ 46/75: Do You Have to Refrigerate Peanut Butter?...
2025-07-22 06:34:33,907 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:34:34,254 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_272978 "HTTP/2 200 OK"
2025-07-22 06:34:34,584 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:34:34,585 - INFO - 成功导入FAQ 46: Do You Have to Refrigerate Peanut Butter?...
2025-07-22 06:34:34,929 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:34:35,380 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:35,721 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.butter "HTTP/2 200 OK"
2025-07-22 06:34:36,097 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25butter%25 "HTTP/2 200 OK"
2025-07-22 06:34:36,440 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:34:36,779 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:34:37,110 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:34:37,440 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:34:37,802 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:34:38,142 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:34:38,555 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:38,895 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:34:39,219 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:34:39,551 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ketchup "HTTP/2 200 OK"
2025-07-22 06:34:39,887 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ketchup%25 "HTTP/2 200 OK"
2025-07-22 06:34:40,233 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.mustard "HTTP/2 200 OK"
2025-07-22 06:34:40,562 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25mustard%25 "HTTP/2 200 OK"
2025-07-22 06:34:40,914 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.peanut+butter "HTTP/2 200 OK"
2025-07-22 06:34:41,244 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25peanut+butter%25 "HTTP/2 200 OK"
2025-07-22 06:34:41,627 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:34:41,973 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:34:42,305 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:34:42,663 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:34:43,001 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:34:43,347 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:34:43,684 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:34:44,027 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:34:44,391 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:34:44,722 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:34:45,050 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:34:45,374 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:34:45,715 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:34:46,132 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:34:46,133 - INFO - 处理FAQ 47/75: Is Frozen Steak Still OK if the Packaging Has Torn...
2025-07-22 06:34:46,542 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:34:46,870 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_152940 "HTTP/2 200 OK"
2025-07-22 06:34:47,259 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:34:47,259 - INFO - 成功导入FAQ 47: Is Frozen Steak Still OK if the Packaging Has Torn...
2025-07-22 06:34:47,589 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:34:47,926 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:48,250 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:34:48,591 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:34:48,961 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:34:49,300 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:34:49,716 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:34:50,046 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:34:50,373 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:50,699 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:34:51,063 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:34:51,409 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:34:51,740 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:34:52,066 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:34:52,478 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:34:52,829 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:34:53,163 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:34:53,507 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:34:53,841 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:34:54,172 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:34:54,526 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:34:54,852 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:34:55,247 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:34:55,574 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:34:55,915 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:34:55,916 - INFO - 处理FAQ 48/75: How Cold Should Your Freezer Be?...
2025-07-22 06:34:56,272 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:34:56,629 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_954955 "HTTP/2 200 OK"
2025-07-22 06:34:56,986 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:34:56,987 - INFO - 成功导入FAQ 48: How Cold Should Your Freezer Be?...
2025-07-22 06:34:57,396 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:34:57,722 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:34:58,049 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:34:58,381 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:34:58,728 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:34:59,053 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:34:59,388 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:34:59,753 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:35:00,098 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:00,440 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:35:00,786 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:35:01,133 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:35:01,479 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:35:01,835 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:35:02,166 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:35:02,489 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:35:02,821 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:35:03,146 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:35:03,477 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:35:03,847 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:35:04,178 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:35:04,510 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:35:04,860 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:35:05,186 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:35:05,519 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:35:05,519 - INFO - 处理FAQ 49/75: My Olive Oil Has Turned Cloudy: Is it Still OK?...
2025-07-22 06:35:05,844 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:35:06,184 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_649097 "HTTP/2 200 OK"
2025-07-22 06:35:06,511 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:35:06,512 - INFO - 成功导入FAQ 49: My Olive Oil Has Turned Cloudy: Is it Still OK?...
2025-07-22 06:35:06,838 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:35:07,163 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:07,500 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:35:07,832 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:35:08,192 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:35:08,535 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:35:08,873 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:35:09,214 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:35:09,549 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:09,877 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:35:10,220 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:35:10,558 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.olive+oil "HTTP/2 200 OK"
2025-07-22 06:35:10,885 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25olive+oil%25 "HTTP/2 200 OK"
2025-07-22 06:35:11,222 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:35:11,571 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:35:11,887 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:35:12,235 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:35:12,568 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:35:12,898 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:35:13,221 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:35:13,553 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:35:13,888 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:35:14,217 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:35:14,599 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:35:14,956 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:35:15,318 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:35:15,775 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:35:15,776 - INFO - 处理FAQ 50/75: How Long Can Mayonnaise Be Left Out of the Fridge?...
2025-07-22 06:35:16,126 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:35:16,472 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_206393 "HTTP/2 200 OK"
2025-07-22 06:35:16,813 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:35:16,814 - INFO - 成功导入FAQ 50: How Long Can Mayonnaise Be Left Out of the Fridge?...
2025-07-22 06:35:17,145 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:35:17,569 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:17,901 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.butter "HTTP/2 200 OK"
2025-07-22 06:35:18,286 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25butter%25 "HTTP/2 200 OK"
2025-07-22 06:35:18,689 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:35:19,106 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:35:19,444 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:35:19,824 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:35:20,232 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:35:20,584 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:35:20,918 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:21,359 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:35:21,751 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:35:22,076 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ketchup "HTTP/2 200 OK"
2025-07-22 06:35:22,587 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ketchup%25 "HTTP/2 200 OK"
2025-07-22 06:35:22,909 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.mayonnaise "HTTP/2 200 OK"
2025-07-22 06:35:23,235 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25mayonnaise%25 "HTTP/2 200 OK"
2025-07-22 06:35:23,562 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.mustard "HTTP/2 200 OK"
2025-07-22 06:35:23,914 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25mustard%25 "HTTP/2 200 OK"
2025-07-22 06:35:24,246 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.peanut+butter "HTTP/2 200 OK"
2025-07-22 06:35:24,570 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25peanut+butter%25 "HTTP/2 200 OK"
2025-07-22 06:35:24,905 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:35:25,229 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:35:25,548 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:35:25,874 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:35:26,200 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:35:26,537 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:35:26,876 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:35:27,207 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:35:27,604 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:35:27,941 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:35:28,321 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:35:28,651 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:35:28,987 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:35:29,318 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:35:29,318 - INFO - 处理FAQ 51/75: How Long Can Hard-Boiled Eggs Be Left Out Of The F...
2025-07-22 06:35:29,652 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:35:30,064 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_179501 "HTTP/2 200 OK"
2025-07-22 06:35:30,525 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:35:30,525 - INFO - 成功导入FAQ 51: How Long Can Hard-Boiled Eggs Be Left Out Of The F...
2025-07-22 06:35:30,881 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:35:31,204 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:31,524 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cheese "HTTP/2 200 OK"
2025-07-22 06:35:31,847 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cheese%25 "HTTP/2 200 OK"
2025-07-22 06:35:32,152 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:35:32,519 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:35:32,845 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:35:33,195 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:35:33,507 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:35:33,851 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:35:34,260 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:34,589 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:35:34,936 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:35:35,284 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:35:35,612 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:35:35,930 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:35:36,308 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:35:36,643 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:35:37,027 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:35:37,435 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:35:37,845 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:35:38,165 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:35:38,521 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:35:38,866 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:35:39,190 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:35:39,586 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:35:39,925 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:35:39,926 - INFO - 处理FAQ 52/75: I Left Pizza Out Overnight - Is It Still Safe to E...
2025-07-22 06:35:40,258 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:35:40,595 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_795329 "HTTP/2 200 OK"
2025-07-22 06:35:40,918 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:35:40,919 - INFO - 成功导入FAQ 52: I Left Pizza Out Overnight - Is It Still Safe to E...
2025-07-22 06:35:41,247 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:35:41,569 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:41,912 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:35:42,240 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:35:42,657 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:35:42,996 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:35:43,310 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:35:43,630 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:35:43,949 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:44,296 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:35:44,705 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:35:45,115 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:35:45,524 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:35:45,934 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:35:46,343 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:35:46,751 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:35:47,089 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:35:47,472 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:35:47,871 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:35:48,223 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:35:48,599 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:35:48,975 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:35:49,300 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:35:49,630 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:35:49,958 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:35:49,959 - INFO - 处理FAQ 53/75: Should You Rinse Raw Chicken Before Cooking It?...
2025-07-22 06:35:50,337 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:35:50,677 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_35445 "HTTP/2 200 OK"
2025-07-22 06:35:51,013 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:35:51,014 - INFO - 成功导入FAQ 53: Should You Rinse Raw Chicken Before Cooking It?...
2025-07-22 06:35:51,361 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:35:51,685 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:52,057 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:35:52,397 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:35:52,735 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:35:53,099 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:35:53,424 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:35:53,819 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:35:54,159 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:35:54,523 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:35:54,945 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:35:55,273 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:35:55,677 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:35:56,002 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:35:56,334 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:35:56,684 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:35:57,035 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:35:57,403 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:35:57,803 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:35:58,121 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:35:58,458 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:35:58,837 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:35:59,246 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:35:59,570 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:35:59,914 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:35:59,915 - INFO - 处理FAQ 54/75: Can You Safely Drink Milk After the Sell-By Date?...
2025-07-22 06:36:00,271 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:36:00,592 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_989626 "HTTP/2 200 OK"
2025-07-22 06:36:00,987 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:36:00,987 - INFO - 成功导入FAQ 54: Can You Safely Drink Milk After the Sell-By Date?...
2025-07-22 06:36:01,296 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:36:01,704 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:02,024 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:36:02,421 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:36:02,832 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:36:03,151 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:36:03,547 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.dairy "HTTP/2 200 OK"
2025-07-22 06:36:03,881 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25dairy%25 "HTTP/2 200 OK"
2025-07-22 06:36:04,264 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:36:04,608 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:36:04,942 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:05,288 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:36:05,625 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:36:06,107 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.milk "HTTP/2 200 OK"
2025-07-22 06:36:06,517 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25milk%25 "HTTP/2 200 OK"
2025-07-22 06:36:06,930 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:36:07,338 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:36:07,817 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:36:08,268 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:36:08,605 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:36:08,942 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:36:09,282 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:36:09,603 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:36:10,002 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:36:10,311 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:36:10,642 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:36:10,996 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:36:11,432 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:36:11,754 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:36:11,755 - INFO - 处理FAQ 55/75: Do You Have to Store Tomatoes in the Fridge?...
2025-07-22 06:36:12,073 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:36:12,456 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_417995 "HTTP/2 200 OK"
2025-07-22 06:36:12,866 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:36:12,867 - INFO - 成功导入FAQ 55: Do You Have to Store Tomatoes in the Fridge?...
2025-07-22 06:36:13,189 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:36:13,537 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:13,892 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:36:14,299 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:36:14,708 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:36:15,120 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:36:15,529 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:36:15,937 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:36:16,347 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:16,752 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:36:17,084 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:36:17,473 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:36:17,802 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:36:18,134 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:36:18,471 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:36:18,796 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:36:19,129 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:36:19,458 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:36:19,809 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:36:20,162 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:36:20,548 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:36:20,901 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:36:21,364 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:36:21,721 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:36:22,075 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:36:22,076 - INFO - 处理FAQ 56/75: Can You Safely Eat Cooked Chicken That's Still Pin...
2025-07-22 06:36:22,425 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:36:22,762 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_248194 "HTTP/2 200 OK"
2025-07-22 06:36:23,151 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:36:23,152 - INFO - 成功导入FAQ 56: Can You Safely Eat Cooked Chicken That's Still Pin...
2025-07-22 06:36:23,474 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:36:23,836 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:24,166 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:36:24,518 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:36:24,883 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:36:25,227 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:36:25,592 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:36:25,916 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:36:26,259 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:26,625 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:36:26,968 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:36:27,317 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:36:27,638 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:36:27,964 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:36:28,293 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:36:28,641 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:36:29,011 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:36:29,338 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:36:29,672 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:36:30,015 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:36:30,344 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:36:30,713 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:36:31,051 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:36:31,379 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:36:31,727 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:36:31,728 - INFO - 处理FAQ 57/75: Is it Safe to Refreeze Thawed Steak?...
2025-07-22 06:36:32,108 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:36:32,441 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_898392 "HTTP/2 200 OK"
2025-07-22 06:36:32,806 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:36:32,807 - INFO - 成功导入FAQ 57: Is it Safe to Refreeze Thawed Steak?...
2025-07-22 06:36:33,177 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:36:33,498 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:33,859 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:36:34,183 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:36:34,549 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:36:34,887 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:36:35,212 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:36:35,572 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:36:35,910 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:36,244 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:36:36,626 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:36:36,947 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:36:37,339 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:36:37,749 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:36:38,158 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:36:38,530 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:36:38,929 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:36:39,250 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:36:39,696 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:36:40,034 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:36:40,400 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:36:40,735 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:36:41,059 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:36:41,404 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:36:41,846 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:36:41,847 - INFO - 处理FAQ 58/75: How Long Does Thawed Steak Last in the Fridge?...
2025-07-22 06:36:42,182 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:36:42,563 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_742758 "HTTP/2 200 OK"
2025-07-22 06:36:42,967 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:36:42,968 - INFO - 成功导入FAQ 58: How Long Does Thawed Steak Last in the Fridge?...
2025-07-22 06:36:43,295 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:36:43,634 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:43,988 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:36:44,406 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:36:44,740 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:36:45,085 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:36:45,429 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:36:45,768 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:36:46,146 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:46,557 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:36:46,886 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:36:47,210 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:36:47,579 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:36:47,945 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:36:48,277 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:36:48,707 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:36:49,115 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:36:49,525 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:36:49,858 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:36:50,241 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:36:50,651 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:36:51,063 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:36:51,472 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:36:51,881 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:36:52,290 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:36:52,291 - INFO - 处理FAQ 59/75: Can You Freeze Milk Successfully?...
2025-07-22 06:36:52,702 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:36:53,046 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_435286 "HTTP/2 200 OK"
2025-07-22 06:36:53,416 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:36:53,417 - INFO - 成功导入FAQ 59: Can You Freeze Milk Successfully?...
2025-07-22 06:36:53,769 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:36:54,122 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:54,544 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cheese "HTTP/2 200 OK"
2025-07-22 06:36:54,952 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cheese%25 "HTTP/2 200 OK"
2025-07-22 06:36:55,361 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:36:55,706 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:36:56,047 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:36:56,391 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:36:56,727 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.dairy "HTTP/2 200 OK"
2025-07-22 06:36:57,052 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25dairy%25 "HTTP/2 200 OK"
2025-07-22 06:36:57,385 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:36:57,725 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:36:58,074 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:36:58,420 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:36:58,753 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:36:59,105 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.milk "HTTP/2 200 OK"
2025-07-22 06:36:59,458 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25milk%25 "HTTP/2 200 OK"
2025-07-22 06:36:59,791 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:37:00,176 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:37:00,585 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:37:00,994 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:37:01,403 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:37:01,741 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:37:02,078 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:37:02,434 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:37:02,770 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:37:03,108 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:37:03,496 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:37:03,836 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:37:04,272 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:37:04,680 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:37:04,681 - INFO - 处理FAQ 60/75: Is Canned Tuna Still Safe After the Best By Date?...
2025-07-22 06:37:05,009 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.expiration_dates "HTTP/2 200 OK"
2025-07-22 06:37:05,501 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_489903 "HTTP/2 200 OK"
2025-07-22 06:37:05,868 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:37:05,869 - INFO - 成功导入FAQ 60: Is Canned Tuna Still Safe After the Best By Date?...
2025-07-22 06:37:06,320 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:37:06,728 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:37:07,056 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:37:07,445 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:37:07,767 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:37:08,162 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:37:08,571 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:37:08,917 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:37:09,300 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:37:09,698 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:37:10,107 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:37:10,437 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:37:10,824 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:37:11,234 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:37:11,600 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:37:11,936 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:37:12,270 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:37:12,636 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:37:12,985 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:37:13,327 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:37:13,680 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:37:14,003 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:37:14,408 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:37:14,818 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:37:15,227 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:37:15,228 - INFO - 处理FAQ 61/75: Should You Eat Steak That's Turning Brown?...
2025-07-22 06:37:15,560 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:37:15,944 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_350220 "HTTP/2 200 OK"
2025-07-22 06:37:16,354 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:37:16,355 - INFO - 成功导入FAQ 61: Should You Eat Steak That's Turning Brown?...
2025-07-22 06:37:16,716 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:37:17,063 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:37:17,480 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:37:17,890 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:37:18,299 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:37:18,622 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:37:19,016 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:37:19,941 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:37:20,297 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:37:20,656 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:37:21,004 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:37:21,371 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:37:21,753 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:37:22,093 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:37:22,497 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:37:22,912 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:37:23,317 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:37:23,648 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:37:24,019 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:37:24,346 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:37:24,683 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:37:25,020 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:37:25,344 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:37:25,688 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:37:26,016 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:37:26,017 - INFO - 处理FAQ 62/75: I Forgot to Refrigerate Food - Will Reheating Make...
2025-07-22 06:37:26,338 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:37:26,666 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_762210 "HTTP/2 200 OK"
2025-07-22 06:37:27,034 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:37:27,034 - INFO - 成功导入FAQ 62: I Forgot to Refrigerate Food - Will Reheating Make...
2025-07-22 06:37:27,413 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:37:27,824 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:37:28,232 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:37:28,584 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:37:28,923 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:37:29,245 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:37:29,567 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:37:29,909 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:37:30,383 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:37:30,792 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:37:31,202 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:37:31,534 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:37:31,873 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:37:32,328 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:37:32,738 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:37:33,148 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:37:33,557 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:37:33,968 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:37:34,376 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:37:34,695 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:37:35,059 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:37:35,397 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:37:35,733 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:37:36,219 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:37:36,547 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:37:36,548 - INFO - 处理FAQ 63/75: What is a Safe Refrigerator Temperature?...
2025-07-22 06:37:36,869 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:37:37,233 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_28050 "HTTP/2 200 OK"
2025-07-22 06:37:37,572 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:37:37,573 - INFO - 成功导入FAQ 63: What is a Safe Refrigerator Temperature?...
2025-07-22 06:37:37,906 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:37:38,267 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:37:38,677 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:37:39,003 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:37:39,335 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:37:39,674 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:37:40,112 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:37:40,521 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:37:40,933 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:37:41,340 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:37:41,749 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:37:42,059 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.milk "HTTP/2 200 OK"
2025-07-22 06:37:42,536 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25milk%25 "HTTP/2 200 OK"
2025-07-22 06:37:42,867 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:37:43,240 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:37:43,559 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:37:43,903 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:37:44,514 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:37:44,924 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:37:45,268 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:37:45,650 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:37:46,050 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:37:46,460 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:37:46,802 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:37:47,136 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:37:47,586 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:37:47,996 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:37:47,997 - INFO - 处理FAQ 64/75: How Long Can Yogurt Sit Out Before It Becomes Unsa...
2025-07-22 06:37:48,405 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:37:48,768 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_610670 "HTTP/2 200 OK"
2025-07-22 06:37:49,122 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:37:49,122 - INFO - 成功导入FAQ 64: How Long Can Yogurt Sit Out Before It Becomes Unsa...
2025-07-22 06:37:49,532 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:37:49,941 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:37:50,281 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cheese "HTTP/2 200 OK"
2025-07-22 06:37:50,658 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cheese%25 "HTTP/2 200 OK"
2025-07-22 06:37:51,069 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:37:51,580 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:37:51,989 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:37:52,401 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:37:52,809 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:37:53,218 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:37:53,627 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:37:53,938 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:37:54,256 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:37:54,620 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:37:54,941 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:37:55,369 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:37:55,687 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:37:56,022 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:37:56,351 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:37:56,683 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:37:57,098 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:37:57,520 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:37:57,890 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:37:58,236 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:37:58,568 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:37:58,921 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:37:59,234 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.yogurt "HTTP/2 200 OK"
2025-07-22 06:37:59,565 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:37:59,566 - INFO - 处理FAQ 65/75: Does Olive Oil Last Longer if You Store it in the ...
2025-07-22 06:37:59,901 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:38:00,284 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_203331 "HTTP/2 200 OK"
2025-07-22 06:38:00,610 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:38:00,610 - INFO - 成功导入FAQ 65: Does Olive Oil Last Longer if You Store it in the ...
2025-07-22 06:38:01,000 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:38:01,410 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:01,757 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:38:02,101 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:38:02,537 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:38:02,868 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:38:03,254 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:38:03,591 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:38:03,971 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:04,298 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:38:04,655 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:38:05,050 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.olive+oil "HTTP/2 200 OK"
2025-07-22 06:38:05,391 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25olive+oil%25 "HTTP/2 200 OK"
2025-07-22 06:38:05,736 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:38:06,121 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:38:06,531 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:38:06,870 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:38:07,657 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:38:07,987 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:38:08,373 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:38:08,785 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:38:09,132 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:38:09,455 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:38:09,808 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:38:10,137 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:38:10,524 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:38:11,036 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:38:11,037 - INFO - 处理FAQ 66/75: Do You Have to Refrigerate Strawberries?...
2025-07-22 06:38:11,445 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.refrigerated_foods "HTTP/2 200 OK"
2025-07-22 06:38:11,857 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_540754 "HTTP/2 200 OK"
2025-07-22 06:38:12,265 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:38:12,265 - INFO - 成功导入FAQ 66: Do You Have to Refrigerate Strawberries?...
2025-07-22 06:38:12,674 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:38:13,084 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:13,494 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:38:13,855 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:38:14,175 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:38:14,518 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:38:14,930 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:38:15,337 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.fruits "HTTP/2 200 OK"
2025-07-22 06:38:15,658 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25fruits%25 "HTTP/2 200 OK"
2025-07-22 06:38:16,156 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:38:16,565 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:16,907 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:38:17,385 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:38:17,794 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:38:18,204 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:38:18,613 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:38:19,025 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:38:19,433 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:38:19,842 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:38:20,252 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:38:20,662 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:38:21,073 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:38:21,481 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:38:21,819 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:38:22,301 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:38:22,626 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:38:22,962 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:38:22,963 - INFO - 处理FAQ 67/75: Is it Safe to Eat a Burger That's Still Pink Insid...
2025-07-22 06:38:23,325 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:38:23,685 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_176211 "HTTP/2 200 OK"
2025-07-22 06:38:24,019 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:38:24,020 - INFO - 成功导入FAQ 67: Is it Safe to Eat a Burger That's Still Pink Insid...
2025-07-22 06:38:24,368 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:38:24,695 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:25,024 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:38:25,347 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:38:25,666 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:38:25,988 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:38:26,335 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:38:26,652 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:38:26,980 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:27,318 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:38:27,727 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:38:28,137 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hot+dogs "HTTP/2 200 OK"
2025-07-22 06:38:28,546 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hot+dogs%25 "HTTP/2 200 OK"
2025-07-22 06:38:28,877 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:38:29,265 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:38:29,673 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:38:30,082 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:38:30,492 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:38:30,824 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:38:31,168 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:38:31,498 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:38:31,827 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:38:32,180 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:38:32,540 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:38:32,949 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:38:33,361 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:38:33,769 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:38:33,770 - INFO - 处理FAQ 68/75: How Can You Tell if a Watermelon is Ripe?...
2025-07-22 06:38:34,178 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.storage_tips "HTTP/2 200 OK"
2025-07-22 06:38:34,588 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_229075 "HTTP/2 200 OK"
2025-07-22 06:38:34,922 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:38:34,923 - INFO - 成功导入FAQ 68: How Can You Tell if a Watermelon is Ripe?...
2025-07-22 06:38:35,409 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:38:35,767 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:36,124 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:38:36,533 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:38:36,943 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:38:37,269 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:38:37,592 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:38:37,936 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:38:38,377 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:38,726 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:38:39,067 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:38:39,390 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:38:39,810 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:38:40,220 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:38:40,630 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:38:40,966 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:38:41,297 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:38:42,033 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:38:42,370 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:38:42,705 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:38:43,088 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:38:43,497 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:38:43,830 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:38:44,188 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:38:44,539 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:38:44,540 - INFO - 处理FAQ 69/75: How Long Does Thawed Chicken Last in the Fridge?...
2025-07-22 06:38:44,930 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:38:45,246 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_138880 "HTTP/2 200 OK"
2025-07-22 06:38:45,565 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:38:45,566 - INFO - 成功导入FAQ 69: How Long Does Thawed Chicken Last in the Fridge?...
2025-07-22 06:38:45,955 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:38:46,284 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:46,673 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:38:47,013 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:38:47,388 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:38:47,798 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:38:48,207 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:38:48,721 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:38:49,130 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:49,470 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:38:49,846 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:38:50,255 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:38:50,665 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:38:51,074 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:38:51,486 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:38:51,893 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:38:52,304 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:38:52,712 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:38:53,123 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:38:53,532 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:38:53,890 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:38:54,237 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:38:54,568 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:38:54,966 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:38:55,375 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:38:55,376 - INFO - 处理FAQ 70/75: Can You Freeze Raw Eggs for Later Use?...
2025-07-22 06:38:55,726 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:38:56,195 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_538163 "HTTP/2 200 OK"
2025-07-22 06:38:56,536 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:38:56,537 - INFO - 成功导入FAQ 70: Can You Freeze Raw Eggs for Later Use?...
2025-07-22 06:38:56,913 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:38:57,238 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:38:57,578 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:38:57,929 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:38:58,273 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:38:58,609 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:38:58,965 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:38:59,369 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:38:59,778 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:00,188 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:39:00,526 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:39:00,855 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:39:01,213 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:39:01,571 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:39:01,897 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:39:02,236 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:39:02,645 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:39:03,057 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:39:03,465 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:39:03,875 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:39:04,284 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:39:04,694 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:39:05,010 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:39:05,411 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:39:05,742 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:39:05,743 - INFO - 处理FAQ 71/75: Can You Safely Reuse Marinade?...
2025-07-22 06:39:06,074 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:39:06,403 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_305106 "HTTP/2 200 OK"
2025-07-22 06:39:06,720 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:39:06,720 - INFO - 成功导入FAQ 71: Can You Safely Reuse Marinade?...
2025-07-22 06:39:07,040 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:39:07,459 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:07,869 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:39:08,278 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:39:08,688 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:39:09,013 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:39:09,343 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:39:09,658 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:39:10,018 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:10,430 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:39:10,838 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:39:11,249 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:39:11,657 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:39:11,981 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:39:12,374 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:39:12,699 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:39:13,090 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.seafood "HTTP/2 200 OK"
2025-07-22 06:39:13,500 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25seafood%25 "HTTP/2 200 OK"
2025-07-22 06:39:13,911 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:39:14,321 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:39:14,729 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:39:15,050 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:39:15,446 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:39:15,766 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:39:16,090 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:39:16,414 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:39:16,739 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:39:16,740 - INFO - 处理FAQ 72/75: Is Raw Ground Beef OK If It's Turning Brown?...
2025-07-22 06:39:17,056 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.food_safety "HTTP/2 200 OK"
2025-07-22 06:39:17,385 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_225415 "HTTP/2 200 OK"
2025-07-22 06:39:17,718 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:39:17,719 - INFO - 成功导入FAQ 72: Is Raw Ground Beef OK If It's Turning Brown?...
2025-07-22 06:39:18,054 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:39:18,403 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:18,825 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:39:19,147 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:39:19,475 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:39:19,789 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:39:20,117 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:39:20,450 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:39:20,774 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:21,095 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:39:21,418 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:39:21,742 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:39:22,064 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:39:22,409 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:39:22,818 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:39:23,228 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:39:23,569 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:39:23,920 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:39:24,240 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:39:24,549 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:39:24,910 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:39:25,276 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:39:25,601 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:39:25,925 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:39:26,247 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:39:26,248 - INFO - 处理FAQ 73/75: How Long Does a Thawed Ham Last in the Fridge?...
2025-07-22 06:39:26,572 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:39:26,958 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_252953 "HTTP/2 200 OK"
2025-07-22 06:39:27,276 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:39:27,277 - INFO - 成功导入FAQ 73: How Long Does a Thawed Ham Last in the Fridge?...
2025-07-22 06:39:27,624 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:39:27,939 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:28,269 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:39:28,591 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:39:28,921 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:39:29,247 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:39:29,586 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:39:29,933 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:39:30,294 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:30,705 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:39:31,142 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:39:31,468 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:39:31,793 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:39:32,120 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:39:32,444 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:39:32,762 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:39:33,082 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:39:33,398 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:39:33,707 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:39:34,027 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:39:34,390 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:39:34,718 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:39:35,048 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:39:35,414 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:39:35,786 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:39:35,788 - INFO - 处理FAQ 74/75: How Long Can You Leave Frozen Ground Beef in the F...
2025-07-22 06:39:36,121 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:39:36,456 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_591221 "HTTP/2 200 OK"
2025-07-22 06:39:36,781 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:39:36,782 - INFO - 成功导入FAQ 74: How Long Can You Leave Frozen Ground Beef in the F...
2025-07-22 06:39:37,107 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:39:37,462 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:37,875 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:39:38,205 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:39:38,567 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:39:38,899 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:39:39,236 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:39:39,550 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:39:39,895 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:40,241 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:39:40,573 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:39:40,896 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:39:41,252 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:39:41,582 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:39:41,927 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:39:42,245 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:39:42,581 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:39:42,993 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:39:43,328 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:39:43,662 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:39:44,004 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:39:44,322 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:39:44,669 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:39:45,005 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:39:45,349 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:39:45,350 - INFO - 处理FAQ 75/75: Can You Cook a Frozen Ham Without Thawing It First...
2025-07-22 06:39:45,681 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_categories?select=id&name=eq.frozen_foods "HTTP/2 200 OK"
2025-07-22 06:39:46,005 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id&external_id=eq.stilltasty_faq_798685 "HTTP/2 200 OK"
2025-07-22 06:39:46,330 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs "HTTP/2 201 Created"
2025-07-22 06:39:46,331 - INFO - 成功导入FAQ 75: Can You Cook a Frozen Ham Without Thawing It First...
2025-07-22 06:39:46,694 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.beef "HTTP/2 200 OK"
2025-07-22 06:39:47,023 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:47,356 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.chicken "HTTP/2 200 OK"
2025-07-22 06:39:47,681 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25chicken%25 "HTTP/2 200 OK"
2025-07-22 06:39:48,001 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.cranberry "HTTP/2 200 OK"
2025-07-22 06:39:48,317 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25cranberry%25 "HTTP/2 200 OK"
2025-07-22 06:39:48,641 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.eggs "HTTP/2 200 OK"
2025-07-22 06:39:48,975 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.ground+beef "HTTP/2 200 OK"
2025-07-22 06:39:49,296 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25ground+beef%25 "HTTP/2 200 OK"
2025-07-22 06:39:49,614 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.hamburger "HTTP/2 200 OK"
2025-07-22 06:39:49,942 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25hamburger%25 "HTTP/2 200 OK"
2025-07-22 06:39:50,269 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.pie "HTTP/2 200 OK"
2025-07-22 06:39:50,642 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25pie%25 "HTTP/2 200 OK"
2025-07-22 06:39:50,982 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.potatoes "HTTP/2 200 OK"
2025-07-22 06:39:51,316 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.salad "HTTP/2 200 OK"
2025-07-22 06:39:51,645 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25salad%25 "HTTP/2 200 OK"
2025-07-22 06:39:51,996 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.shrimp "HTTP/2 200 OK"
2025-07-22 06:39:52,331 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25shrimp%25 "HTTP/2 200 OK"
2025-07-22 06:39:52,673 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.steak "HTTP/2 200 OK"
2025-07-22 06:39:53,032 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25steak%25 "HTTP/2 200 OK"
2025-07-22 06:39:53,360 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.stuffing "HTTP/2 200 OK"
2025-07-22 06:39:53,683 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25stuffing%25 "HTTP/2 200 OK"
2025-07-22 06:39:54,031 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&search_key=eq.turkey "HTTP/2 200 OK"
2025-07-22 06:39:54,381 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/foods?select=id&name=ilike.%25turkey%25 "HTTP/2 200 OK"
2025-07-22 06:39:54,736 - INFO - HTTP Request: POST https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?columns=%22faq_id%22%2C%22food_id%22%2C%22relevance_score%22 "HTTP/2 201 Created"
2025-07-22 06:39:54,738 - INFO - FAQ数据导入完成！
2025-07-22 06:39:54,738 - INFO -   成功导入: 75 个
2025-07-22 06:39:54,738 - INFO -   跳过: 0 个
2025-07-22 06:39:54,738 - INFO -   错误: 0 个
2025-07-22 06:39:55,075 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=id "HTTP/2 200 OK"
2025-07-22 06:39:55,401 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faqs?select=category_id%2Cfaq_categories%28name%2Cname_zh%29 "HTTP/2 200 OK"
2025-07-22 06:39:55,732 - INFO - HTTP Request: GET https://plefidqreqjnesamigoc.supabase.co/rest/v1/faq_food_relations?select=id "HTTP/2 206 Partial Content"
2025-07-22 06:39:55,734 - INFO - 
📊 导入验证结果:
2025-07-22 06:39:55,734 - INFO -   总FAQ数: 75
2025-07-22 06:39:55,734 - INFO -   FAQ-食物关联数: 1676
2025-07-22 06:39:55,734 - INFO -   分类分布:
2025-07-22 06:39:55,734 - INFO -     保存技巧: 1 个
2025-07-22 06:39:55,734 - INFO -     保质期: 3 个
2025-07-22 06:39:55,735 - INFO -     冷冻食品: 25 个
2025-07-22 06:39:55,735 - INFO -     冷藏食品: 20 个
2025-07-22 06:39:55,735 - INFO -     食品处理: 1 个
2025-07-22 06:39:55,735 - INFO -     食品安全: 25 个

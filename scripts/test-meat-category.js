#!/usr/bin/env node

/**
 * 测试肉类分类的分页
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function testMeatCategoryPagination() {
  console.log('=== 测试肉类分类分页 ===\n');

  // 肉类对应的数据库分类ID
  const meatCategoryIds = [3, 18];
  
  // 获取每个分类的数量
  for (const categoryId of meatCategoryIds) {
    const { count } = await supabase
      .from('foods')
      .select('*', { count: 'exact', head: true })
      .eq('category_id', categoryId);
    
    console.log(`分类ID ${categoryId}: ${count} 个食物`);
  }
  
  // 获取总数（使用 .in 查询）
  const { count: total } = await supabase
    .from('foods')
    .select('*', { count: 'exact', head: true })
    .in('category_id', meatCategoryIds);
  
  console.log(`\n肉类总数: ${total}`);
  
  // 模拟分页查询
  const pageSize = 24;
  const totalPages = Math.ceil(total / pageSize);
  
  console.log(`每页: ${pageSize}`);
  console.log(`总页数: ${totalPages}`);
  
  // 测试每页的 hasMore 值
  console.log('\n分页测试:');
  for (let page = 1; page <= totalPages + 1; page++) {
    const offset = (page - 1) * pageSize;
    
    const { data } = await supabase
      .from('foods')
      .select('name')
      .in('category_id', meatCategoryIds)
      .order('name')
      .range(offset, offset + pageSize - 1);
    
    const hasMore = (page * pageSize) < total;
    console.log(`第${page}页: 获取${data?.length || 0}条数据, hasMore=${hasMore}`);
  }
}

testMeatCategoryPagination().catch(console.error);
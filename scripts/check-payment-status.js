#!/usr/bin/env node

/**
 * 检查支付状态的脚本
 * 使用方法: node scripts/check-payment-status.js <order_no>
 */

const orderNo = process.argv[2];

if (!orderNo) {
  console.error('❌ 请提供订单号');
  console.log('使用方法: node scripts/check-payment-status.js <order_no>');
  process.exit(1);
}

const apiUrl = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';

console.log('🔍 检查订单状态...');
console.log(`📋 订单号: ${orderNo}`);
console.log('');

// 1. 检查订单状态
async function checkOrderStatus() {
  console.log('1️⃣ 查询订单状态...');
  
  try {
    const response = await fetch(`${apiUrl}/api/order/status?order_no=${orderNo}`, {
      headers: {
        // 如果需要认证，这里添加 cookie 或 token
      }
    });
    
    const data = await response.json();
    
    if (data.code === 0) {
      const order = data.data.order;
      console.log('✅ 订单信息:');
      console.log(`   - 状态: ${order.status}`);
      console.log(`   - 金额: ${order.currency} ${order.amount / 100}`);
      console.log(`   - 额度: ${order.credits}`);
      console.log(`   - 用户: ${order.user_email}`);
      
      if (order.paid_at) {
        console.log(`   - 支付时间: ${order.paid_at}`);
      }
      
      return order;
    } else {
      console.error('❌ 查询失败:', data.message);
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
  
  return null;
}

// 2. 手动触发支付完成（仅用于测试）
async function completeOrderManually() {
  console.log('');
  console.log('2️⃣ 尝试手动完成订单（测试模式）...');
  
  try {
    const response = await fetch(`${apiUrl}/api/test-order-complete?order_no=${orderNo}`);
    const data = await response.json();
    
    if (data.code === 0) {
      console.log('✅ 订单已手动完成');
      console.log(`   - 增加额度: ${data.data.credits_added}`);
      return true;
    } else {
      console.error('❌ 操作失败:', data.message);
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
  
  return false;
}

// 主流程
async function main() {
  // 检查订单状态
  const order = await checkOrderStatus();
  
  if (!order) {
    console.log('');
    console.log('💡 提示: 确保订单号正确，并且你有权限查看该订单');
    return;
  }
  
  if (order.status === 'created') {
    console.log('');
    console.log('⚠️  订单还未支付或回调未触发');
    console.log('');
    console.log('可能的原因:');
    console.log('1. Creem 回调还未触发（等待几秒再试）');
    console.log('2. Webhook URL 配置错误');
    console.log('3. 回调请求被防火墙拦截');
    console.log('');
    
    // 询问是否手动完成
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    readline.question('是否手动完成订单？(仅用于测试) [y/N]: ', async (answer) => {
      if (answer.toLowerCase() === 'y') {
        const success = await completeOrderManually();
        if (success) {
          console.log('');
          console.log('🎉 测试完成！请检查:');
          console.log('1. 访问"我的订单"页面查看订单');
          console.log('2. 访问"我的额度"页面查看额度变化');
        }
      }
      readline.close();
    });
  } else if (order.status === 'paid') {
    console.log('');
    console.log('🎉 订单已支付成功！');
    console.log('');
    console.log('下一步:');
    console.log('1. 访问"我的订单"页面查看订单');
    console.log('2. 访问"我的额度"页面查看额度变化');
  }
}

main();
#!/usr/bin/env node

/**
 * 测试所有类别页面的国际化
 */

const categories = [
  'fruits',
  'vegetables',
  'meat',
  'seafood',
  'dairy',
  'grains',
  'beverages',
  'snacks',
  'condiments',
  'spices'
];

console.log('=== 类别页面国际化测试 ===\n');

console.log('测试URL：');
console.log('中文版：');
categories.forEach(cat => {
  console.log(`  http://localhost:3001/zh/category/${cat}`);
});

console.log('\n英文版：');
categories.forEach(cat => {
  console.log(`  http://localhost:3001/en/category/${cat}`);
});

console.log('\n请检查以下内容：');
console.log('1. 页面标题是否正确显示当前语言');
console.log('2. 面包屑导航是否使用正确的语言');
console.log('3. 排序选项是否使用正确的语言');
console.log('4. 分页按钮（上一页/下一页）是否使用正确的语言');
console.log('5. 食物卡片中的"冷藏"、"冷冻"、"常温"、"查看详情"是否使用正确的语言');
console.log('6. 无数据时的提示文本是否使用正确的语言');
console.log('7. 点击链接是否保持在同一语言版本');

console.log('\n已知问题修复：');
console.log('✓ 硬编码的中文文本已替换为国际化文本');
console.log('✓ 所有链接已添加正确的语言前缀');
console.log('✓ FoodCard 组件已改为服务端组件以支持国际化');
console.log('✓ 面包屑导航已更新为使用国际化文本');
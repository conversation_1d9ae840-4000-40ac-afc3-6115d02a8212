-- 创建posts表
CREATE TABLE IF NOT EXISTS posts (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255),
    title VARCHAR(255),
    description TEXT,
    content TEXT,
    created_at timestamptz DEFAULT NOW(),
    updated_at timestamptz DEFAULT NOW(),
    status VARCHAR(50),
    cover_url VARCHAR(255),
    author_name VA<PERSON>HAR(255),
    author_avatar_url VARCHAR(255),
    locale VARCHAR(50),
    UNIQUE(slug, locale) -- 确保同一语言下slug唯一
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_posts_locale ON posts(locale);
CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_posts_slug_locale ON posts(slug, locale);

-- 添加注释
COMMENT ON TABLE posts IS '博客文章表';
COMMENT ON COLUMN posts.uuid IS '文章唯一标识符';
COMMENT ON COLUMN posts.slug IS 'URL友好的文章标识';
COMMENT ON COLUMN posts.title IS '文章标题';
COMMENT ON COLUMN posts.description IS '文章摘要或描述';
COMMENT ON COLUMN posts.content IS '文章内容（Markdown格式）';
COMMENT ON COLUMN posts.status IS '文章状态：online, offline, deleted等';
COMMENT ON COLUMN posts.locale IS '语言代码：en, zh等';
#!/usr/bin/env node
/**
 * 检查数据库约束
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const SUPABASE_URL = "https://plefidqreqjnesamigoc.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZWZpZHFyZXFqbmVzYW1pZ29jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMTQ1ODUsImV4cCI6MjA2NTY5MDU4NX0.Ys99vv5Xys8np6rskFj_7TV7pTBKpn5UVj8Fn9ZBDtc";

// 创建 Supabase 客户端
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkConstraints() {
  console.log('🔍 检查数据库约束...');
  
  try {
    // 检查foods表结构
    const { data: tableInfo, error: tableError } = await supabase
      .from('foods')
      .select('*')
      .limit(1);
    
    if (tableError) {
      console.error('获取表信息失败:', tableError);
      return;
    }
    
    console.log('✅ foods表连接成功');
    
    // 检查现有的source值
    const { data: sourceValues, error: sourceError } = await supabase
      .from('foods')
      .select('source')
      .not('source', 'is', null);
    
    if (sourceError) {
      console.error('获取source值失败:', sourceError);
      return;
    }
    
    const uniqueSources = [...new Set(sourceValues.map(item => item.source))];
    console.log('📊 现有的source值:', uniqueSources);
    
    // 检查分类
    const { data: categories, error: catError } = await supabase
      .from('food_categories')
      .select('id, name');
    
    if (catError) {
      console.error('获取分类失败:', catError);
      return;
    }
    
    console.log('📂 现有分类:');
    categories.forEach(cat => {
      console.log(`  ${cat.id}: ${cat.name}`);
    });
    
    // 测试插入一个StillTasty数据
    console.log('\n🧪 测试插入StillTasty数据...');
    
    const testData = {
      name: 'Test Apple',
      search_key: 'test_apple',
      category_id: categories.find(c => c.name === 'Fruits')?.id || 1,
      room_temperature_days: 7,
      refrigerated_days: 14,
      frozen_days: 365,
      storage_tips: ['Test tip'],
      source: 'StillTasty',
      confidence: 0.95
    };
    
    const { data: insertResult, error: insertError } = await supabase
      .from('foods')
      .insert(testData)
      .select();
    
    if (insertError) {
      console.error('❌ 插入测试数据失败:', insertError);
      console.log('错误详情:', insertError.details);
      console.log('错误提示:', insertError.hint);
      console.log('错误代码:', insertError.code);
    } else {
      console.log('✅ 测试数据插入成功:', insertResult);
      
      // 删除测试数据
      await supabase
        .from('foods')
        .delete()
        .eq('id', insertResult[0].id);
      console.log('🗑️ 测试数据已删除');
    }
    
  } catch (error) {
    console.error('检查约束时出错:', error);
  }
}

// 运行检查
checkConstraints();

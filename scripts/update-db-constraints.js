#!/usr/bin/env node
/**
 * 更新数据库约束以支持StillTasty数据源
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const SUPABASE_URL = "https://plefidqreqjnesamigoc.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZWZpZHFyZXFqbmVzYW1pZ29jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMTQ1ODUsImV4cCI6MjA2NTY5MDU4NX0.Ys99vv5Xys8np6rskFj_7TV7pTBKpn5UVj8Fn9ZBDtc";

// 创建 Supabase 客户端
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function updateConstraints() {
  console.log('🔧 更新数据库约束以支持StillTasty数据源...');
  
  try {
    // 首先删除现有的约束
    console.log('1. 删除现有的source检查约束...');
    const { error: dropError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE foods DROP CONSTRAINT IF EXISTS foods_source_check;'
    });
    
    if (dropError) {
      console.error('删除约束失败:', dropError);
      // 尝试直接执行SQL
      console.log('尝试使用原生SQL...');
    }
    
    // 添加新的约束，允许USDA和StillTasty
    console.log('2. 添加新的source检查约束...');
    const { error: addError } = await supabase.rpc('exec_sql', {
      sql: `ALTER TABLE foods ADD CONSTRAINT foods_source_check 
            CHECK (source IN ('USDA', 'StillTasty', 'AI'));`
    });
    
    if (addError) {
      console.error('添加约束失败:', addError);
      console.log('可能需要手动在Supabase控制台中执行以下SQL:');
      console.log('ALTER TABLE foods DROP CONSTRAINT IF EXISTS foods_source_check;');
      console.log(`ALTER TABLE foods ADD CONSTRAINT foods_source_check 
                   CHECK (source IN ('USDA', 'StillTasty', 'AI'));`);
      return;
    }
    
    console.log('✅ 约束更新成功!');
    
    // 测试新约束
    console.log('3. 测试新约束...');
    const testData = {
      name: 'Test Apple StillTasty',
      search_key: 'test_apple_stilltasty',
      category_id: 15, // Fruits
      room_temperature_days: 7,
      refrigerated_days: 14,
      frozen_days: 365,
      storage_tips: ['Test tip for StillTasty'],
      source: 'StillTasty',
      confidence: 0.95
    };
    
    const { data: insertResult, error: insertError } = await supabase
      .from('foods')
      .insert(testData)
      .select();
    
    if (insertError) {
      console.error('❌ 测试插入失败:', insertError);
    } else {
      console.log('✅ 测试插入成功!');
      
      // 删除测试数据
      await supabase
        .from('foods')
        .delete()
        .eq('id', insertResult[0].id);
      console.log('🗑️ 测试数据已删除');
    }
    
  } catch (error) {
    console.error('更新约束时出错:', error);
  }
}

// 运行更新
updateConstraints();

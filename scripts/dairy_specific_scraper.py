#!/usr/bin/env python3
"""
专门针对奶制品分类的EatByDate.com爬虫
基于网页结构分析，专门提取奶制品分类页面的所有食物链接
"""

import os
import json
import csv
import time
import random
import logging
from datetime import datetime
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dairy_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DairySpecificScraper:
    def __init__(self, headless=True):
        """初始化爬虫"""
        self.base_url = "https://eatbydate.com"
        self.driver = self._setup_driver(headless)
        self.scraped_data = []
        
        # 基于网页分析的奶制品URL列表
        self.dairy_urls = [
            # A-E 组
            "https://eatbydate.com/dairy/milk/soy-rice-almond-milk-substitutes-shelf-life-expiration-date/",  # Almond Milk
            "https://eatbydate.com/dairy/cheese/cheese-shelf-life-expiration-date/",  # Asiago Cheese
            "https://eatbydate.com/dairy/milk/how-long-does-infant-formula-last-shelf-life-expiration-date/",  # Baby Formula
            "https://eatbydate.com/dairy/spreads/butter-shelf-life-expiration-date/",  # Butter
            "https://eatbydate.com/dairy/cheese/brie-feta-mozzarella-soft-cheese-shelf-life-expiration-date/",  # Brie Cheese
            "https://eatbydate.com/dairy/cheese/cheese-shelf-life-expiration-date/",  # Cheddar Cheese
            "https://eatbydate.com/dairy/milk/soy-rice-almond-milk-substitutes-shelf-life-expiration-date/",  # Coconut Milk
            "https://eatbydate.com/dairy/milk/dairy-coffee-cream-shelf-life-expiration-date/",  # Coffee Cream
            "https://eatbydate.com/dairy/milk/how-long-does-coffee-mate-last-shelf-life/",  # Coffee-Mate
            "https://eatbydate.com/dairy/cheese/cottage-cheese-shelf-life-expiration-date/",  # Cottage Cheese
            "https://eatbydate.com/dairy/milk/dairy-coffee-cream-shelf-life-expiration-date/",  # Cream
            "https://eatbydate.com/dairy/cheese/cream-cheese-shelf-life-expiration-date/",  # Cream Cheese
            "https://eatbydate.com/dairy/milk/how-long-does-egg-nog-last-shelf-life-expiration-date/",  # Egg Nog
            "https://eatbydate.com/eggs-shelf-life-expiration-date/",  # Eggs
            "https://eatbydate.com/dairy/milk/how-long-does-evaporated-milk-last-shelf-life/",  # Evaporated Milk
            
            # F-L 组
            "https://eatbydate.com/dairy/cheese/brie-feta-mozzarella-soft-cheese-shelf-life-expiration-date/",  # Feta Cheese
            "https://eatbydate.com/dairy/yogurt-shelf-life-expiration-date/",  # Frozen Yogurt
            "https://eatbydate.com/dairy/yogurt-shelf-life-expiration-date/",  # Greek Yogurt
            "https://eatbydate.com/dairy/milk/dairy-coffee-cream-shelf-life-expiration-date/",  # Half and Half
            "https://eatbydate.com/hard-boiled-eggs-shelf-life-expiration-date/",  # Hard Boiled Eggs
            "https://eatbydate.com/dairy/cheese/brie-feta-mozzarella-soft-cheese-shelf-life-expiration-date/",  # Havarti Cheese
            "https://eatbydate.com/dairy/ice-cream-shelf-life-expiration-date/",  # Ice Cream
            
            # M-R 组
            "https://eatbydate.com/dairy/spreads/how-long-does-margarine-last-shelf-life-expiration-date/",  # Margarine
            "https://eatbydate.com/dairy/milk/milk-shelf-life-expiration-date/",  # Milk (各种类型)
            "https://eatbydate.com/dairy/cheese/brie-feta-mozzarella-soft-cheese-shelf-life-expiration-date/",  # Mozzarella Cheese
            "https://eatbydate.com/dairy/cheese/brie-feta-mozzarella-soft-cheese-shelf-life-expiration-date/",  # Muenster Cheese
            "https://eatbydate.com/dairy/cheese/cream-cheese-shelf-life-expiration-date/",  # Neufchatel Cheese
            "https://eatbydate.com/dairy/cheese/cheese-shelf-life-expiration-date/",  # Parmesan Cheese
            "https://eatbydate.com/dairy/milk/how-long-does-powdered-milk-last-shelf-life/",  # Powdered Milk
            "https://eatbydate.com/dairy/pudding-shelf-life-expiration-date/",  # Pudding
            
            # S-Z 组
            "https://eatbydate.com/dairy/milk/soy-rice-almond-milk-substitutes-shelf-life-expiration-date/",  # Rice Milk
            "https://eatbydate.com/dairy/cheese/cottage-cheese-shelf-life-expiration-date/",  # Ricotta Cheese
            "https://eatbydate.com/dairy/cheese/cheese-shelf-life-expiration-date/",  # Romano Cheese
            "https://eatbydate.com/dairy/sour-cream-shelf-life-expiration-date/",  # Sour Cream
            "https://eatbydate.com/dairy/milk/soy-rice-almond-milk-substitutes-shelf-life-expiration-date/",  # Soy Milk
            "https://eatbydate.com/dairy/milk/how-long-does-condensed-milk-last-shelf-life/",  # Sweetened Condensed Milk
            "https://eatbydate.com/dairy/cheese/cheese-shelf-life-expiration-date/",  # Swiss Cheese
            "https://eatbydate.com/dairy/milk/whipped-cream/",  # Whipping Cream
            "https://eatbydate.com/dairy/yogurt-shelf-life-expiration-date/",  # Yogurt
            "https://eatbydate.com/dairy/milk/kefir-shelf-life-expiration-date/",  # Kefir
            "https://eatbydate.com/dairy/milk/buttermilk/",  # Buttermilk
        ]

    def _setup_driver(self, headless=True):
        """设置Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)
            return driver
        except Exception as e:
            logger.error(f"Failed to setup Chrome driver: {e}")
            raise

    def extract_food_data(self, url):
        """从食物页面提取数据"""
        try:
            logger.info(f"Extracting data from: {url}")
            self.driver.get(url)
            time.sleep(3)
            
            # 获取页面源码
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # 提取食物名称
            name = self.extract_food_name(soup, url)
            if not name:
                logger.warning(f"Could not extract food name from {url}")
                return None
            
            # 提取保质期数据
            storage_conditions = self.extract_storage_conditions(soup)
            
            # 提取描述
            description = self.extract_description(soup)
            
            # 构建数据结构
            food_data = {
                "name": name,
                "category": "dairy",
                "category_zh": "奶制品",
                "storage_conditions": storage_conditions,
                "description": description,
                "source": {
                    "name": "EatByDate.com",
                    "url": url,
                    "scraped_at": datetime.now().isoformat(),
                    "confidence": "medium"
                }
            }
            
            return food_data
            
        except Exception as e:
            logger.error(f"Error extracting data from {url}: {str(e)}")
            return None

    def extract_food_name(self, soup, url):
        """提取食物名称"""
        # 方法1: 从标题提取
        title_tag = soup.find('title')
        if title_tag:
            title = title_tag.get_text().strip()
            # 清理标题，提取食物名称
            if 'How Long' in title:
                # "How Long Do Apples Last" -> "Apples"
                parts = title.split()
                for i, part in enumerate(parts):
                    if part.lower() in ['do', 'does']:
                        if i + 1 < len(parts):
                            name_part = parts[i + 1]
                            return name_part.replace('?', '').strip()
            elif 'Shelf Life' in title:
                # "Apples - Shelf Life" -> "Apples"
                name = title.split('-')[0].strip()
                if name:
                    return name
        
        # 方法2: 从URL提取
        path = urlparse(url).path
        if path:
            # 从URL路径提取可能的食物名称
            parts = path.strip('/').split('/')
            for part in parts:
                if any(keyword in part for keyword in ['shelf-life', 'expiration', 'how-long']):
                    # 提取食物名称部分
                    name_parts = part.replace('-shelf-life', '').replace('-expiration-date', '').replace('how-long-do-', '').replace('how-long-does-', '').replace('-last', '')
                    name = name_parts.replace('-', ' ').title()
                    if name and len(name) > 2:
                        return name
        
        # 方法3: 从h1标签提取
        h1_tag = soup.find('h1')
        if h1_tag:
            h1_text = h1_tag.get_text().strip()
            if h1_text and len(h1_text) < 100:  # 避免提取过长的文本
                return h1_text
        
        return None

    def extract_storage_conditions(self, soup):
        """提取存储条件数据"""
        storage_conditions = {
            "room_temperature": {"duration": None, "raw": ""},
            "refrigerated": {"duration": None, "raw": ""},
            "frozen": {"duration": None, "raw": ""}
        }
        
        try:
            # 查找包含保质期信息的表格
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        condition = cells[0].get_text().strip().lower()
                        duration = cells[1].get_text().strip()
                        
                        if any(keyword in condition for keyword in ['pantry', 'counter', 'room']):
                            storage_conditions["room_temperature"]["duration"] = self.normalize_duration(duration)
                            storage_conditions["room_temperature"]["raw"] = duration
                        elif any(keyword in condition for keyword in ['refrigerat', 'fridge']):
                            storage_conditions["refrigerated"]["duration"] = self.normalize_duration(duration)
                            storage_conditions["refrigerated"]["raw"] = duration
                        elif any(keyword in condition for keyword in ['freez', 'frozen']):
                            storage_conditions["frozen"]["duration"] = self.normalize_duration(duration)
                            storage_conditions["frozen"]["raw"] = duration
            
        except Exception as e:
            logger.warning(f"Storage conditions extraction failed: {e}")
        
        return storage_conditions

    def normalize_duration(self, duration_text):
        """标准化持续时间文本"""
        if not duration_text or duration_text.strip() in ['--', '-', 'N/A', '']:
            return None
        
        duration = duration_text.strip()
        
        # 标准化常见格式
        duration = duration.replace('Months', 'months').replace('Month', 'month')
        duration = duration.replace('Weeks', 'weeks').replace('Week', 'week')
        duration = duration.replace('Days', 'days').replace('Day', 'day')
        duration = duration.replace('Years', 'years').replace('Year', 'year')
        
        return duration

    def extract_description(self, soup):
        """提取食物描述"""
        try:
            # 查找描述段落
            description_selectors = [
                'p:contains("shelf life")',
                '.entry-content p:first-of-type',
                '.post-content p:first-of-type',
                'p'
            ]
            
            for selector in description_selectors:
                if 'contains' in selector:
                    # 查找包含特定文本的段落
                    paragraphs = soup.find_all('p')
                    for p in paragraphs:
                        text = p.get_text().lower()
                        if 'shelf life' in text and len(text) > 50:
                            return p.get_text().strip()[:500]  # 限制长度
                else:
                    element = soup.select_one(selector)
                    if element:
                        text = element.get_text().strip()
                        if len(text) > 50:  # 确保描述有意义
                            return text[:500]  # 限制长度
                            
        except Exception as e:
            logger.warning(f"Description extraction failed: {e}")
        
        return ""

    def scrape_all_dairy_products(self):
        """爬取所有奶制品数据"""
        logger.info("Starting to scrape all dairy products...")
        
        # 去重URL列表
        unique_urls = list(set(self.dairy_urls))
        logger.info(f"Found {len(unique_urls)} unique dairy product URLs")
        
        for i, url in enumerate(unique_urls, 1):
            try:
                logger.info(f"Scraping {i}/{len(unique_urls)}: {url}")
                data = self.extract_food_data(url)
                if data:
                    self.scraped_data.append(data)
                    logger.info(f"Successfully scraped: {data['name']}")
                else:
                    logger.warning(f"No data extracted from: {url}")
                    
                # 添加延迟避免被封
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                logger.error(f"Error scraping {url}: {str(e)}")
                continue
        
        logger.info(f"Completed scraping. Total items: {len(self.scraped_data)}")
        return self.scraped_data

    def save_data(self, output_dir="data"):
        """保存爬取的数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON格式
        json_file = f"{output_dir}/dairy_products_data_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
        
        # 保存CSV格式
        csv_file = f"{output_dir}/dairy_products_data_{timestamp}.csv"
        if self.scraped_data:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=[
                    'name', 'category', 'category_zh', 
                    'room_temp_duration', 'refrigerated_duration', 'frozen_duration',
                    'description', 'source_url'
                ])
                writer.writeheader()
                
                for item in self.scraped_data:
                    writer.writerow({
                        'name': item['name'],
                        'category': item['category'],
                        'category_zh': item['category_zh'],
                        'room_temp_duration': item['storage_conditions']['room_temperature']['duration'],
                        'refrigerated_duration': item['storage_conditions']['refrigerated']['duration'],
                        'frozen_duration': item['storage_conditions']['frozen']['duration'],
                        'description': item['description'],
                        'source_url': item['source']['url']
                    })
        
        logger.info(f"Data saved to:")
        logger.info(f"  JSON: {json_file}")
        logger.info(f"  CSV: {csv_file}")
        
        return json_file, csv_file

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    scraper = None
    try:
        logger.info("🥛 Starting Dairy Products Scraper...")
        
        # 创建爬虫实例
        scraper = DairySpecificScraper(headless=True)
        
        # 爬取所有奶制品
        scraped_data = scraper.scrape_all_dairy_products()
        
        if scraped_data:
            # 保存数据
            json_file, csv_file = scraper.save_data()
            
            print(f"\n✅ 奶制品爬取完成！总共获取了 {len(scraped_data)} 个奶制品项目")
            
            # 统计不同类型的奶制品
            product_names = [item['name'] for item in scraped_data]
            unique_products = list(set(product_names))
            
            print(f"\n📊 获取的奶制品类型:")
            for product in sorted(unique_products):
                print(f"  - {product}")
            
            print(f"\n📁 数据已保存到:")
            print(f"  JSON: {json_file}")
            print(f"  CSV: {csv_file}")
        else:
            print("❌ 没有爬取到任何奶制品数据")
            
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        print(f"❌ 爬取失败: {e}")
    finally:
        if scraper:
            scraper.close()

if __name__ == "__main__":
    main()

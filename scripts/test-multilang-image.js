#!/usr/bin/env node
/**
 * 多语言图像识别测试脚本
 * 测试不同Accept-Language头的图像识别响应
 */

const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:3001';

/**
 * 创建一个简单的测试图片（1x1像素的PNG）
 */
function createTestImage() {
  const pngData = Buffer.from(
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77yQAAAABJRU5ErkJggg==',
    'base64'
  );
  
  const testImagePath = path.join(__dirname, 'test-image.png');
  fs.writeFileSync(testImagePath, pngData);
  return testImagePath;
}

/**
 * 创建FormData（Node.js版本）
 */
function createFormData(imagePath) {
  const boundary = '----formdata-test-' + Math.random().toString(36);
  const imageData = fs.readFileSync(imagePath);
  
  let formData = '';
  formData += `--${boundary}\r\n`;
  formData += `Content-Disposition: form-data; name="image"; filename="test.png"\r\n`;
  formData += `Content-Type: image/png\r\n\r\n`;
  
  const formDataBuffer = Buffer.concat([
    Buffer.from(formData, 'utf8'),
    imageData,
    Buffer.from(`\r\n--${boundary}--\r\n`, 'utf8')
  ]);
  
  return {
    buffer: formDataBuffer,
    contentType: `multipart/form-data; boundary=${boundary}`
  };
}

/**
 * 测试特定语言的图像识别
 */
async function testLanguageSpecificImageRecognition(language, acceptLanguageHeader) {
  console.log(`\n🌍 测试 ${language} 语言图像识别`);
  console.log(`Accept-Language: ${acceptLanguageHeader}`);
  console.log('=' * 50);

  try {
    // 创建测试图片
    const testImagePath = createTestImage();
    const { buffer, contentType } = createFormData(testImagePath);

    const startTime = Date.now();
    
    // 发送请求（带语言头）
    const response = await fetch(`${API_BASE_URL}/api/food/identify-image`, {
      method: 'POST',
      headers: {
        'Content-Type': contentType,
        'Accept-Language': acceptLanguageHeader
      },
      body: buffer
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`⏱️ 响应时间: ${responseTime}ms`);
    console.log(`📡 HTTP状态: ${response.status} ${response.statusText}`);

    const responseText = await response.text();
    
    if (response.ok) {
      const data = JSON.parse(responseText);
      console.log(`✅ 识别成功: ${data.name} (${data.category})`);
      console.log(`🎯 置信度: ${(data.confidence * 100).toFixed(1)}%`);
      
      if (data.tips && data.tips.length > 0) {
        console.log(`💡 保鲜建议语言检测:`);
        const firstTip = data.tips[0];
        const isChinese = /[\u4e00-\u9fff]/.test(firstTip);
        console.log(`   语言: ${isChinese ? '中文' : '英文'}`);
        console.log(`   示例: "${firstTip.substring(0, 50)}${firstTip.length > 50 ? '...' : ''}"`);
      }
      
      return { success: true, language: language, responseTime, data };
    } else {
      const errorData = JSON.parse(responseText);
      console.log(`❌ 识别失败: ${errorData.error}`);
      console.log(`📄 错误信息: ${errorData.message}`);
      
      // 检测错误信息的语言
      const isChinese = /[\u4e00-\u9fff]/.test(errorData.message);
      console.log(`🌐 错误信息语言: ${isChinese ? '中文' : '英文'}`);
      
      if (errorData.suggestions && errorData.suggestions.length > 0) {
        const firstSuggestion = errorData.suggestions[0];
        const suggestionIsChinese = /[\u4e00-\u9fff]/.test(firstSuggestion);
        console.log(`💡 建议语言: ${suggestionIsChinese ? '中文' : '英文'}`);
        console.log(`   示例: "${firstSuggestion}"`);
      }
      
      return { 
        success: false, 
        language: language, 
        responseTime, 
        error: errorData.error,
        messageLanguage: isChinese ? 'zh' : 'en'
      };
    }

  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
    return { success: false, language: language, error: error.message, responseTime: 0 };
  } finally {
    // 清理测试文件
    const testImagePath = path.join(__dirname, 'test-image.png');
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }
  }
}

/**
 * 主测试函数
 */
async function runMultiLanguageTests() {
  console.log('🧪 多语言图像识别测试\n');
  
  // 测试不同的语言设置
  const testCases = [
    {
      name: '英文用户',
      acceptLanguage: 'en-US,en;q=0.9'
    },
    {
      name: '中文用户（简体）',
      acceptLanguage: 'zh-CN,zh;q=0.9,en;q=0.8'
    },
    {
      name: '中文用户（繁体）',
      acceptLanguage: 'zh-TW,zh;q=0.9,en;q=0.8'
    },
    {
      name: '无语言偏好',
      acceptLanguage: '*'
    },
    {
      name: '其他语言（法语）',
      acceptLanguage: 'fr-FR,fr;q=0.9,en;q=0.8'
    }
  ];

  const results = [];

  for (const testCase of testCases) {
    const result = await testLanguageSpecificImageRecognition(testCase.name, testCase.acceptLanguage);
    results.push(result);
    
    // 等待一下避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // 分析结果
  console.log('\n📊 测试结果分析');
  console.log('=' * 60);
  
  let englishCount = 0;
  let chineseCount = 0;
  
  results.forEach(result => {
    if (result.success) {
      console.log(`✅ ${result.language}: 识别成功`);
    } else {
      const lang = result.messageLanguage || 'unknown';
      console.log(`❌ ${result.language}: 识别失败 (错误信息语言: ${lang})`);
      
      if (lang === 'en') englishCount++;
      if (lang === 'zh') chineseCount++;
    }
  });

  console.log('\n🌐 语言检测统计:');
  console.log(`📝 英文错误信息: ${englishCount} 次`);
  console.log(`📝 中文错误信息: ${chineseCount} 次`);
  
  // 验证语言检测是否正确
  const englishTest = results.find(r => r.language === '英文用户');
  const chineseTest = results.find(r => r.language === '中文用户（简体）');
  
  console.log('\n🎯 语言检测验证:');
  if (englishTest && englishTest.messageLanguage === 'en') {
    console.log('✅ 英文用户收到英文错误信息 - 正确');
  } else {
    console.log('❌ 英文用户未收到英文错误信息 - 需要修复');
  }
  
  if (chineseTest && chineseTest.messageLanguage === 'zh') {
    console.log('✅ 中文用户收到中文错误信息 - 正确');
  } else {
    console.log('❌ 中文用户未收到中文错误信息 - 需要修复');
  }

  console.log('\n🔧 修复建议:');
  if (englishCount === 0) {
    console.log('⚠️ 所有错误信息都是中文，语言检测可能有问题');
    console.log('💡 检查 detectPreferredLanguage 函数的 Accept-Language 解析逻辑');
  } else if (chineseCount === 0) {
    console.log('⚠️ 所有错误信息都是英文，中文检测可能有问题');
    console.log('💡 检查中文语言代码的匹配逻辑');
  } else {
    console.log('✅ 多语言检测基本正常，但可能需要细调');
  }
}

// 运行测试
if (require.main === module) {
  runMultiLanguageTests().catch(console.error);
}

module.exports = { testLanguageSpecificImageRecognition, runMultiLanguageTests };

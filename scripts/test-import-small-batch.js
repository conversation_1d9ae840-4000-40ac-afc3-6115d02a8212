#!/usr/bin/env node

/**
 * 小批量测试导入脚本
 * 只导入前 10 条数据用于测试
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

// Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('错误：缺少 Supabase 配置。请确保设置了环境变量。');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// 从主脚本复制的函数
function cleanHtmlText(text) {
  if (!text) return '';
  text = text.replace(/<[^>]+>/g, '');
  text = text.replace(/\s+/g, ' ');
  text = text.replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"');
  return text.trim();
}

function parseStorageDays(storageText) {
  if (!storageText) return null;
  
  const text = storageText.toLowerCase();
  
  if (text.includes('indefinitely')) return 9999;
  
  const yearMatch = text.match(/(\d+)\s*year/);
  if (yearMatch) return parseInt(yearMatch[1]) * 365;
  
  const monthMatch = text.match(/(\d+)\s*month/);
  if (monthMatch) return parseInt(monthMatch[1]) * 30;
  
  const weekMatch = text.match(/(\d+)\s*week/);
  if (weekMatch) return parseInt(weekMatch[1]) * 7;
  
  const rangeMatch = text.match(/(\d+)-(\d+)\s*day/);
  if (rangeMatch) {
    const min = parseInt(rangeMatch[1]);
    const max = parseInt(rangeMatch[2]);
    return Math.round((min + max) / 2);
  }
  
  const dayMatch = text.match(/(\d+)\s*day/);
  if (dayMatch) return parseInt(dayMatch[1]);
  
  return null;
}

function generateSearchKey(name) {
  return name.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .trim();
}

async function testImport() {
  console.log('=== 小批量测试导入 ===\n');
  
  try {
    // 读取数据
    const dataFile = path.join(__dirname, 'processed_data/processed_stilltasty_data_20250718_132503.json');
    const rawData = fs.readFileSync(dataFile, 'utf8');
    const data = JSON.parse(rawData);
    
    // 只取前 10 条
    const testData = data.slice(0, 10);
    console.log(`准备导入 ${testData.length} 条测试数据\n`);
    
    // 首先运行迁移（确保数据库结构正确）
    console.log('检查数据库结构...');
    
    // 测试分类映射
    const { data: mappings, error: mappingError } = await supabase
      .from('stilltasty_category_mapping')
      .select('*')
      .limit(5);
    
    if (mappingError) {
      console.error('无法读取分类映射表，可能需要先运行数据库迁移:', mappingError.message);
      console.log('\n请先运行: npm run db:migrate 或手动执行 supabase/migrations/005_add_stilltasty_support.sql');
      return;
    }
    
    console.log(`找到 ${mappings?.length || 0} 个分类映射\n`);
    
    // 处理每条数据
    for (let i = 0; i < testData.length; i++) {
      const item = testData[i];
      console.log(`\n处理 ${i + 1}/${testData.length}: ${item.name_clean || item.name}`);
      
      try {
        const searchKey = generateSearchKey(item.name_clean || item.name);
        console.log(`  搜索键: ${searchKey}`);
        
        // 检查是否已存在
        const { data: existing } = await supabase
          .from('foods')
          .select('id, name, source')
          .eq('search_key', searchKey)
          .single();
        
        if (existing) {
          console.log(`  ⚠️  已存在: ${existing.name} (来源: ${existing.source})`);
          continue;
        }
        
        // 获取分类
        const { data: categoryMapping } = await supabase
          .from('stilltasty_category_mapping')
          .select('food_category_id')
          .eq('stilltasty_category', item.category_id)
          .single();
        
        const categoryId = categoryMapping?.food_category_id || null;
        console.log(`  分类: ${item.category_id} -> ${categoryId || '未映射'}`);
        
        // 准备数据
        const foodData = {
          name: item.name_clean || item.name,
          search_key: searchKey,
          category_id: categoryId,
          refrigerated_days: parseStorageDays(item.storage.refrigerated?.text) || item.storage.refrigerated?.days,
          frozen_days: parseStorageDays(item.storage.frozen?.text) || item.storage.frozen?.days,
          room_temperature_days: parseStorageDays(item.storage.room_temperature?.text) || item.storage.room_temperature?.days,
          storage_tips: item.tips?.map(tip => cleanHtmlText(tip)).filter(Boolean) || [],
          source: 'STILLTASTY',
          confidence: 0.95,
          external_id: item.id,
          source_url: item.source?.url || null
        };
        
        console.log(`  存储天数: 常温=${foodData.room_temperature_days}, 冷藏=${foodData.refrigerated_days}, 冷冻=${foodData.frozen_days}`);
        
        // 插入数据
        const { data: newFood, error: insertError } = await supabase
          .from('foods')
          .insert(foodData)
          .select('id')
          .single();
        
        if (insertError) {
          console.error(`  ✗ 插入失败:`, insertError.message);
          continue;
        }
        
        console.log(`  ✓ 成功插入，ID: ${newFood.id}`);
        
        // 插入关键词
        if (item.keywords?.length > 0) {
          const keywordData = item.keywords.map(kw => ({
            food_id: newFood.id,
            keyword: kw.toLowerCase(),
            weight: 1.0
          }));
          
          await supabase.from('food_keywords').insert(keywordData);
          console.log(`  ✓ 插入 ${keywordData.length} 个关键词`);
        }
        
      } catch (error) {
        console.error(`  ✗ 处理失败:`, error.message);
      }
    }
    
    console.log('\n测试导入完成！');
    
    // 验证导入结果
    console.log('\n验证导入结果...');
    const { data: imported, error: queryError } = await supabase
      .from('foods')
      .select('name, search_key, refrigerated_days, frozen_days, source')
      .eq('source', 'STILLTASTY')
      .limit(10);
    
    if (!queryError && imported) {
      console.log(`\n成功导入 ${imported.length} 条 StillTasty 数据:`);
      imported.forEach((food, i) => {
        console.log(`  ${i + 1}. ${food.name} (冷藏: ${food.refrigerated_days}天, 冷冻: ${food.frozen_days}天)`);
      });
    }
    
  } catch (error) {
    console.error('测试导入失败:', error);
  }
}

// 运行测试
testImport();
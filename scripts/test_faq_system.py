#!/usr/bin/env python3
"""
FAQ系统测试脚本
验证FAQ数据导入和API功能
"""

import os
import json
import asyncio
import logging
from datetime import datetime
from supabase import create_client, Client

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FAQSystemTester:
    def __init__(self):
        """初始化FAQ系统测试器"""
        # Supabase配置
        self.supabase_url = os.getenv('NEXT_PUBLIC_SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("请设置NEXT_PUBLIC_SUPABASE_URL和SUPABASE_SERVICE_ROLE_KEY环境变量")
        
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)

    def test_database_connection(self):
        """测试数据库连接"""
        try:
            logger.info("🔗 测试数据库连接...")
            result = self.supabase.table('faq_categories').select('count', count='exact').execute()
            logger.info(f"✅ 数据库连接成功，FAQ分类表有 {result.count} 条记录")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {str(e)}")
            return False

    def test_faq_data_integrity(self):
        """测试FAQ数据完整性"""
        try:
            logger.info("📊 测试FAQ数据完整性...")
            
            # 检查FAQ总数
            faq_result = self.supabase.table('faqs').select('count', count='exact').execute()
            faq_count = faq_result.count
            logger.info(f"📝 总FAQ数量: {faq_count}")
            
            # 检查分类数量
            category_result = self.supabase.table('faq_categories').select('count', count='exact').execute()
            category_count = category_result.count
            logger.info(f"📂 总分类数量: {category_count}")
            
            # 检查FAQ-食物关联数量
            relation_result = self.supabase.table('faq_food_relations').select('count', count='exact').execute()
            relation_count = relation_result.count
            logger.info(f"🔗 FAQ-食物关联数量: {relation_count}")
            
            # 检查数据质量
            if faq_count >= 70 and category_count >= 8 and relation_count >= 1000:
                logger.info("✅ 数据完整性检查通过")
                return True
            else:
                logger.warning("⚠️ 数据完整性检查未完全通过")
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据完整性检查失败: {str(e)}")
            return False

    def test_faq_categories(self):
        """测试FAQ分类功能"""
        try:
            logger.info("🏷️ 测试FAQ分类功能...")
            
            result = self.supabase.table('faq_categories').select('*').order('priority').execute()
            categories = result.data
            
            logger.info(f"📋 分类列表:")
            for category in categories:
                logger.info(f"  - {category['name_zh']} ({category['name']}) {category['icon']}")
            
            if len(categories) >= 8:
                logger.info("✅ FAQ分类功能测试通过")
                return True
            else:
                logger.warning("⚠️ FAQ分类数量不足")
                return False
                
        except Exception as e:
            logger.error(f"❌ FAQ分类功能测试失败: {str(e)}")
            return False

    def test_faq_search(self):
        """测试FAQ搜索功能"""
        try:
            logger.info("🔍 测试FAQ搜索功能...")
            
            # 测试关键词搜索
            search_terms = ['turkey', 'frozen', 'safe', 'refrigerator']
            
            for term in search_terms:
                result = self.supabase.table('faqs').select('id, question').ilike('question', f'%{term}%').limit(3).execute()
                found_count = len(result.data)
                logger.info(f"  搜索 '{term}': 找到 {found_count} 个结果")
                
                if found_count > 0:
                    for faq in result.data:
                        logger.info(f"    - {faq['question'][:50]}...")
            
            logger.info("✅ FAQ搜索功能测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ FAQ搜索功能测试失败: {str(e)}")
            return False

    def test_faq_by_category(self):
        """测试按分类获取FAQ"""
        try:
            logger.info("📂 测试按分类获取FAQ...")
            
            # 获取冷冻食品分类的FAQ
            result = self.supabase.table('faqs').select('id, question, faq_categories(name_zh)').eq('faq_categories.name', 'frozen_foods').limit(5).execute()
            
            frozen_faqs = result.data
            logger.info(f"❄️ 冷冻食品分类FAQ数量: {len(frozen_faqs)}")
            
            for faq in frozen_faqs:
                logger.info(f"  - {faq['question'][:50]}...")
            
            if len(frozen_faqs) > 0:
                logger.info("✅ 按分类获取FAQ测试通过")
                return True
            else:
                logger.warning("⚠️ 未找到冷冻食品分类的FAQ")
                return False
                
        except Exception as e:
            logger.error(f"❌ 按分类获取FAQ测试失败: {str(e)}")
            return False

    def test_faq_food_relations(self):
        """测试FAQ与食物的关联关系"""
        try:
            logger.info("🥗 测试FAQ与食物的关联关系...")
            
            # 查找与turkey相关的FAQ
            result = self.supabase.table('faqs').select('id, question').contains('related_foods', ['turkey']).limit(5).execute()
            
            turkey_faqs = result.data
            logger.info(f"🦃 与turkey相关的FAQ数量: {len(turkey_faqs)}")
            
            for faq in turkey_faqs:
                logger.info(f"  - {faq['question'][:50]}...")
            
            if len(turkey_faqs) > 0:
                logger.info("✅ FAQ与食物关联关系测试通过")
                return True
            else:
                logger.warning("⚠️ 未找到与turkey相关的FAQ")
                return False
                
        except Exception as e:
            logger.error(f"❌ FAQ与食物关联关系测试失败: {str(e)}")
            return False

    def generate_test_report(self, test_results):
        """生成测试报告"""
        logger.info("📋 生成测试报告...")
        
        report = {
            "test_time": datetime.now().isoformat(),
            "test_results": test_results,
            "summary": {
                "total_tests": len(test_results),
                "passed_tests": sum(1 for result in test_results.values() if result),
                "failed_tests": sum(1 for result in test_results.values() if not result),
                "success_rate": sum(1 for result in test_results.values() if result) / len(test_results) * 100
            }
        }
        
        # 保存报告
        report_file = f"faq_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 测试报告已保存到: {report_file}")
        return report

    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始FAQ系统全面测试...")
        
        test_results = {}
        
        # 运行各项测试
        test_results["database_connection"] = self.test_database_connection()
        test_results["data_integrity"] = self.test_faq_data_integrity()
        test_results["categories"] = self.test_faq_categories()
        test_results["search"] = self.test_faq_search()
        test_results["category_filter"] = self.test_faq_by_category()
        test_results["food_relations"] = self.test_faq_food_relations()
        
        # 生成报告
        report = self.generate_test_report(test_results)
        
        # 输出总结
        logger.info("=" * 50)
        logger.info("📊 FAQ系统测试总结")
        logger.info("=" * 50)
        logger.info(f"总测试数: {report['summary']['total_tests']}")
        logger.info(f"通过测试: {report['summary']['passed_tests']}")
        logger.info(f"失败测试: {report['summary']['failed_tests']}")
        logger.info(f"成功率: {report['summary']['success_rate']:.1f}%")
        
        if report['summary']['success_rate'] >= 80:
            logger.info("🎉 FAQ系统测试整体通过！")
        else:
            logger.warning("⚠️ FAQ系统测试存在问题，需要检查")
        
        return report

def main():
    """主函数"""
    try:
        # 设置环境变量
        os.environ['NEXT_PUBLIC_SUPABASE_URL'] = "https://plefidqreqjnesamigoc.supabase.co"
        os.environ['SUPABASE_SERVICE_ROLE_KEY'] = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZWZpZHFyZXFqbmVzYW1pZ29jIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDExNDU4NSwiZXhwIjoyMDY1NjkwNTg1fQ.zeLJ2m7soRmkLE1kgf5cyrnBKWnmrg8pu_OHSIWSHlA"
        
        # 创建测试器实例
        tester = FAQSystemTester()
        
        # 运行所有测试
        report = tester.run_all_tests()
        
        return report['summary']['success_rate'] >= 80
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

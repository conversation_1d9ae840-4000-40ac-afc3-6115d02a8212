#!/usr/bin/env node

/**
 * 分析 StillTasty 数据的类别分布
 */

const fs = require('fs');
const path = require('path');

const dataFile = path.join(__dirname, 'processed_data/processed_stilltasty_data_20250718_132503.json');

// 读取数据
const rawData = fs.readFileSync(dataFile, 'utf8');
const data = JSON.parse(rawData);

// 统计类别分布
const categoryStats = {};
const categoryNames = {};

data.forEach(item => {
  const categoryId = item.category_id;
  const categoryName = item.category_name;
  const categoryNameZh = item.category_name_zh;
  
  if (!categoryStats[categoryId]) {
    categoryStats[categoryId] = 0;
    categoryNames[categoryId] = {
      en: categoryName,
      zh: categoryNameZh
    };
  }
  
  categoryStats[categoryId]++;
});

// 转换为数组并排序
const sortedCategories = Object.entries(categoryStats)
  .map(([id, count]) => ({
    id,
    name_en: categoryNames[id].en,
    name_zh: categoryNames[id].zh,
    count,
    percentage: ((count / data.length) * 100).toFixed(1)
  }))
  .sort((a, b) => b.count - a.count);

// 输出结果
console.log('=== StillTasty 数据类别分布分析 ===\n');
console.log(`总数据量: ${data.length} 条\n`);
console.log('类别统计:');
console.log('------------------------');

sortedCategories.forEach((cat, index) => {
  console.log(`${index + 1}. ${cat.name_en} (${cat.name_zh})`);
  console.log(`   ID: ${cat.id}`);
  console.log(`   数量: ${cat.count} (${cat.percentage}%)`);
  console.log('');
});

// 找出小类别（少于10个物品）
const smallCategories = sortedCategories.filter(cat => cat.count < 10);
if (smallCategories.length > 0) {
  console.log('\n⚠️  物品数量少于10的类别:');
  smallCategories.forEach(cat => {
    console.log(`- ${cat.name_en}: ${cat.count} 个`);
  });
}

// 输出JSON格式的统计结果
const statsOutput = {
  total: data.length,
  categories: sortedCategories,
  summary: {
    totalCategories: sortedCategories.length,
    largeCategories: sortedCategories.filter(c => c.count >= 50).length,
    mediumCategories: sortedCategories.filter(c => c.count >= 10 && c.count < 50).length,
    smallCategories: sortedCategories.filter(c => c.count < 10).length
  }
};

fs.writeFileSync(
  path.join(__dirname, 'stilltasty-category-stats.json'),
  JSON.stringify(statsOutput, null, 2)
);

console.log('\n✅ 统计结果已保存到 stilltasty-category-stats.json');
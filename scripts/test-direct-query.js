#!/usr/bin/env node

/**
 * 直接测试数据库查询
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDirectQuery() {
  console.log('=== 直接测试数据库查询 ===\n');

  const categoryIds = [3, 18]; // meat
  
  // 查询数据
  const { data, error } = await supabase
    .from('foods')
    .select(`
      id,
      name,
      search_key,
      refrigerated_days,
      frozen_days,
      room_temperature_days,
      storage_tips,
      source,
      confidence,
      food_categories(
        id,
        name,
        description
      )
    `)
    .in('category_id', categoryIds)
    .order('name', { ascending: true })
    .range(0, 23);

  console.log('数据查询结果:');
  console.log('- 错误:', error);
  console.log('- 数据长度:', data?.length || 0);
  
  // 查询总数
  const { count, error: countError } = await supabase
    .from('foods')
    .select('*', { count: 'exact', head: true })
    .in('category_id', categoryIds);
    
  console.log('\n总数查询结果:');
  console.log('- 错误:', countError);
  console.log('- 总数:', count);
  
  // 模拟 getFoodsByCategory 的返回
  const page = 1;
  const pageSize = 24;
  const total = count || 0;
  const hasMore = (page * pageSize) < total;
  
  console.log('\n返回值:');
  console.log({
    foods: data?.length || 0,
    total: total,
    hasMore: hasMore
  });
  
  console.log('\n分页导航应该显示:', total > pageSize);
}

testDirectQuery().catch(console.error);
#!/usr/bin/env python3
"""
EatByDate数据使用演示

展示如何在项目中使用爬取和处理后的EatByDate数据
"""

import json
import os
import glob
from datetime import datetime

class EatByDateDataDemo:
    def __init__(self):
        self.data = []
        self.load_latest_data()
    
    def load_latest_data(self):
        """加载最新的处理后数据"""
        processed_dir = os.path.join(os.path.dirname(__file__), "processed_data")
        
        if not os.path.exists(processed_dir):
            print("❌ No processed data found. Please run the scraper first.")
            return
        
        # 查找最新的处理后数据文件
        pattern = os.path.join(processed_dir, "processed_eatbydate_data_*.json")
        files = glob.glob(pattern)
        
        if not files:
            print("❌ No processed data files found.")
            return
        
        latest_file = max(files, key=os.path.getctime)
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ Loaded {len(self.data)} food items from {os.path.basename(latest_file)}")
        except Exception as e:
            print(f"❌ Error loading data: {e}")
    
    def search_food(self, query):
        """搜索食物"""
        if not self.data:
            return []
        
        query = query.lower()
        results = []
        
        for item in self.data:
            if query in item['name'].lower():
                results.append(item)
        
        return results
    
    def get_storage_info(self, food_name):
        """获取特定食物的存储信息"""
        results = self.search_food(food_name)
        
        if not results:
            return None
        
        # 返回第一个匹配的结果
        item = results[0]
        storage = item['storage_conditions']
        
        return {
            'name': item['name'],
            'category': item['category_zh'],
            'storage': {
                'room_temperature': storage['room_temperature']['duration'] or 'N/A',
                'refrigerated': storage['refrigerated']['duration'] or 'N/A',
                'frozen': storage['frozen']['duration'] or 'N/A'
            },
            'description': item['description'],
            'source': item['source']['name']
        }
    
    def get_category_foods(self, category):
        """获取特定分类的所有食物"""
        if not self.data:
            return []
        
        category = category.lower()
        results = []
        
        for item in self.data:
            if category in item['category'].lower() or category in item['category_zh']:
                results.append({
                    'name': item['name'],
                    'category': item['category_zh'],
                    'storage_summary': self._get_storage_summary(item)
                })
        
        return results
    
    def _get_storage_summary(self, item):
        """获取存储信息摘要"""
        storage = item['storage_conditions']
        summary = []
        
        if storage['room_temperature']['duration']:
            summary.append(f"室温: {storage['room_temperature']['duration']}")
        if storage['refrigerated']['duration']:
            summary.append(f"冷藏: {storage['refrigerated']['duration']}")
        if storage['frozen']['duration']:
            summary.append(f"冷冻: {storage['frozen']['duration']}")
        
        return " | ".join(summary) if summary else "无存储信息"
    
    def get_statistics(self):
        """获取数据统计信息"""
        if not self.data:
            return {}
        
        stats = {
            'total_items': len(self.data),
            'categories': {},
            'storage_coverage': {
                'room_temperature': 0,
                'refrigerated': 0,
                'frozen': 0
            }
        }
        
        for item in self.data:
            # 统计分类
            category = item['category_zh']
            stats['categories'][category] = stats['categories'].get(category, 0) + 1
            
            # 统计存储条件覆盖率
            storage = item['storage_conditions']
            if storage['room_temperature']['duration']:
                stats['storage_coverage']['room_temperature'] += 1
            if storage['refrigerated']['duration']:
                stats['storage_coverage']['refrigerated'] += 1
            if storage['frozen']['duration']:
                stats['storage_coverage']['frozen'] += 1
        
        return stats

def demo_search():
    """演示搜索功能"""
    print("\n🔍 搜索功能演示")
    print("-" * 30)
    
    demo = EatByDateDataDemo()
    
    if not demo.data:
        print("❌ No data available for demo")
        return
    
    # 搜索示例
    search_terms = ["milk", "apple", "chicken", "bread"]
    
    for term in search_terms:
        results = demo.search_food(term)
        print(f"\n搜索 '{term}': 找到 {len(results)} 个结果")
        
        for result in results[:2]:  # 只显示前2个结果
            storage_info = demo.get_storage_info(result['name'])
            if storage_info:
                print(f"  📦 {storage_info['name']} ({storage_info['category']})")
                print(f"     室温: {storage_info['storage']['room_temperature']}")
                print(f"     冷藏: {storage_info['storage']['refrigerated']}")
                print(f"     冷冻: {storage_info['storage']['frozen']}")

def demo_categories():
    """演示分类功能"""
    print("\n📂 分类功能演示")
    print("-" * 30)
    
    demo = EatByDateDataDemo()
    
    if not demo.data:
        print("❌ No data available for demo")
        return
    
    # 获取统计信息
    stats = demo.get_statistics()
    
    print(f"总计食物项目: {stats['total_items']}")
    print("\n各分类食物数量:")
    
    for category, count in stats['categories'].items():
        print(f"  {category}: {count} 项")
        
        # 显示该分类的几个示例
        category_foods = demo.get_category_foods(category)
        for food in category_foods[:2]:  # 只显示前2个
            print(f"    - {food['name']}: {food['storage_summary']}")

def demo_integration():
    """演示项目集成"""
    print("\n🔗 项目集成演示")
    print("-" * 30)
    
    demo = EatByDateDataDemo()
    
    if not demo.data:
        print("❌ No data available for demo")
        return
    
    # 模拟项目中的食物查询API
    def food_lookup_api(query):
        """模拟食物查询API"""
        results = demo.search_food(query)
        
        api_response = {
            'query': query,
            'results': [],
            'source': 'EatByDate.com',
            'timestamp': datetime.now().isoformat()
        }
        
        for item in results[:3]:  # 限制返回3个结果
            storage = item['storage_conditions']
            api_response['results'].append({
                'name': item['name'],
                'category': item['category'],
                'storage_recommendations': {
                    'room_temperature': storage['room_temperature']['duration'],
                    'refrigerated': storage['refrigerated']['duration'],
                    'frozen': storage['frozen']['duration']
                },
                'confidence': item['source']['confidence'],
                'description': item['description'][:100] + '...' if len(item['description']) > 100 else item['description']
            })
        
        return api_response
    
    # 测试API
    test_queries = ["apple", "milk"]
    
    for query in test_queries:
        print(f"\n🔍 API查询: '{query}'")
        response = food_lookup_api(query)
        
        print(f"找到 {len(response['results'])} 个结果:")
        for result in response['results']:
            print(f"  📦 {result['name']}")
            print(f"     分类: {result['category']}")
            print(f"     冷藏: {result['storage_recommendations']['refrigerated'] or 'N/A'}")
            print(f"     置信度: {result['confidence']}")

def main():
    """主演示函数"""
    print("🎯 EatByDate数据使用演示")
    print("=" * 50)
    
    # 检查是否有数据
    demo = EatByDateDataDemo()
    if not demo.data:
        print("\n❌ 没有找到处理后的数据文件")
        print("请先运行爬虫: python run_eatbydate_scraper.py")
        return
    
    # 运行各种演示
    demo_search()
    demo_categories()
    demo_integration()
    
    print("\n✨ 演示完成！")
    print("\n💡 提示:")
    print("- 这些数据可以集成到你的食物保质期网站中")
    print("- 可以作为USDA数据的补充来源")
    print("- 建议定期更新数据以保持准确性")

if __name__ == "__main__":
    main()

2025-07-17 06:24:54,473 - INFO - 🚀 Starting Dynamic EatByDate Scraper...
2025-07-17 06:25:21,637 - INFO - Starting to scrape all categories...
2025-07-17 06:25:21,638 - INFO - Scraping category: 奶制品
2025-07-17 06:25:21,638 - INFO - Discovering food links from category: 奶制品 - https://eatbydate.com/dairy/
2025-07-17 06:25:51,652 - ERROR - Error discovering links from https://eatbydate.com/dairy/: Message: timeout: Timed out receiving message from renderer: 27.615
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010472b55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000104723454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x00000001042723f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x000000010425d1fc cxxbridge1$string$len + 4140
4   chromedriver                        0x000000010425cf74 cxxbridge1$string$len + 3492
5   chromedriver                        0x000000010425ad2c chromedriver + 191788
6   chromedriver                        0x000000010425b8bc chromedriver + 194748
7   chromedriver                        0x0000000104268afc cxxbridge1$string$len + 51500
8   chromedriver                        0x000000010427ea18 cxxbridge1$string$len + 141384
9   chromedriver                        0x000000010425bf18 chromedriver + 196376
10  chromedriver                        0x000000010427e820 cxxbridge1$string$len + 140880
11  chromedriver                        0x00000001042fadbc cxxbridge1$string$len + 650220
12  chromedriver                        0x00000001042ada0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001046ee5e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x00000001046f1848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x00000001046cf234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x00000001046f2104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001046c02e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000104711ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000104712184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000104723090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:25:51,656 - WARNING - No food URLs found for category: 奶制品
2025-07-17 06:25:51,657 - INFO - Scraping category: 水果
2025-07-17 06:25:51,657 - INFO - Discovering food links from category: 水果 - https://eatbydate.com/fruits/
2025-07-17 06:26:09,902 - INFO - Found 115 food links in 水果
2025-07-17 06:26:09,903 - INFO - Found 115 URLs for 水果, will scrape all of them
2025-07-17 06:26:09,903 - INFO - Scraping 1/115: https://eatbydate.com/dairy/yogurt-shelf-life-expiration-date/
2025-07-17 06:26:09,903 - INFO - Extracting data from: https://eatbydate.com/dairy/yogurt-shelf-life-expiration-date/
2025-07-17 06:26:26,958 - INFO - Successfully scraped: Yogurt
2025-07-17 06:26:29,460 - INFO - Scraping 2/115: https://eatbydate.com/vegetables/canned-vegetables-shelf-life-expiration-date/
2025-07-17 06:26:29,460 - INFO - Extracting data from: https://eatbydate.com/vegetables/canned-vegetables-shelf-life-expiration-date/
2025-07-17 06:26:59,480 - ERROR - Error extracting data from https://eatbydate.com/vegetables/canned-vegetables-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 28.172
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010472b55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000104723454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x00000001042723f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x000000010425d1fc cxxbridge1$string$len + 4140
4   chromedriver                        0x000000010425cf74 cxxbridge1$string$len + 3492
5   chromedriver                        0x000000010425ad2c chromedriver + 191788
6   chromedriver                        0x000000010425b8bc chromedriver + 194748
7   chromedriver                        0x0000000104268afc cxxbridge1$string$len + 51500
8   chromedriver                        0x000000010427ea18 cxxbridge1$string$len + 141384
9   chromedriver                        0x000000010425bf18 chromedriver + 196376
10  chromedriver                        0x000000010427e820 cxxbridge1$string$len + 140880
11  chromedriver                        0x00000001042fadbc cxxbridge1$string$len + 650220
12  chromedriver                        0x00000001042ada0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001046ee5e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x00000001046f1848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x00000001046cf234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x00000001046f2104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001046c02e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000104711ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000104712184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000104723090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:26:59,483 - WARNING - No data extracted from: https://eatbydate.com/vegetables/canned-vegetables-shelf-life-expiration-date/
2025-07-17 06:27:01,134 - INFO - Scraping 3/115: https://eatbydate.com/other/condiments/how-long-does-bbq-sauce-last-shelf-life-expiration-date/
2025-07-17 06:27:01,135 - INFO - Extracting data from: https://eatbydate.com/other/condiments/how-long-does-bbq-sauce-last-shelf-life-expiration-date/
2025-07-17 06:27:16,071 - INFO - Successfully scraped: BBQ
2025-07-17 06:27:18,944 - INFO - Scraping 4/115: https://eatbydate.com/canned-fruit-shelf-life-expiration-date
2025-07-17 06:27:18,944 - INFO - Extracting data from: https://eatbydate.com/canned-fruit-shelf-life-expiration-date
2025-07-17 06:27:36,567 - INFO - Successfully scraped: Canned
2025-07-17 06:27:38,451 - INFO - Scraping 5/115: https://eatbydate.com/fruits/fresh/how-long-do-strawberries-last/
2025-07-17 06:27:38,452 - INFO - Extracting data from: https://eatbydate.com/fruits/fresh/how-long-do-strawberries-last/
2025-07-17 06:27:57,615 - INFO - Successfully scraped: Strawberries
2025-07-17 06:27:59,858 - INFO - Scraping 6/115: https://eatbydate.com/fruits/fresh/tomatoes-shelf-life-expiration-date/
2025-07-17 06:27:59,859 - INFO - Extracting data from: https://eatbydate.com/fruits/fresh/tomatoes-shelf-life-expiration-date/
2025-07-17 06:28:12,813 - INFO - Successfully scraped: Tomatoes
2025-07-17 06:28:13,995 - INFO - Scraping 7/115: https://eatbydate.com/grains/pasta-shelf-life-expiration-date/
2025-07-17 06:28:13,995 - INFO - Extracting data from: https://eatbydate.com/grains/pasta-shelf-life-expiration-date/
2025-07-17 06:28:35,362 - INFO - Successfully scraped: Pasta
2025-07-17 06:28:37,730 - INFO - Scraping 8/115: https://eatbydate.com/fruits/frozen-fruit-shelf-life-expiration-date/
2025-07-17 06:28:37,731 - INFO - Extracting data from: https://eatbydate.com/fruits/frozen-fruit-shelf-life-expiration-date/
2025-07-17 06:28:49,392 - INFO - Successfully scraped: Frozen
2025-07-17 06:28:51,956 - INFO - Scraping 9/115: https://eatbydate.com/fruits/dried-fruit-shelf-life-expiration-date/
2025-07-17 06:28:51,956 - INFO - Extracting data from: https://eatbydate.com/fruits/dried-fruit-shelf-life-expiration-date/
2025-07-17 06:29:13,403 - INFO - Successfully scraped: Dried
2025-07-17 06:29:14,998 - INFO - Scraping 10/115: https://eatbydate.com/proteins/meats/beef-shelf-life-expiration-date/
2025-07-17 06:29:14,999 - INFO - Extracting data from: https://eatbydate.com/proteins/meats/beef-shelf-life-expiration-date/
2025-07-17 06:29:27,428 - INFO - Successfully scraped: Beef
2025-07-17 06:29:29,695 - INFO - Scraping 11/115: https://eatbydate.com/grains/baked-goods/stuffing-shelf-life-expiration-date/
2025-07-17 06:29:29,696 - INFO - Extracting data from: https://eatbydate.com/grains/baked-goods/stuffing-shelf-life-expiration-date/
2025-07-17 06:29:51,009 - INFO - Successfully scraped: Stuffing
2025-07-17 06:29:52,275 - INFO - Scraping 12/115: https://eatbydate.com/other/baking/how-long-does-crisco-last-shelf-life-expiration-date-storage-tips/
2025-07-17 06:29:52,275 - INFO - Extracting data from: https://eatbydate.com/other/baking/how-long-does-crisco-last-shelf-life-expiration-date-storage-tips/
2025-07-17 06:30:04,269 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(54, 'Connection reset by peer')': /session/4394ea97619173a276340497c4bca173
2025-07-17 06:30:04,283 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1159ba4d0>: Failed to establish a new connection: [Errno 61] Connection refused')': /session/4394ea97619173a276340497c4bca173
2025-07-17 06:30:04,287 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1159ba710>: Failed to establish a new connection: [Errno 61] Connection refused')': /session/4394ea97619173a276340497c4bca173

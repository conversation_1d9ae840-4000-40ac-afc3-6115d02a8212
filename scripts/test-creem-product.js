/**
 * 测试特定的Creem产品ID
 */

// 临时禁用代理
delete process.env.HTTP_PROXY;
delete process.env.HTTPS_PROXY;
delete process.env.http_proxy;
delete process.env.https_proxy;

const CREEM_API_KEY = "creem_test_1gt8ta2IIRvAeR4MKJHacx";
const CREEM_API_BASE = "https://api.creem.io/v1";
const PRODUCT_ID = "prod_3rDi4h6HXsofQvCQg8liRP";

async function testCreemProduct() {
  console.log('🧪 测试Creem产品ID...');
  console.log(`📋 API Key: ${CREEM_API_KEY.substring(0, 20)}...`);
  console.log(`🆔 产品ID: ${PRODUCT_ID}`);
  console.log('');

  // 测试1: 验证产品是否存在
  console.log('🔍 测试1: 验证产品是否存在');
  try {
    const response = await fetch(`${CREEM_API_BASE}/products/${PRODUCT_ID}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': CREEM_API_KEY,
      },
    });

    console.log(`   状态码: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const product = await response.json();
      console.log(`   ✅ 产品存在!`);
      console.log(`   📦 产品名称: ${product.name}`);
      console.log(`   💰 价格: ${product.price} ${product.currency}`);
      console.log(`   📊 状态: ${product.status || 'N/A'}`);
      console.log(`   🔄 类型: ${product.type || 'N/A'}`);
    } else {
      const errorText = await response.text();
      console.log(`   ❌ 产品不存在: ${errorText}`);
      return false;
    }
  } catch (error) {
    console.log(`   💥 错误: ${error.message}`);
    return false;
  }

  console.log('');

  // 测试2: 尝试创建结账会话
  console.log('🛒 测试2: 创建结账会话');
  try {
    const response = await fetch(`${CREEM_API_BASE}/checkouts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': CREEM_API_KEY,
      },
      body: JSON.stringify({
        product_id: PRODUCT_ID,
        customer_email: '<EMAIL>',
        success_url: 'http://localhost:3000/creem-success',
        cancel_url: 'http://localhost:3000/#pricing',
        request_id: 'test_' + Date.now(),
      }),
    });

    console.log(`   状态码: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const session = await response.json();
      console.log(`   ✅ 结账会话创建成功!`);
      console.log(`   🆔 会话ID: ${session.id}`);
      console.log(`   🔗 结账URL: ${session.url}`);
      console.log('');
      console.log('🎉 Creem配置测试成功! 您现在可以使用Creem支付了!');
      return true;
    } else {
      const errorText = await response.text();
      console.log(`   ❌ 创建失败: ${errorText}`);
      return false;
    }
  } catch (error) {
    console.log(`   💥 错误: ${error.message}`);
    return false;
  }
}

// 运行测试
testCreemProduct()
  .then((success) => {
    if (success) {
      console.log('');
      console.log('✅ 所有测试通过! Creem支付已准备就绪!');
      console.log('💡 现在您可以在应用中测试支付流程了。');
    } else {
      console.log('');
      console.log('❌ 测试失败，请检查产品ID或API密钥配置。');
    }
  })
  .catch(console.error);

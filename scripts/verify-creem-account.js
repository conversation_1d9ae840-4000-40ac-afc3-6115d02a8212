/**
 * 验证Creem账户和API密钥
 */

// 临时禁用代理
delete process.env.HTTP_PROXY;
delete process.env.HTTPS_PROXY;
delete process.env.http_proxy;
delete process.env.https_proxy;

const CREEM_API_KEY = "creem_test_1gt8ta2IIRvAeR4MKJHacx";
const CREEM_API_BASE = "https://api.creem.io/v1";

async function verifyCreemAccount() {
  console.log('🔍 验证Creem账户信息...');
  console.log(`📋 API Key: ${CREEM_API_KEY.substring(0, 20)}...`);
  console.log('');

  // 测试1: 验证API密钥有效性
  console.log('🔑 测试1: 验证API密钥');
  try {
    // 尝试访问账户信息或任何需要认证的端点
    const response = await fetch(`${CREEM_API_BASE}/products`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': CREEM_API_KEY,
      },
    });

    console.log(`   状态码: ${response.status} ${response.statusText}`);
    
    if (response.status === 401) {
      console.log('   ❌ API密钥无效或过期');
      console.log('   💡 请检查Creem Dashboard中的API密钥');
      return false;
    } else if (response.status === 403) {
      console.log('   ⚠️  API密钥有效但权限不足');
      console.log('   💡 可能需要升级账户或验证邮箱');
    } else if (response.ok) {
      console.log('   ✅ API密钥有效');
      
      try {
        const data = await response.json();
        if (Array.isArray(data)) {
          console.log(`   📦 找到 ${data.length} 个产品`);
          
          if (data.length > 0) {
            console.log('   📋 您账户中的产品:');
            data.forEach((product, index) => {
              console.log(`      ${index + 1}. ${product.name || 'Unnamed'}`);
              console.log(`         ID: ${product.id}`);
              console.log(`         价格: ${product.price || 'N/A'} ${product.currency || ''}`);
              console.log(`         状态: ${product.status || 'N/A'}`);
              console.log('');
            });
            
            // 检查我们配置的产品ID是否在列表中
            const configuredId = 'prod_3rDi4h6HXsofQvCQg8liRP';
            const foundProduct = data.find(p => p.id === configuredId);
            
            if (foundProduct) {
              console.log(`   ✅ 配置的产品ID ${configuredId} 存在于您的账户中`);
            } else {
              console.log(`   ❌ 配置的产品ID ${configuredId} 不在您的账户中`);
              console.log('   💡 请使用上面列出的产品ID之一');
            }
          } else {
            console.log('   ⚠️  您的账户中没有产品');
            console.log('   💡 请在Creem Dashboard中创建产品');
          }
        } else {
          console.log('   📊 响应数据:', JSON.stringify(data, null, 2));
        }
      } catch (parseError) {
        console.log('   ⚠️  无法解析响应数据');
      }
    } else {
      const errorText = await response.text();
      console.log(`   ❓ 未知状态: ${errorText}`);
    }
  } catch (error) {
    console.log(`   💥 网络错误: ${error.message}`);
    return false;
  }

  console.log('');
  console.log('🏁 验证完成!');
  return true;
}

// 运行验证
verifyCreemAccount()
  .then(() => {
    console.log('');
    console.log('💡 建议:');
    console.log('1. 如果看到了产品列表，请使用列表中的产品ID');
    console.log('2. 如果没有产品，请在Creem Dashboard中创建产品');
    console.log('3. 如果API密钥无效，请检查Creem Dashboard中的密钥');
  })
  .catch(console.error);

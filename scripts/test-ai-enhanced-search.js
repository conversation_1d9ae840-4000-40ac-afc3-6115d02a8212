#!/usr/bin/env node
/**
 * AI 增强查询功能测试脚本
 * 验证 USDA 数据 + AI 优化的查询效果
 */

const API_BASE_URL = 'http://localhost:3001';

// 测试查询列表
const testQueries = [
  // 中文查询 - 测试AI优化效果
  { query: '苹果', language: 'zh', expectAI: true },
  { query: '芒果', language: 'zh', expectAI: true },
  { query: '香蕉', language: 'zh', expectAI: true },
  { query: '鸡蛋', language: 'zh', expectAI: true },
  { query: '牛肉', language: 'zh', expectAI: true },
  
  // 英文查询 - 测试AI优化效果
  { query: 'apple', language: 'en', expectAI: true },
  { query: 'chicken', language: 'en', expectAI: true },
  { query: 'beef', language: 'en', expectAI: true },
  
  // 不存在的食物 - 测试降级机制
  { query: '火龙果', language: 'zh', expectAI: false },
  { query: 'dragonfruit', language: 'en', expectAI: false }
];

/**
 * 测试单个查询
 */
async function testQuery(query, language, expectAI) {
  try {
    console.log(`\n🔍 测试查询: "${query}" (${language})`);
    
    const startTime = Date.now();
    const response = await fetch(`${API_BASE_URL}/api/food/identify-text`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ foodName: query })
    });
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (response.ok) {
      const data = await response.json();
      
      // 分析结果质量
      const hasOptimizedName = data.name !== query && (
        (language === 'zh' && /[\u4e00-\u9fff]/.test(data.name)) ||
        (language === 'en' && !/[\u4e00-\u9fff]/.test(data.name))
      );
      
      const hasDetailedTips = data.tips && data.tips.length > 0 && 
        data.tips.some(tip => tip.length > 20);
      
      const hasChineseCategory = /[\u4e00-\u9fff]/.test(data.category);
      
      // 判断是否为AI优化结果
      const isAIOptimized = hasDetailedTips || hasChineseCategory || 
        (language === 'zh' && /[\u4e00-\u9fff]/.test(data.name));
      
      console.log(`✅ 查询成功 (${responseTime}ms)`);
      console.log(`   食物名称: ${data.name}`);
      console.log(`   类别: ${data.category}`);
      console.log(`   置信度: ${(data.confidence * 100).toFixed(1)}%`);
      
      // 显示存储信息
      const storage = [];
      if (data.storage.refrigerated > 0) storage.push(`冷藏: ${data.storage.refrigerated}天`);
      if (data.storage.frozen > 0) storage.push(`冷冻: ${data.storage.frozen}天`);
      if (data.storage.room_temperature > 0) storage.push(`常温: ${data.storage.room_temperature}天`);
      if (storage.length > 0) {
        console.log(`   存储时间: ${storage.join(', ')}`);
      }
      
      // 显示保鲜建议
      if (data.tips && data.tips.length > 0) {
        console.log(`   保鲜建议: ${data.tips.length}条`);
        data.tips.forEach((tip, index) => {
          console.log(`     ${index + 1}. ${tip.substring(0, 50)}${tip.length > 50 ? '...' : ''}`);
        });
      }
      
      // AI优化检测
      if (isAIOptimized) {
        console.log(`   🤖 AI优化: ✅ 检测到AI优化特征`);
      } else {
        console.log(`   🤖 AI优化: ❌ 未检测到AI优化特征`);
      }
      
      return {
        query,
        language,
        success: true,
        responseTime,
        isAIOptimized,
        expectAI,
        aiMatch: expectAI === isAIOptimized,
        data
      };
      
    } else {
      const errorData = await response.json();
      console.log(`❌ 查询失败: ${errorData.error || errorData.message}`);
      
      return {
        query,
        language,
        success: false,
        responseTime,
        isAIOptimized: false,
        expectAI,
        aiMatch: !expectAI, // 如果期望失败且确实失败，则匹配
        error: errorData.error || errorData.message
      };
    }
    
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
    return {
      query,
      language,
      success: false,
      responseTime: 0,
      isAIOptimized: false,
      expectAI,
      aiMatch: false,
      error: error.message
    };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🤖 AI 增强查询功能测试');
  console.log('=' * 60);
  console.log(`API 地址: ${API_BASE_URL}`);
  console.log(`测试时间: ${new Date().toLocaleString()}\n`);
  
  const results = [];
  let totalResponseTime = 0;
  
  for (const test of testQueries) {
    const result = await testQuery(test.query, test.language, test.expectAI);
    results.push(result);
    totalResponseTime += result.responseTime;
    
    // 避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 统计分析
  const successCount = results.filter(r => r.success).length;
  const aiOptimizedCount = results.filter(r => r.isAIOptimized).length;
  const aiMatchCount = results.filter(r => r.aiMatch).length;
  const avgResponseTime = totalResponseTime / results.length;
  
  console.log('\n📊 测试总结');
  console.log('=' * 50);
  console.log(`总测试数: ${testQueries.length}`);
  console.log(`查询成功: ${successCount} (${((successCount / testQueries.length) * 100).toFixed(1)}%)`);
  console.log(`AI优化: ${aiOptimizedCount} (${((aiOptimizedCount / testQueries.length) * 100).toFixed(1)}%)`);
  console.log(`AI预期匹配: ${aiMatchCount} (${((aiMatchCount / testQueries.length) * 100).toFixed(1)}%)`);
  console.log(`平均响应时间: ${avgResponseTime.toFixed(0)}ms`);
  
  // 按语言分组统计
  const zhResults = results.filter(r => r.language === 'zh');
  const enResults = results.filter(r => r.language === 'en');
  
  console.log(`\n中文查询:`);
  console.log(`  成功率: ${((zhResults.filter(r => r.success).length / zhResults.length) * 100).toFixed(1)}%`);
  console.log(`  AI优化率: ${((zhResults.filter(r => r.isAIOptimized).length / zhResults.length) * 100).toFixed(1)}%`);
  
  console.log(`英文查询:`);
  console.log(`  成功率: ${((enResults.filter(r => r.success).length / enResults.length) * 100).toFixed(1)}%`);
  console.log(`  AI优化率: ${((enResults.filter(r => r.isAIOptimized).length / enResults.length) * 100).toFixed(1)}%`);
  
  // 显示失败的测试
  const failedTests = results.filter(r => !r.success);
  if (failedTests.length > 0) {
    console.log('\n❌ 失败的测试:');
    failedTests.forEach(test => {
      console.log(`- "${test.query}" (${test.language}): ${test.error}`);
    });
  }
  
  // 显示AI预期不匹配的测试
  const aiMismatchTests = results.filter(r => !r.aiMatch);
  if (aiMismatchTests.length > 0) {
    console.log('\n⚠️ AI预期不匹配的测试:');
    aiMismatchTests.forEach(test => {
      console.log(`- "${test.query}": 期望${test.expectAI ? 'AI优化' : '非AI'}, 实际${test.isAIOptimized ? 'AI优化' : '非AI'}`);
    });
  }
  
  // 性能评估
  console.log('\n⚡ 性能评估:');
  if (avgResponseTime < 2000) {
    console.log('✅ 响应速度优秀 (< 2秒)');
  } else if (avgResponseTime < 5000) {
    console.log('⚠️ 响应速度一般 (2-5秒)');
  } else {
    console.log('❌ 响应速度较慢 (> 5秒)');
  }
  
  // 总体评估
  const overallScore = (successCount * 0.4 + aiOptimizedCount * 0.4 + aiMatchCount * 0.2) / testQueries.length * 100;
  console.log(`\n🎯 总体评分: ${overallScore.toFixed(1)}/100`);
  
  if (overallScore >= 80) {
    console.log('🎉 AI增强功能表现优秀！');
  } else if (overallScore >= 60) {
    console.log('👍 AI增强功能表现良好，有改进空间');
  } else {
    console.log('⚠️ AI增强功能需要优化');
  }
  
  return results;
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

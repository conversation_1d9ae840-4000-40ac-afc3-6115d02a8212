#!/usr/bin/env node

/**
 * 数据迁移脚本：从旧项目迁移数据到新项目
 *
 * 迁移顺序：
 * 1. 食物分类 (food_categories)
 * 2. 食物主表 (foods)
 * 3. 食物别名 (food_aliases)
 * 4. FAQ分类 (faq_categories)
 * 5. FAQ主表 (faqs)
 * 6. FAQ-食物关联 (faq_food_relations)
 */

const fs = require('fs');
const path = require('path');

// 配置
const OLD_PROJECT_ID = 'plefidqreqjnesamigoc';
const NEW_PROJECT_ID = 'emopvngdwwghndzcfxvw';

// 数据转换函数
function transformFaqCategory(oldCategory) {
  return {
    name: oldCategory.name_zh || oldCategory.name, // 使用中文名作为主名称
    slug: oldCategory.name, // 英文名作为slug
    description: oldCategory.description,
    sort_order: oldCategory.priority || 0,
    is_active: true,
    name_zh: oldCategory.name_zh,
    icon: oldCategory.icon,
    priority: oldCategory.priority || 0
  };
}

function transformFaq(oldFaq) {
  // 生成slug
  const slug = oldFaq.question
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .substring(0, 100);

  return {
    category_id: oldFaq.category_id,
    question: oldFaq.question,
    answer: oldFaq.answer,
    slug: slug,
    sort_order: oldFaq.id || 0,
    is_active: true,
    view_count: oldFaq.view_count || 0,
    is_featured: false,
    external_id: oldFaq.external_id,
    answer_summary: oldFaq.answer_summary,
    key_points: oldFaq.key_points || [],
    related_foods: oldFaq.related_foods || [],
    tags: oldFaq.tags || [],
    word_count: oldFaq.word_count || 0,
    helpful_count: oldFaq.helpful_count || 0,
    source_name: oldFaq.source_name || 'StillTasty.com',
    source_url: oldFaq.source_url,
    confidence: parseFloat(oldFaq.confidence) || 0.95,
    processed_at: oldFaq.processed_at
  };
}

function transformFoodCategory(oldCategory) {
  return {
    name: oldCategory.name,
    description: oldCategory.description
  };
}

function transformFood(oldFood) {
  return {
    name: oldFood.name,
    search_key: oldFood.search_key,
    category_id: oldFood.category_id,
    refrigerated_days: oldFood.refrigerated_days,
    frozen_days: oldFood.frozen_days,
    room_temperature_days: oldFood.room_temperature_days,
    storage_tips: oldFood.storage_tips || [],
    source: oldFood.source || 'USDA',
    confidence: parseFloat(oldFood.confidence) || 0.98,
    usda_product_id: oldFood.usda_product_id,
    usda_category: oldFood.usda_category
  };
}

function transformFoodAlias(oldAlias) {
  return {
    food_id: oldAlias.food_id,
    alias: oldAlias.alias,
    language: oldAlias.language || 'en',
    alias_type: oldAlias.alias_type || 'name'
  };
}

function transformFaqFoodRelation(oldRelation) {
  return {
    faq_id: oldRelation.faq_id,
    food_id: oldRelation.food_id,
    relevance_score: parseFloat(oldRelation.relevance_score) || 0.5
  };
}

module.exports = {
  transformFaqCategory,
  transformFaq,
  transformFoodCategory,
  transformFood,
  transformFoodAlias,
  transformFaqFoodRelation
};
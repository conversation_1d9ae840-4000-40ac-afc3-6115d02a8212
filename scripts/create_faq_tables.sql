-- FAQ数据库表结构
-- 为StillTasty FAQ数据创建数据库表

-- 1. FAQ分类表
CREATE TABLE IF NOT EXISTS faq_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    name_zh VARCHAR(100) NOT NULL,
    icon VARCHAR(10),
    priority INTEGER DEFAULT 0,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. FAQ主表
CREATE TABLE IF NOT EXISTS faqs (
    id SERIAL PRIMARY KEY,
    external_id VARCHAR(100) UNIQUE, -- 来自爬取数据的ID
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    answer_summary TEXT,
    category_id INTEGER REFERENCES faq_categories(id),
    
    -- 关键要点
    key_points TEXT[],
    
    -- 相关食物关键词
    related_foods TEXT[],
    
    -- 标签
    tags TEXT[],
    
    -- 统计信息
    word_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    helpful_count INTEGER DEFAULT 0,
    
    -- 数据来源
    source_name VARCHAR(100) DEFAULT 'StillTasty.com',
    source_url TEXT,
    confidence DECIMAL(3,2) DEFAULT 0.95,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- 3. FAQ与食物的关联表
CREATE TABLE IF NOT EXISTS faq_food_relations (
    id SERIAL PRIMARY KEY,
    faq_id INTEGER REFERENCES faqs(id) ON DELETE CASCADE,
    food_id INTEGER REFERENCES foods(id) ON DELETE CASCADE,
    relevance_score DECIMAL(3,2) DEFAULT 0.5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(faq_id, food_id)
);

-- 4. FAQ搜索历史表（用于统计和优化）
CREATE TABLE IF NOT EXISTS faq_search_logs (
    id SERIAL PRIMARY KEY,
    search_query TEXT NOT NULL,
    faq_id INTEGER REFERENCES faqs(id),
    user_ip VARCHAR(45),
    user_agent TEXT,
    found BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_faqs_category ON faqs(category_id);
CREATE INDEX IF NOT EXISTS idx_faqs_external_id ON faqs(external_id);
CREATE INDEX IF NOT EXISTS idx_faqs_question_text ON faqs USING gin(to_tsvector('english', question));
CREATE INDEX IF NOT EXISTS idx_faqs_answer_text ON faqs USING gin(to_tsvector('english', answer));
CREATE INDEX IF NOT EXISTS idx_faqs_related_foods ON faqs USING gin(related_foods);
CREATE INDEX IF NOT EXISTS idx_faqs_tags ON faqs USING gin(tags);
CREATE INDEX IF NOT EXISTS idx_faq_food_relations_faq ON faq_food_relations(faq_id);
CREATE INDEX IF NOT EXISTS idx_faq_food_relations_food ON faq_food_relations(food_id);
CREATE INDEX IF NOT EXISTS idx_faq_search_logs_query ON faq_search_logs(search_query);

-- 插入FAQ分类数据
INSERT INTO faq_categories (name, name_zh, icon, priority, description) VALUES
('frozen_foods', '冷冻食品', '❄️', 1, 'Questions about frozen food storage and safety'),
('refrigerated_foods', '冷藏食品', '🧊', 2, 'Questions about refrigerated food storage'),
('room_temperature', '常温保存', '🌡️', 3, 'Questions about room temperature food storage'),
('food_safety', '食品安全', '🛡️', 4, 'Questions about food safety and health'),
('storage_tips', '保存技巧', '💡', 5, 'Tips and tricks for food storage'),
('expiration_dates', '保质期', '📅', 6, 'Questions about expiration dates and shelf life'),
('preparation', '食品处理', '👨‍🍳', 7, 'Questions about food preparation and cooking'),
('general', '一般问题', '❓', 8, 'General food-related questions')
ON CONFLICT (name) DO UPDATE SET
    name_zh = EXCLUDED.name_zh,
    icon = EXCLUDED.icon,
    priority = EXCLUDED.priority,
    description = EXCLUDED.description,
    updated_at = NOW();

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建更新时间戳的触发器
CREATE TRIGGER update_faq_categories_updated_at 
    BEFORE UPDATE ON faq_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_faqs_updated_at 
    BEFORE UPDATE ON faqs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建全文搜索函数
CREATE OR REPLACE FUNCTION search_faqs(search_term TEXT, category_filter TEXT DEFAULT NULL)
RETURNS TABLE (
    id INTEGER,
    question TEXT,
    answer_summary TEXT,
    category_name TEXT,
    category_name_zh TEXT,
    category_icon TEXT,
    related_foods TEXT[],
    tags TEXT[],
    relevance REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.id,
        f.question,
        f.answer_summary,
        fc.name as category_name,
        fc.name_zh as category_name_zh,
        fc.icon as category_icon,
        f.related_foods,
        f.tags,
        ts_rank(
            to_tsvector('english', f.question || ' ' || f.answer),
            plainto_tsquery('english', search_term)
        ) as relevance
    FROM faqs f
    JOIN faq_categories fc ON f.category_id = fc.id
    WHERE 
        (category_filter IS NULL OR fc.name = category_filter)
        AND (
            to_tsvector('english', f.question || ' ' || f.answer) @@ plainto_tsquery('english', search_term)
            OR f.question ILIKE '%' || search_term || '%'
            OR search_term = ANY(f.related_foods)
            OR search_term = ANY(f.tags)
        )
    ORDER BY relevance DESC, f.view_count DESC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql;

-- 创建获取热门FAQ的函数
CREATE OR REPLACE FUNCTION get_popular_faqs(category_filter TEXT DEFAULT NULL, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    id INTEGER,
    question TEXT,
    answer_summary TEXT,
    category_name TEXT,
    category_name_zh TEXT,
    category_icon TEXT,
    view_count INTEGER,
    helpful_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.id,
        f.question,
        f.answer_summary,
        fc.name as category_name,
        fc.name_zh as category_name_zh,
        fc.icon as category_icon,
        f.view_count,
        f.helpful_count
    FROM faqs f
    JOIN faq_categories fc ON f.category_id = fc.id
    WHERE (category_filter IS NULL OR fc.name = category_filter)
    ORDER BY f.view_count DESC, f.helpful_count DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 创建获取相关FAQ的函数
CREATE OR REPLACE FUNCTION get_related_faqs(faq_id_param INTEGER, limit_count INTEGER DEFAULT 5)
RETURNS TABLE (
    id INTEGER,
    question TEXT,
    answer_summary TEXT,
    category_name TEXT,
    category_name_zh TEXT,
    similarity_score REAL
) AS $$
BEGIN
    RETURN QUERY
    WITH target_faq AS (
        SELECT related_foods, tags, category_id
        FROM faqs 
        WHERE id = faq_id_param
    )
    SELECT 
        f.id,
        f.question,
        f.answer_summary,
        fc.name as category_name,
        fc.name_zh as category_name_zh,
        (
            -- 计算相似度：相同食物关键词 + 相同标签 + 相同分类
            COALESCE(array_length(f.related_foods & tf.related_foods, 1), 0) * 0.4 +
            COALESCE(array_length(f.tags & tf.tags, 1), 0) * 0.3 +
            CASE WHEN f.category_id = tf.category_id THEN 0.3 ELSE 0 END
        ) as similarity_score
    FROM faqs f
    JOIN faq_categories fc ON f.category_id = fc.id
    CROSS JOIN target_faq tf
    WHERE 
        f.id != faq_id_param
        AND (
            f.related_foods && tf.related_foods 
            OR f.tags && tf.tags 
            OR f.category_id = tf.category_id
        )
    ORDER BY similarity_score DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 添加注释
COMMENT ON TABLE faq_categories IS 'FAQ分类表';
COMMENT ON TABLE faqs IS 'FAQ主表，存储问题和答案';
COMMENT ON TABLE faq_food_relations IS 'FAQ与食物的关联表';
COMMENT ON TABLE faq_search_logs IS 'FAQ搜索历史记录表';

COMMENT ON COLUMN faqs.external_id IS '外部系统的ID，用于数据同步';
COMMENT ON COLUMN faqs.key_points IS '从答案中提取的关键要点';
COMMENT ON COLUMN faqs.related_foods IS '相关的食物关键词数组';
COMMENT ON COLUMN faqs.tags IS '标签数组，用于分类和搜索';
COMMENT ON COLUMN faqs.word_count IS '答案的字数统计';
COMMENT ON COLUMN faqs.view_count IS '查看次数统计';
COMMENT ON COLUMN faqs.helpful_count IS '用户认为有用的次数';
COMMENT ON COLUMN faqs.confidence IS '数据的置信度，0-1之间';

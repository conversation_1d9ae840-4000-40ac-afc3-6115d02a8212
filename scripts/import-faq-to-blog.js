const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Supabase配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('请确保在.env.local中设置了SUPABASE_URL和SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// 分类封面图片映射
const categoryCoverImages = {
  'food_safety': '/imgs/blog/food-safety-cover.jpg',
  'frozen_foods': '/imgs/blog/frozen-foods-cover.jpg', 
  'refrigerated_foods': '/imgs/blog/refrigerated-foods-cover.jpg',
  'expiration_dates': '/imgs/blog/expiration-dates-cover.jpg',
  'preparation': '/imgs/blog/food-preparation-cover.jpg',
  'storage_tips': '/imgs/blog/storage-tips-cover.jpg'
};

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 生成slug
function generateSlug(question) {
  return question
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/-+/g, '-') // 多个连字符替换为单个
    .replace(/^-+|-+$/g, '') // 移除首尾连字符
    .substring(0, 100); // 限制长度
}

// 格式化Markdown内容
function formatMarkdownContent(faq) {
  let content = `## Overview\n\n${faq.answer}\n\n`;
  
  // 添加关键要点
  if (faq.key_points && faq.key_points.length > 0) {
    content += `## Key Points\n\n`;
    faq.key_points.forEach(point => {
      content += `- ${point}\n`;
    });
    content += '\n';
  }
  
  // 添加相关食品
  if (faq.related_foods && faq.related_foods.length > 0) {
    content += `## Related Foods\n\n`;
    content += `This information applies to: ${faq.related_foods.join(', ')}\n\n`;
  }
  
  // 添加标签
  if (faq.tags && faq.tags.length > 0) {
    content += `## Tags\n\n`;
    faq.tags.forEach(tag => {
      content += `\`${tag}\` `;
    });
    content += '\n\n';
  }
  
  // 添加数据来源 - 已注释掉
  // if (faq.source) {
  //   content += `---\n\n`;
  //   content += `*Source: ${faq.source.name}*\n`;
  //   content += `*Confidence: ${faq.source.confidence}*\n`;
  // }
  
  return content;
}

// 翻译分类名称
const categoryTranslations = {
  'food_safety': '食品安全',
  'frozen_foods': '冷冻食品', 
  'refrigerated_foods': '冷藏食品',
  'expiration_dates': '有效期',
  'preparation': '食品准备',
  'storage_tips': '储存技巧'
};

// 主函数
async function importFAQsToBlog() {
  try {
    // 首先测试数据库连接
    console.log('测试数据库连接...');
    const { data: testData, error: testError } = await supabase
      .from('posts')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.error('数据库连接失败:', testError);
      process.exit(1);
    }
    console.log('数据库连接成功');
    
    // 读取FAQ数据
    const faqData = JSON.parse(
      fs.readFileSync(path.join(__dirname, 'data/processed_faq_data_20250721_065602.json'), 'utf8')
    );
    
    console.log(`读取到 ${faqData.length} 个FAQ项目`);
    
    // 批量准备数据
    const posts = [];
    const currentTime = new Date().toISOString();
    
    for (let i = 0; i < faqData.length; i++) {
      const faq = faqData[i];
      const slug = generateSlug(faq.question);
      
      // 英文版本
      const enPost = {
        uuid: generateUUID(),
        slug: `faq-${slug}`,
        title: faq.question,
        description: faq.answer_summary || faq.answer.substring(0, 160) + '...',
        content: formatMarkdownContent(faq),
        status: 'online',
        locale: 'en',
        author_name: 'HowLongFresh Expert',
        author_avatar_url: '/imgs/avatar/expert.png',
        cover_url: categoryCoverImages[faq.category] || '/imgs/blog/default-cover.jpg',
        category: faq.category,
        category_icon: faq.category_icon,
        created_at: currentTime,
        updated_at: currentTime
      };
      
      // 中文版本
      const zhPost = {
        uuid: generateUUID(),
        slug: `faq-${slug}`,
        title: `${faq.category_icon} ${faq.question}`, // 添加图标让标题更生动
        description: faq.answer_summary || faq.answer.substring(0, 160) + '...',
        content: formatMarkdownContent(faq),
        status: 'online',
        locale: 'zh',
        author_name: 'HowLongFresh 专家',
        author_avatar_url: '/imgs/avatar/expert.png',
        cover_url: categoryCoverImages[faq.category] || '/imgs/blog/default-cover.jpg',
        category: faq.category,
        category_icon: faq.category_icon,
        created_at: currentTime,
        updated_at: currentTime
      };
      
      posts.push(enPost, zhPost);
      
      if ((i + 1) % 10 === 0) {
        console.log(`已处理 ${i + 1} 个FAQ...`);
      }
    }
    
    console.log(`准备导入 ${posts.length} 篇博客文章（${faqData.length} 个FAQ x 2种语言）`);
    
    // 先测试插入一条数据（创建一个测试用的数据，不使用实际的posts数据）
    console.log('\n测试插入第一条数据...');
    const testPost = {
      ...posts[0],
      uuid: generateUUID(), // 生成新的UUID避免冲突
      slug: 'test-post-' + Date.now() // 临时slug
    };
    console.log('测试数据:', JSON.stringify(testPost, null, 2));

    const { data: testInsert, error: testInsertError } = await supabase
      .from('posts')
      .insert([testPost])
      .select();

    if (testInsertError) {
      console.error('测试插入失败:', testInsertError);
      console.error('错误码:', testInsertError.code);
      console.error('错误消息:', testInsertError.message);
      console.error('错误详情:', testInsertError.details);
      console.error('错误提示:', testInsertError.hint);
      process.exit(1);
    } else {
      console.log('测试插入成功:', testInsert);
      // 删除测试数据
      await supabase.from('posts').delete().eq('id', testInsert[0].id);
      console.log('测试数据已清理');
    }

    // 如果测试成功，继续批量插入
    console.log('\n开始批量导入...');

    // 分批插入数据（每批25条）
    const batchSize = 25;
    let successCount = 0;

    for (let i = 0; i < posts.length; i += batchSize) {
      const batch = posts.slice(i, i + batchSize);
      
      const { data, error } = await supabase
        .from('posts')
        .insert(batch)
        .select();
      
      if (error) {
        console.error(`批次 ${Math.floor(i / batchSize) + 1} 插入失败:`, error);
        console.error('错误详情:', error.message, error.details, error.hint);
        if (batch.length > 0) {
          console.error('第一条数据样例:', JSON.stringify(batch[0], null, 2));
        }
      } else {
        successCount += batch.length;
        console.log(`批次 ${Math.floor(i / batchSize) + 1} 插入成功，已导入 ${successCount}/${posts.length} 篇文章`);
        if (data && data.length > 0) {
          console.log(`插入的数据ID: ${data.map(d => d.id).join(', ')}`);
        }
      }
    }
    
    console.log('\n导入完成！');
    console.log(`成功导入 ${successCount} 篇博客文章`);
    
    // 统计各分类文章数量
    const categoryStats = {};
    faqData.forEach(faq => {
      categoryStats[faq.category] = (categoryStats[faq.category] || 0) + 1;
    });
    
    console.log('\n各分类文章统计:');
    Object.entries(categoryStats).forEach(([category, count]) => {
      console.log(`- ${categoryTranslations[category] || category}: ${count} 篇`);
    });
    
  } catch (error) {
    console.error('导入过程中出错:', error);
    process.exit(1);
  }
}

// 运行导入
importFAQsToBlog();
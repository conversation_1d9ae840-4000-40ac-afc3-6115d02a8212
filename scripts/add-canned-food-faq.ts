/**
 * 添加罐头食品安全FAQ数据
 * 对应用户截图中的内容："Is it Safe to Leave Canned Food Leftovers in the Can?"
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// 加载环境变量
config({ path: '.env.local' });

// 环境变量
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

const supabase = createClient(supabaseUrl, supabaseKey);

// 罐头食品安全FAQ数据
const cannedFoodFAQ = {
  external_id: 'canned-food-leftovers-safety',
  question: 'Is it Safe to Leave Canned Food Leftovers in the Can?',
  question_zh: '将剩余的罐头食品留在罐头中安全吗？',
  answer: `From a safety standpoint, the answer is yes. The United States Department of Agriculture says it's safe to refrigerate canned foods manufactured in the United States directly in the can. That said, there are some quality considerations to keep in mind.

When you open a can of food, you expose the contents to air and bacteria. While refrigeration slows bacterial growth, it doesn't stop it entirely. For optimal quality and safety, it's recommended to transfer leftover canned foods to a clean, airtight container before refrigerating.

Here are the key considerations:

1. **Safety**: Refrigerating opened canned foods in the original can is safe for short periods (2-3 days)
2. **Quality**: The food may develop a metallic taste if left in the can too long
3. **Best Practice**: Transfer to glass or plastic containers for better preservation
4. **Storage Time**: Use refrigerated canned food leftovers within 3-4 days
5. **Temperature**: Keep refrigerated at 40°F (4°C) or below`,
  answer_summary: '从安全角度来说，将罐头食品剩余部分留在罐中冷藏是安全的。美国农业部表示，在美国制造的罐头食品可以直接在罐中冷藏。但为了最佳质量，建议转移到密封容器中。',
  key_points: [
    '美国农业部确认直接在罐中冷藏是安全的',
    '为了最佳质量，建议转移到密封容器中',
    '冷藏的罐头食品剩余应在3-4天内食用',
    '长时间留在罐中可能产生金属味',
    '保持冷藏温度在4°C或以下'
  ],
  related_foods: [
    'canned vegetables',
    'canned fruits',
    'canned soup',
    'canned beans',
    'canned tomatoes',
    'canned fish',
    'canned meat'
  ],
  tags: [
    'food safety',
    'canned food',
    'leftovers',
    'refrigeration',
    'storage',
    'USDA guidelines',
    'food preservation'
  ],
  word_count: 250,
  source_name: 'USDA Food Safety Guidelines',
  source_url: 'https://www.fsis.usda.gov/food-safety/safe-food-handling-and-preparation/food-safety-basics/leftovers-and-food-safety',
  confidence: 0.98
};

async function addCannedFoodFAQ() {
  try {
    console.log('🚀 开始添加罐头食品安全FAQ...');

    // 1. 获取或创建food_safety分类
    let { data: category, error: categoryError } = await supabase
      .from('faq_categories')
      .select('id')
      .eq('name', 'food_safety')
      .single();

    if (categoryError || !category) {
      console.log('创建food_safety分类...');
      const { data: newCategory, error: createError } = await supabase
        .from('faq_categories')
        .insert({
          name: 'food_safety',
          name_zh: '食品安全',
          icon: '🛡️',
          priority: 4,
          description: 'Questions about food safety and health'
        })
        .select('id')
        .single();

      if (createError) {
        throw new Error(`创建分类失败: ${createError.message}`);
      }
      category = newCategory;
    }

    console.log(`✅ 分类ID: ${category.id}`);

    // 2. 检查FAQ是否已存在
    const { data: existingFAQ } = await supabase
      .from('faqs')
      .select('id')
      .eq('external_id', cannedFoodFAQ.external_id)
      .single();

    if (existingFAQ) {
      console.log('⚠️  FAQ已存在，跳过插入');
      return;
    }

    // 3. 插入FAQ数据
    const faqData = {
      external_id: cannedFoodFAQ.external_id,
      question: cannedFoodFAQ.question,
      answer: cannedFoodFAQ.answer,
      answer_summary: cannedFoodFAQ.answer_summary,
      category_id: category.id,
      key_points: cannedFoodFAQ.key_points,
      related_foods: cannedFoodFAQ.related_foods,
      tags: cannedFoodFAQ.tags,
      word_count: cannedFoodFAQ.word_count,
      source_name: cannedFoodFAQ.source_name,
      source_url: cannedFoodFAQ.source_url,
      confidence: cannedFoodFAQ.confidence,
      processed_at: new Date().toISOString()
    };

    const { data: insertedFAQ, error: insertError } = await supabase
      .from('faqs')
      .insert(faqData)
      .select('id')
      .single();

    if (insertError) {
      throw new Error(`插入FAQ失败: ${insertError.message}`);
    }

    console.log(`✅ 成功添加FAQ，ID: ${insertedFAQ.id}`);
    console.log(`📝 问题: ${cannedFoodFAQ.question}`);
    console.log(`🔗 外部ID: ${cannedFoodFAQ.external_id}`);

    // 4. 验证插入结果
    const { data: verifyFAQ, error: verifyError } = await supabase
      .from('faqs')
      .select(`
        id,
        external_id,
        question,
        answer_summary,
        faq_categories (
          name,
          name_zh,
          icon
        )
      `)
      .eq('id', insertedFAQ.id)
      .single();

    if (verifyError) {
      throw new Error(`验证FAQ失败: ${verifyError.message}`);
    }

    console.log('🎉 FAQ添加成功！');
    console.log('验证信息:', {
      id: verifyFAQ.id,
      external_id: verifyFAQ.external_id,
      question: verifyFAQ.question,
      category: verifyFAQ.faq_categories
    });

  } catch (error) {
    console.error('❌ 添加FAQ失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addCannedFoodFAQ()
    .then(() => {
      console.log('✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

export { addCannedFoodFAQ, cannedFoodFAQ };

#!/usr/bin/env python3
"""
EatByDate.com Food Data Scraper

This script scrapes food storage information from eatbydate.com
organized by categories (Dairy, Drinks, Fruits, Grains, Other, Proteins, Vegetables)
"""

import json
import time
import csv
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EatByDateScraper:
    def __init__(self, headless=True):
        """初始化爬虫"""
        self.base_url = "https://eatbydate.com"
        self.categories = {
            "dairy": "奶制品",
            "drinks": "饮料", 
            "fruits": "水果",
            "grains": "谷物",
            "other": "其他",
            "proteins": "蛋白质",
            "vegetables": "蔬菜"
        }
        self.driver = self._setup_driver(headless)
        self.scraped_data = []
        
    def _setup_driver(self, headless=True):
        """设置Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        
        try:
            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            return driver
        except Exception as e:
            logger.error(f"Failed to initialize Chrome driver: {e}")
            raise
    
    def scrape_category_page(self, category_url, category_name):
        """爬取单个分类页面的食物列表"""
        logger.info(f"Scraping category: {category_name} - {category_url}")

        try:
            self.driver.get(category_url)
            time.sleep(3)  # 等待页面加载

            # 查找食物链接
            food_links = []

            # 根据实际网站结构，查找食物链接
            # 在分类页面中，食物链接通常在导航菜单或页面内容中
            selectors = [
                # 导航菜单中的链接
                "nav a[href*='/fruits/fresh/']",
                "nav a[href*='/dairy/']",
                "nav a[href*='/vegetables/fresh-vegetables/']",
                "nav a[href*='/proteins/']",
                "nav a[href*='/grains/']",
                "nav a[href*='/drinks/']",
                "nav a[href*='/other/']",
                # 页面内容中的链接
                "a[href*='shelf-life']",
                "a[href*='how-long-do']",
                "a[href*='how-long-does']",
                # 特定的食物链接模式
                "a[href*='/fresh/']",
                "a[href*='/meats/']",
                "a[href*='/milk/']",
                "a[href*='/cheese/']"
            ]

            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        text = element.text.strip()

                        # 过滤有效的食物链接
                        if (href and text and
                            ('shelf-life' in href or 'how-long-do' in href or
                             '/fresh/' in href or '/meats/' in href or
                             '/milk/' in href or '/cheese/' in href) and
                            len(text) > 0 and len(text) < 100):  # 避免过长的文本

                            food_links.append({
                                'url': href,
                                'title': text,
                                'category': category_name
                            })

                except Exception as e:
                    logger.warning(f"Selector {selector} failed: {e}")
                    continue

            # 去重
            unique_links = []
            seen_urls = set()
            for link in food_links:
                if link['url'] not in seen_urls:
                    seen_urls.add(link['url'])
                    unique_links.append(link)

            logger.info(f"Found {len(unique_links)} unique food items in {category_name}")
            return unique_links

        except Exception as e:
            logger.error(f"Error scraping category {category_name}: {e}")
            return []
    
    def scrape_food_detail(self, food_item):
        """爬取单个食物的详细信息"""
        try:
            self.driver.get(food_item['url'])
            time.sleep(2)
            
            # 提取食物详细信息
            food_data = {
                'name': food_item['title'],
                'category': food_item['category'],
                'url': food_item['url'],
                'pantry_life': '',
                'refrigerator_life': '',
                'freezer_life': '',
                'description': '',
                'scraped_at': datetime.now().isoformat()
            }
            
            # 尝试提取保质期信息
            try:
                # 根据实际网站结构，查找保质期表格
                table_found = False

                # 查找保质期表格
                try:
                    tables = self.driver.find_elements(By.CSS_SELECTOR, "table")
                    for table in tables:
                        table_text = table.text.lower()
                        if 'pantry' in table_text or 'refrigerator' in table_text or 'freezer' in table_text:
                            # 提取表格中的保质期信息
                            rows = table.find_elements(By.CSS_SELECTOR, "tr")
                            for row in rows:
                                row_text = row.text.lower()
                                if 'fresh' in row_text or food_item['title'].lower().split()[0] in row_text:
                                    cells = row.find_elements(By.CSS_SELECTOR, "td")
                                    if len(cells) >= 3:  # 通常有3列：食物类型、室温、冷藏
                                        if len(cells) > 1:
                                            food_data['pantry_life'] = cells[1].text.strip()
                                        if len(cells) > 2:
                                            food_data['refrigerator_life'] = cells[2].text.strip()
                                        if len(cells) > 3:
                                            food_data['freezer_life'] = cells[3].text.strip()
                                    table_found = True
                                    break
                            if table_found:
                                break
                except Exception as e:
                    logger.warning(f"Table extraction failed: {e}")

                # 如果表格提取失败，尝试从文本中提取
                if not table_found:
                    try:
                        # 查找包含保质期信息的文本
                        content_selectors = [
                            "article",
                            ".entry-content",
                            ".post-content",
                            ".content"
                        ]

                        for selector in content_selectors:
                            try:
                                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                                for element in elements:
                                    text = element.text
                                    # 使用更精确的正则表达式提取保质期
                                    import re

                                    # 查找室温保质期
                                    pantry_patterns = [
                                        r'pantry[:\s]+([^,\n]+)',
                                        r'room temperature[:\s]+([^,\n]+)',
                                        r'counter[:\s]+([^,\n]+)'
                                    ]
                                    for pattern in pantry_patterns:
                                        match = re.search(pattern, text, re.IGNORECASE)
                                        if match:
                                            food_data['pantry_life'] = match.group(1).strip()
                                            break

                                    # 查找冷藏保质期
                                    fridge_patterns = [
                                        r'refrigerator[:\s]+([^,\n]+)',
                                        r'fridge[:\s]+([^,\n]+)',
                                        r'refrigerated[:\s]+([^,\n]+)'
                                    ]
                                    for pattern in fridge_patterns:
                                        match = re.search(pattern, text, re.IGNORECASE)
                                        if match:
                                            food_data['refrigerator_life'] = match.group(1).strip()
                                            break

                                    # 查找冷冻保质期
                                    freezer_patterns = [
                                        r'freezer[:\s]+([^,\n]+)',
                                        r'frozen[:\s]+([^,\n]+)',
                                        r'freeze[:\s]+([^,\n]+)'
                                    ]
                                    for pattern in freezer_patterns:
                                        match = re.search(pattern, text, re.IGNORECASE)
                                        if match:
                                            food_data['freezer_life'] = match.group(1).strip()
                                            break

                                    if food_data['pantry_life'] or food_data['refrigerator_life'] or food_data['freezer_life']:
                                        break

                                if food_data['pantry_life'] or food_data['refrigerator_life'] or food_data['freezer_life']:
                                    break

                            except Exception as e:
                                continue

                    except Exception as e:
                        logger.warning(f"Text extraction failed: {e}")

            except Exception as e:
                logger.warning(f"Could not extract storage info for {food_item['title']}: {e}")
            
            # 提取描述信息
            try:
                desc_selectors = [".entry-content", ".post-content", "article p", ".content p"]
                for selector in desc_selectors:
                    try:
                        desc_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if desc_element:
                            food_data['description'] = desc_element.text[:500]  # 限制描述长度
                            break
                    except:
                        continue
            except Exception as e:
                logger.warning(f"Could not extract description for {food_item['title']}: {e}")
            
            return food_data
            
        except Exception as e:
            logger.error(f"Error scraping food detail {food_item['url']}: {e}")
            return None
    
    def _extract_duration(self, text, storage_type):
        """从文本中提取保质期时间"""
        import re
        
        # 常见的时间模式
        patterns = [
            r'(\d+)\s*(?:to\s*)?(\d+)?\s*(days?|weeks?|months?|years?)',
            r'(\d+)\s*-\s*(\d+)\s*(days?|weeks?|months?|years?)',
            r'(\d+)\s*(days?|weeks?|months?|years?)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text.lower())
            if matches:
                return matches[0] if isinstance(matches[0], str) else ' '.join(filter(None, matches[0]))
        
        return ''
    
    def scrape_all_categories(self):
        """爬取所有分类的数据"""
        logger.info("Starting to scrape all categories...")
        
        for category, category_name in self.categories.items():
            category_url = f"{self.base_url}/{category}/"
            
            # 获取分类页面的食物列表
            food_links = self.scrape_category_page(category_url, category_name)
            
            # 爬取每个食物的详细信息
            for i, food_item in enumerate(food_links[:10]):  # 限制每个分类最多10个食物，避免过度爬取
                logger.info(f"Scraping food {i+1}/{len(food_links[:10])}: {food_item['title']}")
                
                food_data = self.scrape_food_detail(food_item)
                if food_data:
                    self.scraped_data.append(food_data)
                
                # 添加延迟避免被封
                time.sleep(2)
            
            # 分类间添加更长延迟
            time.sleep(5)
    
    def save_data(self, output_dir="data"):
        """保存爬取的数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存为JSON格式
        json_file = os.path.join(output_dir, f"eatbydate_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
        
        # 保存为CSV格式
        csv_file = os.path.join(output_dir, f"eatbydate_data_{timestamp}.csv")
        if self.scraped_data:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=self.scraped_data[0].keys())
                writer.writeheader()
                writer.writerows(self.scraped_data)
        
        logger.info(f"Data saved to {json_file} and {csv_file}")
        logger.info(f"Total items scraped: {len(self.scraped_data)}")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    scraper = EatByDateScraper(headless=True)
    
    try:
        scraper.scrape_all_categories()
        scraper.save_data()
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    main()

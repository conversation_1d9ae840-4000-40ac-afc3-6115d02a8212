/**
 * Creem API 测试脚本
 * 用于验证API密钥和产品ID是否正确
 */

const CREEM_API_KEY = process.env.CREEM_API_KEY || "creem_test_1gt8ta2IIRvAeR4MKJHacx";
const CREEM_API_BASE = "https://api.creem.io/v1";

// 测试产品ID
const TEST_PRODUCT_IDS = [
  'prod_3rDi4h6HXsofQvCQg8liRP',
  'prod_1OcPVhMnVmfxoae5l6hcwD'
];

async function testCreemAPI() {
  console.log('🧪 开始测试 Creem API...');
  console.log(`📋 API Key: ${CREEM_API_KEY.substring(0, 20)}...`);
  console.log(`🌐 API Base: ${CREEM_API_BASE}`);
  console.log('');

  // 测试1: 获取产品列表
  console.log('📦 测试1: 获取产品列表');
  try {
    const response = await fetch(`${CREEM_API_BASE}/products`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': CREEM_API_KEY,
      },
    });

    console.log(`   状态码: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const products = await response.json();
      console.log(`   ✅ 成功获取产品列表`);
      console.log(`   📊 产品数量: ${products.length || 0}`);
      
      if (products.length > 0) {
        console.log('   📋 产品列表:');
        products.forEach((product, index) => {
          console.log(`      ${index + 1}. ${product.name} (ID: ${product.id})`);
          console.log(`         价格: ${product.price} ${product.currency}`);
          console.log(`         状态: ${product.status || 'N/A'}`);
          console.log('');
        });
      }
    } else {
      const errorText = await response.text();
      console.log(`   ❌ 失败: ${errorText}`);
    }
  } catch (error) {
    console.log(`   💥 错误: ${error.message}`);
  }

  console.log('');

  // 测试2: 验证特定产品ID
  for (const productId of TEST_PRODUCT_IDS) {
    console.log(`🔍 测试2: 验证产品ID ${productId}`);
    try {
      const response = await fetch(`${CREEM_API_BASE}/products/${productId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': CREEM_API_KEY,
        },
      });

      console.log(`   状态码: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const product = await response.json();
        console.log(`   ✅ 产品存在: ${product.name}`);
        console.log(`   💰 价格: ${product.price} ${product.currency}`);
        console.log(`   📊 状态: ${product.status || 'N/A'}`);
      } else {
        const errorText = await response.text();
        console.log(`   ❌ 产品不存在或无权访问: ${errorText}`);
      }
    } catch (error) {
      console.log(`   💥 错误: ${error.message}`);
    }
    console.log('');
  }

  // 测试3: 尝试创建结账会话（使用第一个产品ID）
  console.log(`🛒 测试3: 创建结账会话`);
  const testProductId = TEST_PRODUCT_IDS[0];
  try {
    const response = await fetch(`${CREEM_API_BASE}/checkouts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': CREEM_API_KEY,
      },
      body: JSON.stringify({
        product_id: testProductId,
        customer_email: '<EMAIL>',
        success_url: 'http://localhost:3000/creem-success',
        cancel_url: 'http://localhost:3000/#pricing',
        request_id: 'test_' + Date.now(),
      }),
    });

    console.log(`   状态码: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const session = await response.json();
      console.log(`   ✅ 结账会话创建成功`);
      console.log(`   🆔 会话ID: ${session.id}`);
      console.log(`   🔗 结账URL: ${session.url}`);
    } else {
      const errorText = await response.text();
      console.log(`   ❌ 创建失败: ${errorText}`);
    }
  } catch (error) {
    console.log(`   💥 错误: ${error.message}`);
  }

  console.log('');
  console.log('🏁 测试完成!');
}

// 运行测试
testCreemAPI().catch(console.error);

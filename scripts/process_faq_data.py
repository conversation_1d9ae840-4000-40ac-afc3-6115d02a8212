#!/usr/bin/env python3
"""
StillTasty FAQ数据处理脚本
清洗和规范化爬取的FAQ数据
"""

import os
import json
import csv
import re
import logging
from datetime import datetime
from typing import Dict, List
from bs4 import BeautifulSoup

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('process_faq_data.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FAQDataProcessor:
    def __init__(self):
        """初始化FAQ数据处理器"""
        self.processed_data = []
        
        # 改进的分类映射
        self.category_mapping = {
            "frozen_foods": {"name_zh": "冷冻食品", "icon": "❄️", "priority": 1},
            "refrigerated_foods": {"name_zh": "冷藏食品", "icon": "🧊", "priority": 2},
            "room_temperature": {"name_zh": "常温保存", "icon": "🌡️", "priority": 3},
            "food_safety": {"name_zh": "食品安全", "icon": "🛡️", "priority": 4},
            "storage_tips": {"name_zh": "保存技巧", "icon": "💡", "priority": 5},
            "expiration_dates": {"name_zh": "保质期", "icon": "📅", "priority": 6},
            "preparation": {"name_zh": "食品处理", "icon": "👨‍🍳", "priority": 7},
            "general": {"name_zh": "一般问题", "icon": "❓", "priority": 8}
        }
        
        # 需要过滤的无用内容
        self.filter_patterns = [
            r'See Also:.*?(?=\n\n|\Z)',  # 移除"See Also"部分
            r'About Our Authors.*?(?=\n\n|\Z)',  # 移除作者信息
            r'Have a question\?.*?(?=\n\n|\Z)',  # 移除联系信息
            r'More Questions.*?(?=\n\n|\Z)',  # 移除更多问题列表
            r'Today\'s Tips.*?(?=\n\n|\Z)',  # 移除今日小贴士
            r'<.*?>',  # 移除HTML标签
            r'\n\s*\n\s*\n',  # 移除多余的空行
            r'^\s+|\s+$',  # 移除首尾空白
        ]

    def clean_text(self, text: str) -> str:
        """清洗文本内容"""
        if not text:
            return ""
        
        # 使用BeautifulSoup移除HTML标签
        soup = BeautifulSoup(text, 'html.parser')
        text = soup.get_text()
        
        # 应用过滤模式
        for pattern in self.filter_patterns:
            text = re.sub(pattern, '', text, flags=re.DOTALL | re.IGNORECASE)
        
        # 规范化空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        return text

    def improve_categorization(self, question: str, answer: str) -> str:
        """改进的FAQ分类逻辑"""
        text = (question + " " + answer).lower()
        
        # 更精确的分类关键词
        category_keywords = {
            "frozen_foods": {
                "keywords": ["frozen", "freezer", "freeze", "thaw", "thawed", "defrost"],
                "weight": 2
            },
            "refrigerated_foods": {
                "keywords": ["refrigerat", "fridge", "cold", "chill", "refrigerator"],
                "weight": 2
            },
            "room_temperature": {
                "keywords": ["room temperature", "counter", "pantry", "shelf", "ambient"],
                "weight": 2
            },
            "food_safety": {
                "keywords": ["safe", "unsafe", "danger", "risk", "bacteria", "contamination", "illness", "poisoning"],
                "weight": 3
            },
            "storage_tips": {
                "keywords": ["store", "storage", "keep", "preserve", "maintain", "last longer"],
                "weight": 1
            },
            "expiration_dates": {
                "keywords": ["expiration", "expire", "date", "best by", "sell by", "use by", "shelf life"],
                "weight": 2
            },
            "preparation": {
                "keywords": ["cook", "cooking", "wash", "rinse", "prepare", "heat", "temperature", "reheat"],
                "weight": 1
            }
        }
        
        # 计算加权分数
        scores = {}
        for category, config in category_keywords.items():
            score = 0
            for keyword in config["keywords"]:
                if keyword in text:
                    score += config["weight"]
            if score > 0:
                scores[category] = score
        
        # 返回得分最高的分类
        if scores:
            return max(scores.items(), key=lambda x: x[1])[0]
        return "general"

    def extract_key_points(self, answer: str) -> List[str]:
        """从答案中提取关键要点"""
        if not answer:
            return []
        
        # 按句子分割
        sentences = re.split(r'[.!?]+', answer)
        key_points = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 20 and len(sentence) < 200:  # 过滤太短或太长的句子
                # 检查是否包含有用信息
                useful_patterns = [
                    r'\d+\s*(day|week|month|hour)',  # 时间信息
                    r'(safe|unsafe|danger|risk)',  # 安全信息
                    r'(refrigerat|freez|store)',  # 存储信息
                    r'(temperature|degree)',  # 温度信息
                ]
                
                if any(re.search(pattern, sentence, re.IGNORECASE) for pattern in useful_patterns):
                    key_points.append(sentence.strip())
        
        return key_points[:3]  # 最多返回3个要点

    def process_faq_item(self, item: Dict) -> Dict:
        """处理单个FAQ项目"""
        # 清洗问题和答案
        question = self.clean_text(item.get('question', ''))
        answer = self.clean_text(item.get('answer', ''))
        
        # 重新分类
        category = self.improve_categorization(question, answer)
        category_info = self.category_mapping.get(category, self.category_mapping["general"])
        
        # 提取关键要点
        key_points = self.extract_key_points(answer)
        
        # 清洗相关食物列表
        related_foods = []
        if item.get('related_foods'):
            for food in item['related_foods']:
                if food and len(food) > 1:  # 过滤空值和单字符
                    related_foods.append(food.lower().strip())
        
        # 去重并排序
        related_foods = sorted(list(set(related_foods)))
        
        # 清洗标签
        tags = []
        if item.get('tags'):
            for tag in item['tags']:
                if tag and len(tag) > 1:
                    tags.append(tag.lower().strip())
        
        # 去重并排序
        tags = sorted(list(set(tags)))
        
        # 构建处理后的数据
        processed_item = {
            "id": item.get('id', f"faq_{hash(question) % 1000000}"),
            "question": question,
            "answer": answer,
            "answer_summary": answer[:200] + "..." if len(answer) > 200 else answer,
            "key_points": key_points,
            "category": category,
            "category_zh": category_info["name_zh"],
            "category_icon": category_info["icon"],
            "category_priority": category_info["priority"],
            "related_foods": related_foods,
            "tags": tags,
            "word_count": len(answer.split()) if answer else 0,
            "source": {
                "name": "StillTasty.com",
                "url": item.get('source', {}).get('url', ''),
                "processed_at": datetime.now().isoformat(),
                "confidence": "high"
            }
        }
        
        return processed_item

    def process_data_file(self, input_file: str):
        """处理FAQ数据文件"""
        logger.info(f"开始处理FAQ数据文件: {input_file}")
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            logger.info(f"加载了 {len(raw_data)} 个原始FAQ项目")
            
            # 处理每个FAQ项目
            for i, item in enumerate(raw_data, 1):
                try:
                    processed_item = self.process_faq_item(item)
                    
                    # 质量检查
                    if processed_item['question'] and processed_item['answer']:
                        if len(processed_item['answer']) > 50:  # 确保答案有足够内容
                            self.processed_data.append(processed_item)
                            logger.info(f"处理FAQ {i}/{len(raw_data)}: {processed_item['question'][:50]}...")
                        else:
                            logger.warning(f"跳过FAQ {i} - 答案太短: {processed_item['question'][:50]}...")
                    else:
                        logger.warning(f"跳过FAQ {i} - 缺少问题或答案")
                        
                except Exception as e:
                    logger.error(f"处理FAQ {i} 时出错: {str(e)}")
                    continue
            
            logger.info(f"成功处理了 {len(self.processed_data)} 个FAQ项目")
            
        except Exception as e:
            logger.error(f"处理数据文件时出错: {str(e)}")
            raise

    def save_processed_data(self, output_dir="data"):
        """保存处理后的数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON格式
        json_file = f"{output_dir}/processed_faq_data_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.processed_data, f, ensure_ascii=False, indent=2)
        
        # 保存CSV格式
        csv_file = f"{output_dir}/processed_faq_data_{timestamp}.csv"
        if self.processed_data:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                fieldnames = [
                    'id', 'question', 'answer_summary', 'category', 'category_zh',
                    'related_foods_count', 'tags_count', 'word_count', 'source_url'
                ]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for item in self.processed_data:
                    writer.writerow({
                        'id': item['id'],
                        'question': item['question'],
                        'answer_summary': item['answer_summary'],
                        'category': item['category'],
                        'category_zh': item['category_zh'],
                        'related_foods_count': len(item['related_foods']),
                        'tags_count': len(item['tags']),
                        'word_count': item['word_count'],
                        'source_url': item['source']['url']
                    })
        
        # 生成统计报告
        stats_file = f"{output_dir}/faq_processing_stats_{timestamp}.json"
        stats = self.generate_stats()
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        logger.info(f"处理后的数据已保存到:")
        logger.info(f"  JSON: {json_file}")
        logger.info(f"  CSV: {csv_file}")
        logger.info(f"  统计: {stats_file}")
        
        return json_file, csv_file, stats_file

    def generate_stats(self) -> Dict:
        """生成处理统计信息"""
        if not self.processed_data:
            return {}

        # 分类统计
        category_stats = {}
        for item in self.processed_data:
            category = item['category_zh']
            if category not in category_stats:
                category_stats[category] = {
                    'count': 0,
                    'avg_word_count': 0,
                    'total_words': 0
                }
            category_stats[category]['count'] += 1
            category_stats[category]['total_words'] += item['word_count']

        # 计算平均字数
        for category in category_stats:
            if category_stats[category]['count'] > 0:
                category_stats[category]['avg_word_count'] = round(
                    category_stats[category]['total_words'] / category_stats[category]['count'], 1
                )

        # 食物关键词统计
        food_frequency = {}
        for item in self.processed_data:
            for food in item['related_foods']:
                food_frequency[food] = food_frequency.get(food, 0) + 1

        # 获取最常见的食物
        top_foods = sorted(food_frequency.items(), key=lambda x: x[1], reverse=True)[:10]

        # 标签统计
        tag_frequency = {}
        for item in self.processed_data:
            for tag in item['tags']:
                tag_frequency[tag] = tag_frequency.get(tag, 0) + 1

        top_tags = sorted(tag_frequency.items(), key=lambda x: x[1], reverse=True)[:10]

        # 质量指标
        total_words = sum(item['word_count'] for item in self.processed_data)
        avg_word_count = round(total_words / len(self.processed_data), 1) if self.processed_data else 0

        stats = {
            "processing_summary": {
                "total_faqs": len(self.processed_data),
                "total_words": total_words,
                "avg_word_count": avg_word_count,
                "processed_at": datetime.now().isoformat()
            },
            "category_distribution": category_stats,
            "top_food_keywords": dict(top_foods),
            "top_tags": dict(top_tags),
            "quality_metrics": {
                "faqs_with_key_points": len([item for item in self.processed_data if item['key_points']]),
                "faqs_with_related_foods": len([item for item in self.processed_data if item['related_foods']]),
                "faqs_with_tags": len([item for item in self.processed_data if item['tags']]),
                "avg_related_foods_per_faq": round(
                    sum(len(item['related_foods']) for item in self.processed_data) / len(self.processed_data), 1
                ) if self.processed_data else 0
            }
        }

        return stats

def main():
    """主函数"""
    try:
        logger.info("🔄 开始处理StillTasty FAQ数据...")

        # 查找最新的FAQ数据文件
        data_dir = "data"
        if not os.path.exists(data_dir):
            logger.error(f"数据目录不存在: {data_dir}")
            return

        # 查找FAQ数据文件
        faq_files = [f for f in os.listdir(data_dir) if f.startswith('stilltasty_faq_data_') and f.endswith('.json')]

        if not faq_files:
            logger.error("未找到FAQ数据文件")
            return

        # 使用最新的文件
        latest_file = sorted(faq_files)[-1]
        input_file = os.path.join(data_dir, latest_file)

        logger.info(f"📂 输入文件: {input_file}")

        # 创建处理器实例
        processor = FAQDataProcessor()

        # 处理数据
        processor.process_data_file(input_file)

        # 保存处理后的数据
        json_file, csv_file, stats_file = processor.save_processed_data()

        # 显示统计信息
        stats = processor.generate_stats()

        print(f"\n✅ FAQ数据处理完成！")
        print(f"📊 处理统计:")
        print(f"  总FAQ数: {stats['processing_summary']['total_faqs']}")
        print(f"  总字数: {stats['processing_summary']['total_words']}")
        print(f"  平均字数: {stats['processing_summary']['avg_word_count']}")

        print(f"\n📈 分类分布:")
        for category, data in stats['category_distribution'].items():
            print(f"  {category}: {data['count']} 个 (平均 {data['avg_word_count']} 字)")

        print(f"\n🥗 热门食物关键词:")
        for food, count in list(stats['top_food_keywords'].items())[:5]:
            print(f"  {food}: {count} 次")

        print(f"\n🏷️ 热门标签:")
        for tag, count in list(stats['top_tags'].items())[:5]:
            print(f"  {tag}: {count} 次")

        print(f"\n📁 文件已保存到:")
        print(f"  JSON: {json_file}")
        print(f"  CSV: {csv_file}")
        print(f"  统计: {stats_file}")

    except Exception as e:
        logger.error(f"处理过程中出现错误: {str(e)}")
        print(f"❌ 处理失败: {str(e)}")

if __name__ == "__main__":
    main()

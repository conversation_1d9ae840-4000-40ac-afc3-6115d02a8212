#!/usr/bin/env python3
"""
将 USDA 数据迁移到 Supabase 数据库
"""

import json
import pandas as pd
import os
from supabase import create_client, Client
from typing import Dict, List, Optional
import re

# Supabase 配置
SUPABASE_URL = "https://plefidqreqjnesamigoc.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZWZpZHFyZXFqbmVzYW1pZ29jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMTQ1ODUsImV4cCI6MjA2NTY5MDU4NX0.Ys99vv5Xys8np6rskFj_7TV7pTBKpn5UVj8Fn9ZBDtc"

def create_supabase_client() -> Client:
    """创建 Supabase 客户端"""
    return create_client(SUPABASE_URL, SUPABASE_KEY)

def normalize_search_key(name: str) -> str:
    """标准化搜索键"""
    # 转换为小写，移除特殊字符，用下划线替换空格
    key = re.sub(r'[^\w\s]', '', name.lower())
    key = re.sub(r'\s+', '_', key.strip())
    return key

def load_usda_data() -> Dict:
    """加载 USDA 数据"""
    try:
        # 读取 TypeScript 文件并提取 JSON 数据
        with open('lib/usda-food-database.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取 JSON 部分
        start = content.find('export const USDA_FOOD_DATABASE = ') + len('export const USDA_FOOD_DATABASE = ')
        end = content.find('export type FoodStorage')
        json_str = content[start:end].strip().rstrip(';')
        
        return json.loads(json_str)
    except Exception as e:
        print(f"加载 USDA 数据失败: {e}")
        return {}

def get_category_id(supabase: Client, category_name: str) -> Optional[int]:
    """获取类别 ID"""
    try:
        result = supabase.table('food_categories').select('id').eq('name', category_name).execute()
        if result.data:
            return result.data[0]['id']
        return None
    except Exception as e:
        print(f"获取类别 ID 失败: {e}")
        return None

def insert_food_item(supabase: Client, search_key: str, food_data: Dict) -> Optional[int]:
    """插入食物项目"""
    try:
        # 获取类别 ID
        category_id = get_category_id(supabase, food_data['category'])
        
        # 准备食物数据
        food_item = {
            'name': food_data['name'],
            'search_key': search_key,
            'category_id': category_id,
            'refrigerated_days': food_data['storage'].get('refrigerated'),
            'frozen_days': food_data['storage'].get('frozen'),
            'room_temperature_days': food_data['storage'].get('room_temperature'),
            'storage_tips': food_data.get('tips', []),
            'source': food_data.get('source', 'USDA'),
            'confidence': 0.98 if food_data.get('source') == 'USDA' else 0.90
        }
        
        # 移除 None 值
        food_item = {k: v for k, v in food_item.items() if v is not None}
        
        # 插入食物
        result = supabase.table('foods').insert(food_item).execute()
        if result.data:
            return result.data[0]['id']
        return None
    except Exception as e:
        print(f"插入食物失败 {search_key}: {e}")
        return None

def insert_food_alias(supabase: Client, food_id: int, alias: str, language: str = 'en'):
    """插入食物别名"""
    try:
        alias_data = {
            'food_id': food_id,
            'alias': alias,
            'language': language,
            'alias_type': 'name'
        }
        supabase.table('food_aliases').insert(alias_data).execute()
    except Exception as e:
        print(f"插入别名失败: {e}")

def create_chinese_mapping() -> Dict[str, str]:
    """创建中文映射"""
    return {
        # 水果类
        '苹果': 'apples',
        '香蕉': 'bananas', 
        '橙子': 'citrus_fruit',
        '橘子': 'citrus_fruit',
        '芒果': 'papaya,_mango,_feijoa,_passionfruit,_casaha_melon',
        '葡萄': 'grapes',
        '草莓': 'strawberries',
        '西瓜': 'melons',
        '梨': 'peaches,_nectarines,_plums,_pears,_sapote',
        '梨子': 'peaches,_nectarines,_plums,_pears,_sapote',
        '蓝莓': 'blueberries',
        '樱桃': 'berries',
        '菠萝': 'pineapple',
        '牛油果': 'avocados',
        '椰子': 'coconuts',
        
        # 蔬菜类
        '生菜': 'lettuce',
        '胡萝卜': 'carrots,_parsnips',
        '西红柿': 'tomatoes',
        '番茄': 'tomatoes',
        '土豆': 'potatoes',
        '马铃薯': 'potatoes',
        '洋葱': 'onions',
        '黄瓜': 'cucumbers',
        '西兰花': 'broccoli_and_broccoli_raab_(rapini)',
        '花椰菜': 'cauliflower',
        '芹菜': 'celery',
        '菠菜': 'greens',
        '白菜': 'cabbage',
        '茄子': 'eggplant',
        '辣椒': 'peppers',
        '玉米': 'corn_on_the_cob',
        '蘑菇': 'mushrooms',
        
        # 乳制品和蛋类
        '牛奶': 'milk',
        '奶酪': 'cheese',
        '鸡蛋': 'eggs',
        '黄油': 'butter',
        '奶油': 'cream',
        '酸奶': 'yogurt',
        
        # 肉类和海鲜
        '鸡肉': 'chicken',
        '牛肉': 'beef',
        '猪肉': 'pork',
        '火鸡': 'turkey',
        '鱼': 'lean_fish',
        '三文鱼': 'fatty_fish',
        '虾': 'shrimp,_crayfish',
        '螃蟹': 'crab_meat',
        
        # 其他
        '面包': 'bread',
        '米饭': 'rice',
        '意大利面': 'pasta',
        '豆腐': 'tofu',
        '泡菜': 'kimchi',
        '味噌': 'miso',
        '生抽': 'soy_sauce',
        '老抽': 'soy_sauce'
    }

def migrate_data():
    """执行数据迁移"""
    print("🚀 开始迁移数据到 Supabase...")
    
    # 创建 Supabase 客户端
    supabase = create_supabase_client()
    
    # 加载 USDA 数据
    usda_data = load_usda_data()
    print(f"加载了 {len(usda_data)} 条 USDA 数据")
    
    # 获取中文映射
    chinese_mapping = create_chinese_mapping()
    
    # 统计信息
    success_count = 0
    error_count = 0
    processed_keys = set()
    
    # 迁移数据
    for search_key, food_data in usda_data.items():
        try:
            # 跳过重复的中文条目（它们指向相同的英文数据）
            if search_key in processed_keys:
                continue
            
            # 标准化搜索键
            normalized_key = normalize_search_key(search_key)
            
            # 插入食物项目
            food_id = insert_food_item(supabase, normalized_key, food_data)
            
            if food_id:
                # 添加原始搜索键作为别名
                if search_key != normalized_key:
                    insert_food_alias(supabase, food_id, search_key, 'en')
                
                # 添加中文别名
                for chinese_name, english_key in chinese_mapping.items():
                    if english_key == search_key or normalize_search_key(english_key) == normalized_key:
                        insert_food_alias(supabase, food_id, chinese_name, 'zh')
                
                success_count += 1
                processed_keys.add(search_key)
                
                if success_count % 10 == 0:
                    print(f"已处理 {success_count} 条记录...")
            else:
                error_count += 1
                
        except Exception as e:
            print(f"处理 {search_key} 时出错: {e}")
            error_count += 1
    
    print(f"\n✅ 迁移完成!")
    print(f"成功: {success_count} 条")
    print(f"失败: {error_count} 条")
    print(f"总计: {len(usda_data)} 条")

def test_search():
    """测试搜索功能"""
    print("\n🔍 测试搜索功能...")
    
    supabase = create_supabase_client()
    
    test_queries = ['苹果', 'apple', 'mango', '芒果', 'chicken', '鸡肉']
    
    for query in test_queries:
        try:
            result = supabase.rpc('search_foods', {'search_term': query}).execute()
            if result.data:
                food = result.data[0]
                print(f"查询 '{query}': {food['name']} ({food['match_type']})")
            else:
                print(f"查询 '{query}': 未找到")
        except Exception as e:
            print(f"查询 '{query}' 失败: {e}")

if __name__ == "__main__":
    print("🍎 USDA 数据迁移到 Supabase")
    print("=" * 50)
    
    try:
        migrate_data()
        test_search()
        print("\n🎉 迁移和测试完成!")
    except Exception as e:
        print(f"❌ 迁移失败: {e}")

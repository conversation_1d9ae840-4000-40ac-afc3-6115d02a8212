#!/bin/bash

# 从.env.local读取配置
source .env.local

# SQL内容
SQL_CONTENT=$(cat supabase/migrations/003_create_posts_table.sql)

# 使用psql连接字符串
# 从Supabase URL提取数据库连接信息
DB_HOST="db.emopvngdwwghndzcfxvw.supabase.co"
DB_PORT="5432"
DB_NAME="postgres"
DB_USER="postgres"

echo "请在Supabase Dashboard中执行以下步骤："
echo ""
echo "1. 登录到 Supabase Dashboard (https://app.supabase.com)"
echo "2. 选择你的项目 (emopvngdwwghndzcfxvw)"
echo "3. 点击左侧菜单的 'SQL Editor'"
echo "4. 点击 'New Query'"
echo "5. 复制并粘贴下面的SQL："
echo ""
echo "=========================================="
cat supabase/migrations/003_create_posts_table.sql
echo "=========================================="
echo ""
echo "6. 点击 'Run' 按钮执行SQL"
echo ""
echo "执行成功后，运行: node scripts/import-faq-to-blog.js"
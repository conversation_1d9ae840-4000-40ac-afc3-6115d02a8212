#!/usr/bin/env python3
"""
FAQ数据导入脚本
将处理后的FAQ数据导入到Supabase数据库
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from supabase import create_client, Client

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('import_faq_data.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FAQDataImporter:
    def __init__(self):
        """初始化FAQ数据导入器"""
        # Supabase配置
        self.supabase_url = os.getenv('NEXT_PUBLIC_SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')  # 使用服务角色密钥
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("请设置NEXT_PUBLIC_SUPABASE_URL和SUPABASE_SERVICE_ROLE_KEY环境变量")
        
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
        self.imported_count = 0
        self.skipped_count = 0
        self.error_count = 0

    def get_category_id(self, category_name: str) -> Optional[int]:
        """获取FAQ分类ID"""
        try:
            result = self.supabase.table('faq_categories').select('id').eq('name', category_name).execute()
            
            if result.data:
                return result.data[0]['id']
            else:
                logger.warning(f"未找到分类: {category_name}")
                return None
                
        except Exception as e:
            logger.error(f"获取分类ID时出错: {str(e)}")
            return None

    def find_related_food_ids(self, food_keywords: List[str]) -> List[int]:
        """根据食物关键词查找相关的食物ID"""
        if not food_keywords:
            return []
        
        try:
            # 查询foods表中匹配的食物
            food_ids = []
            
            for keyword in food_keywords:
                # 精确匹配search_key
                result = self.supabase.table('foods').select('id').eq('search_key', keyword.lower()).execute()
                
                if result.data:
                    food_ids.extend([item['id'] for item in result.data])
                else:
                    # 模糊匹配name
                    result = self.supabase.table('foods').select('id').ilike('name', f'%{keyword}%').execute()
                    if result.data:
                        food_ids.extend([item['id'] for item in result.data])
            
            # 去重
            return list(set(food_ids))
            
        except Exception as e:
            logger.error(f"查找相关食物ID时出错: {str(e)}")
            return []

    def import_faq_item(self, faq_data: Dict) -> bool:
        """导入单个FAQ项目"""
        try:
            # 获取分类ID
            category_id = self.get_category_id(faq_data['category'])
            if not category_id:
                logger.warning(f"跳过FAQ - 无效分类: {faq_data['question'][:50]}...")
                self.skipped_count += 1
                return False
            
            # 检查是否已存在
            existing = self.supabase.table('faqs').select('id').eq('external_id', faq_data['id']).execute()
            
            if existing.data:
                logger.info(f"FAQ已存在，跳过: {faq_data['question'][:50]}...")
                self.skipped_count += 1
                return False
            
            # 准备FAQ数据
            faq_record = {
                'external_id': faq_data['id'],
                'question': faq_data['question'],
                'answer': faq_data['answer'],
                'answer_summary': faq_data.get('answer_summary', ''),
                'category_id': category_id,
                'key_points': faq_data.get('key_points', []),
                'related_foods': faq_data.get('related_foods', []),
                'tags': faq_data.get('tags', []),
                'word_count': faq_data.get('word_count', 0),
                'source_name': faq_data.get('source', {}).get('name', 'StillTasty.com'),
                'source_url': faq_data.get('source', {}).get('url', ''),
                'confidence': 0.95,  # StillTasty数据的置信度
                'processed_at': faq_data.get('source', {}).get('processed_at', datetime.now().isoformat())
            }
            
            # 插入FAQ记录
            result = self.supabase.table('faqs').insert(faq_record).execute()
            
            if result.data:
                faq_id = result.data[0]['id']
                logger.info(f"成功导入FAQ {self.imported_count + 1}: {faq_data['question'][:50]}...")
                
                # 创建FAQ与食物的关联
                self.create_food_relations(faq_id, faq_data.get('related_foods', []))
                
                self.imported_count += 1
                return True
            else:
                logger.error(f"插入FAQ失败: {faq_data['question'][:50]}...")
                self.error_count += 1
                return False
                
        except Exception as e:
            logger.error(f"导入FAQ时出错: {str(e)} - {faq_data['question'][:50]}...")
            self.error_count += 1
            return False

    def create_food_relations(self, faq_id: int, food_keywords: List[str]):
        """创建FAQ与食物的关联关系"""
        if not food_keywords:
            return
        
        try:
            # 查找相关的食物ID
            food_ids = self.find_related_food_ids(food_keywords)
            
            if not food_ids:
                return
            
            # 创建关联记录
            relations = []
            for food_id in food_ids:
                relations.append({
                    'faq_id': faq_id,
                    'food_id': food_id,
                    'relevance_score': 0.8  # 默认相关性分数
                })
            
            if relations:
                result = self.supabase.table('faq_food_relations').insert(relations).execute()
                if result.data:
                    logger.debug(f"创建了 {len(relations)} 个FAQ-食物关联")
                
        except Exception as e:
            logger.error(f"创建食物关联时出错: {str(e)}")

    def import_data_file(self, input_file: str):
        """导入FAQ数据文件"""
        logger.info(f"开始导入FAQ数据文件: {input_file}")
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                faq_data = json.load(f)
            
            logger.info(f"加载了 {len(faq_data)} 个FAQ项目")
            
            # 导入每个FAQ项目
            for i, faq_item in enumerate(faq_data, 1):
                try:
                    logger.info(f"处理FAQ {i}/{len(faq_data)}: {faq_item['question'][:50]}...")
                    self.import_faq_item(faq_item)
                    
                except Exception as e:
                    logger.error(f"处理FAQ {i} 时出错: {str(e)}")
                    self.error_count += 1
                    continue
            
            logger.info(f"FAQ数据导入完成！")
            logger.info(f"  成功导入: {self.imported_count} 个")
            logger.info(f"  跳过: {self.skipped_count} 个")
            logger.info(f"  错误: {self.error_count} 个")
            
        except Exception as e:
            logger.error(f"导入数据文件时出错: {str(e)}")
            raise

    def create_database_tables(self):
        """创建数据库表结构"""
        logger.info("创建FAQ数据库表结构...")
        
        try:
            # 读取SQL文件
            sql_file = "create_faq_tables.sql"
            if not os.path.exists(sql_file):
                logger.error(f"SQL文件不存在: {sql_file}")
                return False
            
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 执行SQL（注意：Supabase Python客户端不直接支持执行DDL）
            # 这里我们需要手动在Supabase控制台执行SQL，或使用其他方法
            logger.warning("请手动在Supabase SQL编辑器中执行 create_faq_tables.sql 文件")
            logger.warning("SQL文件路径: " + os.path.abspath(sql_file))
            
            return True
            
        except Exception as e:
            logger.error(f"创建数据库表时出错: {str(e)}")
            return False

    def verify_import(self):
        """验证导入结果"""
        try:
            # 统计FAQ总数
            result = self.supabase.table('faqs').select('id', count='exact').execute()
            total_faqs = result.count if hasattr(result, 'count') else len(result.data)
            
            # 统计各分类的FAQ数量
            result = self.supabase.table('faqs').select('category_id, faq_categories(name, name_zh)').execute()
            
            category_stats = {}
            for item in result.data:
                category_name = item['faq_categories']['name_zh']
                category_stats[category_name] = category_stats.get(category_name, 0) + 1
            
            # 统计FAQ-食物关联数量
            result = self.supabase.table('faq_food_relations').select('id', count='exact').execute()
            total_relations = result.count if hasattr(result, 'count') else len(result.data)
            
            logger.info(f"\n📊 导入验证结果:")
            logger.info(f"  总FAQ数: {total_faqs}")
            logger.info(f"  FAQ-食物关联数: {total_relations}")
            logger.info(f"  分类分布:")
            for category, count in sorted(category_stats.items()):
                logger.info(f"    {category}: {count} 个")
            
            return True
            
        except Exception as e:
            logger.error(f"验证导入结果时出错: {str(e)}")
            return False

def main():
    """主函数"""
    try:
        logger.info("🚀 开始FAQ数据导入...")
        
        # 查找最新的处理后FAQ数据文件
        data_dir = "data"
        if not os.path.exists(data_dir):
            logger.error(f"数据目录不存在: {data_dir}")
            return
        
        # 查找处理后的FAQ数据文件
        processed_files = [f for f in os.listdir(data_dir) if f.startswith('processed_faq_data_') and f.endswith('.json')]
        
        if not processed_files:
            logger.error("未找到处理后的FAQ数据文件")
            return
        
        # 使用最新的文件
        latest_file = sorted(processed_files)[-1]
        input_file = os.path.join(data_dir, latest_file)
        
        logger.info(f"📂 输入文件: {input_file}")
        
        # 创建导入器实例
        importer = FAQDataImporter()
        
        # 提醒创建数据库表
        logger.info("⚠️  请确保已在Supabase中执行了 create_faq_tables.sql")
        response = input("是否已创建数据库表？(y/n): ")
        if response.lower() != 'y':
            logger.info("请先在Supabase SQL编辑器中执行 create_faq_tables.sql，然后重新运行此脚本")
            return
        
        # 导入数据
        importer.import_data_file(input_file)
        
        # 验证导入结果
        importer.verify_import()
        
        print(f"\n✅ FAQ数据导入完成！")
        print(f"📊 导入统计:")
        print(f"  成功导入: {importer.imported_count} 个FAQ")
        print(f"  跳过: {importer.skipped_count} 个FAQ")
        print(f"  错误: {importer.error_count} 个FAQ")
        
    except Exception as e:
        logger.error(f"导入过程中出现错误: {str(e)}")
        print(f"❌ 导入失败: {str(e)}")

if __name__ == "__main__":
    main()

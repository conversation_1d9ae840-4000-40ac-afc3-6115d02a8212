#!/usr/bin/env node
/**
 * 清理已导入的StillTasty数据中的HTML标签和广告内容
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const SUPABASE_URL = "https://plefidqreqjnesamigoc.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZWZpZHFyZXFqbmVzYW1pZ29jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMTQ1ODUsImV4cCI6MjA2NTY5MDU4NX0.Ys99vv5Xys8np6rskFj_7TV7pTBKpn5UVj8Fn9ZBDtc";

// 创建 Supabase 客户端
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * 清理HTML标签和多余的空白字符
 */
function cleanHtmlText(text) {
  if (!text) return "";
  
  // 移除广告相关的内容
  text = text.replace(/<!--- Mobile ads.*?<!--- END Mobile ads.*?>/gs, '');
  text = text.replace(/<script.*?<\/script>/gs, '');
  text = text.replace(/<!-- .*? -->/gs, '');
  text = text.replace(/data-freestar-ad.*?>/g, '>');
  text = text.replace(/freestar\.config.*?;/g, '');
  
  // 移除所有HTML标签
  text = text.replace(/<[^>]+>/g, '');
  
  // 解码HTML实体
  text = text.replace(/&amp;/g, '&')
             .replace(/&lt;/g, '<')
             .replace(/&gt;/g, '>')
             .replace(/&quot;/g, '"')
             .replace(/&#39;/g, "'")
             .replace(/&nbsp;/g, ' ');
  
  // 移除多余的空白字符、制表符和换行符
  text = text.replace(/[\t\n\r]+/g, ' ');
  text = text.replace(/\s+/g, ' ').trim();
  
  // 如果清理后的文本太短或只包含无意义内容，返回空字符串
  if (text.length < 3 || /^[\s\-_\.]*$/.test(text)) {
    return "";
  }
  
  return text;
}

/**
 * 检查文本是否是有效的存储建议
 */
function isValidStorageTip(text) {
  if (!text || text.length < 10) return false;
  
  // 过滤掉包含广告、脚本或无意义内容的文本
  const invalidPatterns = [
    /freestar/i,
    /advertisement/i,
    /data-freestar/i,
    /googletag/i,
    /^[\s\-_\.]*$/,
    /^\d+[\s\-]*$/,
    /^[^a-zA-Z]*$/
  ];
  
  for (const pattern of invalidPatterns) {
    if (pattern.test(text)) return false;
  }
  
  // 检查是否包含有用的存储相关关键词
  const storageKeywords = [
    'refrigerat', 'freez', 'room temperature', 'pantry', 'store', 'keep',
    'best quality', 'fresh', 'shelf life', 'expir', 'last', 'maintain'
  ];
  
  const lowerText = text.toLowerCase();
  return storageKeywords.some(keyword => lowerText.includes(keyword));
}

/**
 * 清理存储建议数组
 */
function cleanStorageTips(tips) {
  if (!Array.isArray(tips)) return [];
  
  const cleanedTips = tips
    .map(tip => cleanHtmlText(tip))
    .filter(tip => isValidStorageTip(tip))
    .slice(0, 5); // 限制最多5个建议
  
  return cleanedTips;
}

/**
 * 获取所有需要清理的食物数据
 */
async function getFoodsToClean() {
  try {
    const { data, error } = await supabase
      .from('foods')
      .select('id, storage_tips')
      .not('storage_tips', 'is', null);
    
    if (error) {
      console.error('获取食物数据失败:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('获取食物数据异常:', error);
    return [];
  }
}

/**
 * 更新单个食物的存储建议
 */
async function updateFoodStorageTips(foodId, cleanedTips) {
  try {
    const { error } = await supabase
      .from('foods')
      .update({ storage_tips: cleanedTips })
      .eq('id', foodId);
    
    if (error) {
      console.error(`更新食物 ${foodId} 失败:`, error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`更新食物 ${foodId} 异常:`, error);
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('开始清理StillTasty数据...');
  
  // 获取所有需要清理的食物数据
  const foods = await getFoodsToClean();
  console.log(`找到 ${foods.length} 个食物需要清理存储建议`);
  
  let cleanedCount = 0;
  let skippedCount = 0;
  
  for (const food of foods) {
    const originalTips = food.storage_tips;
    const cleanedTips = cleanStorageTips(originalTips);
    
    // 检查是否有变化
    const hasChanges = JSON.stringify(originalTips) !== JSON.stringify(cleanedTips);
    
    if (hasChanges) {
      const success = await updateFoodStorageTips(food.id, cleanedTips);
      if (success) {
        cleanedCount++;
        console.log(`✅ 清理食物 ${food.id}: ${originalTips?.length || 0} -> ${cleanedTips.length} 个建议`);
      }
    } else {
      skippedCount++;
    }
    
    // 添加小延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n清理完成!');
  console.log(`✅ 已清理: ${cleanedCount} 个食物`);
  console.log(`⏭️  跳过: ${skippedCount} 个食物`);
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  cleanHtmlText,
  isValidStorageTip,
  cleanStorageTips
};

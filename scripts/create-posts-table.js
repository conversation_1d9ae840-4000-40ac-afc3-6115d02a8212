const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Supabase配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('请确保在.env.local中设置了SUPABASE_URL和SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createPostsTable() {
  try {
    console.log('开始创建posts表...');
    
    // 读取SQL文件
    const sqlContent = fs.readFileSync(
      path.join(__dirname, '../supabase/migrations/003_create_posts_table.sql'), 
      'utf8'
    );
    
    // 执行SQL - 使用rpc调用执行原始SQL
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: sqlContent
    });
    
    if (error) {
      // 如果rpc方法不存在，尝试直接使用REST API
      console.log('尝试使用REST API执行SQL...');
      
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseServiceKey,
          'Authorization': `Bearer ${supabaseServiceKey}`
        },
        body: JSON.stringify({ sql: sqlContent })
      });
      
      if (!response.ok) {
        // 如果REST API也失败，说明需要手动执行
        console.log('\n⚠️  无法自动执行SQL，请手动在Supabase Dashboard中执行以下SQL：\n');
        console.log('='*60);
        console.log(sqlContent);
        console.log('='*60);
        console.log('\n请复制上述SQL到Supabase SQL Editor中执行。');
        return false;
      }
    }
    
    console.log('✅ Posts表创建成功！');
    
    // 验证表是否创建成功
    const { data: testData, error: testError } = await supabase
      .from('posts')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.error('表验证失败:', testError);
      return false;
    }
    
    console.log('✅ Posts表验证成功！');
    return true;
    
  } catch (err) {
    console.error('创建表时出错:', err);
    return false;
  }
}

// 执行创建表
createPostsTable().then(success => {
  if (success) {
    console.log('\n现在可以运行以下命令导入FAQ数据：');
    console.log('node scripts/import-faq-to-blog.js');
  }
});
#!/usr/bin/env node
/**
 * 清理并重新导入StillTasty数据
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const SUPABASE_URL = "https://plefidqreqjnesamigoc.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZWZpZHFyZXFqbmVzYW1pZ29jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMTQ1ODUsImV4cCI6MjA2NTY5MDU4NX0.Ys99vv5Xys8np6rskFj_7TV7pTBKpn5UVj8Fn9ZBDtc";

// 创建 Supabase 客户端
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * 删除所有StillTasty数据（ID >= 601）
 */
async function deleteStillTastyData() {
  try {
    console.log('正在删除现有的StillTasty数据...');
    
    const { error } = await supabase
      .from('foods')
      .delete()
      .gte('id', 601);
    
    if (error) {
      console.error('删除数据失败:', error);
      return false;
    }
    
    console.log('✅ 成功删除StillTasty数据');
    return true;
  } catch (error) {
    console.error('删除数据异常:', error);
    return false;
  }
}

/**
 * 重新导入StillTasty数据
 */
async function reimportStillTastyData() {
  try {
    console.log('正在重新导入StillTasty数据...');
    
    // 运行导入脚本
    const { spawn } = require('child_process');
    
    return new Promise((resolve, reject) => {
      const importProcess = spawn('node', ['scripts/import-stilltasty-data.js'], {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      
      importProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ 成功重新导入StillTasty数据');
          resolve(true);
        } else {
          console.error('❌ 重新导入失败，退出码:', code);
          resolve(false);
        }
      });
      
      importProcess.on('error', (error) => {
        console.error('❌ 重新导入异常:', error);
        resolve(false);
      });
    });
  } catch (error) {
    console.error('重新导入异常:', error);
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('开始清理并重新导入StillTasty数据...\n');
  
  // 1. 删除现有数据
  const deleteSuccess = await deleteStillTastyData();
  if (!deleteSuccess) {
    console.error('❌ 删除数据失败，停止操作');
    process.exit(1);
  }
  
  console.log('');
  
  // 2. 重新导入数据
  const importSuccess = await reimportStillTastyData();
  if (!importSuccess) {
    console.error('❌ 重新导入失败');
    process.exit(1);
  }
  
  console.log('\n🎉 清理并重新导入完成！');
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

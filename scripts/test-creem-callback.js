#!/usr/bin/env node

/**
 * 测试 Creem 回调的脚本
 * 使用方法: node scripts/test-creem-callback.js <order_no>
 */

const orderNo = process.argv[2];

if (!orderNo) {
  console.error('❌ 请提供订单号');
  console.log('使用方法: node scripts/test-creem-callback.js <order_no>');
  process.exit(1);
}

// 获取环境配置
const apiUrl = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';
const callbackUrl = `${apiUrl}/api/creem-callback`;

console.log('🔄 测试 Creem 回调...');
console.log(`📍 回调 URL: ${callbackUrl}`);
console.log(`📋 订单号: ${orderNo}`);

// 模拟 Creem 的 checkout.completed 事件
const callbackData = {
  id: `evt_test_${Date.now()}`,
  eventType: 'checkout.completed',
  created_at: Date.now(),
  object: {
    id: `ch_test_${Date.now()}`,
    object: 'checkout',
    request_id: orderNo, // 订单号在这里！
    order: {
      object: 'order',
      id: `ord_test_${Date.now()}`,
      amount: 3990, // 39.9 元
      currency: 'CNY',
      status: 'paid',
      type: 'recurring',
    },
    product: {
      id: 'prod_test',
      name: 'HowLongFresh Pro',
      price: 3990,
      currency: 'CNY',
      billing_type: 'recurring',
      billing_period: 'monthly',
    },
    customer: {
      email: '<EMAIL>',
      name: 'Test User',
    },
    status: 'completed',
    mode: 'test'
  }
};

console.log('\n📦 回调数据:');
console.log(JSON.stringify(callbackData, null, 2));

// 发送回调请求
fetch(callbackUrl, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    // 模拟 Creem 的请求头
    'X-Creem-Signature': 'test_signature',
    'X-Creem-Event': 'payment.succeeded'
  },
  body: JSON.stringify(callbackData)
})
.then(response => response.json())
.then(data => {
  console.log('\n✅ 回调响应:');
  console.log(JSON.stringify(data, null, 2));
  
  if (data.code === 0) {
    console.log('\n🎉 回调成功！');
    console.log('📌 下一步:');
    console.log('1. 访问支付成功页面查看订单状态');
    console.log('2. 检查"我的订单"页面是否显示订单');
    console.log('3. 检查用户额度是否增加');
  } else {
    console.error('\n❌ 回调失败:', data.message);
  }
})
.catch(error => {
  console.error('\n❌ 请求失败:', error.message);
  console.log('\n💡 提示:');
  console.log('1. 确保开发服务器正在运行 (pnpm dev)');
  console.log('2. 检查 API 路径是否正确');
  console.log('3. 查看服务器日志了解详细错误');
});
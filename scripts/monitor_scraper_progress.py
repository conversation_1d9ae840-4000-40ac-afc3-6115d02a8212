#!/usr/bin/env python3
"""
监控StillTasty爬虫进度的脚本
"""

import os
import time
import json
from datetime import datetime

def monitor_progress():
    """监控爬虫进度"""
    log_file = "stilltasty_full_scraper.log"
    data_dir = "data"
    
    print("🔍 StillTasty爬虫进度监控")
    print("=" * 50)
    
    categories = [
        "Fruits (水果)",
        "Vegetables (蔬菜)", 
        "Dairy & Eggs (奶制品和鸡蛋)",
        "Meat & Poultry (肉类和家禽)",
        "Fish & Shellfish (鱼类和贝类)",
        "Nuts, Grains & Pasta (坚果、谷物和面食)",
        "Condiments & Oils (调料和油类)",
        "Snacks & Baked Goods (零食和烘焙食品)",
        "Herbs & Spices (香草和香料)",
        "Beverages (饮料)"
    ]
    
    while True:
        try:
            # 检查日志文件
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                
                # 统计进度
                current_category = None
                completed_categories = 0
                current_items = 0
                
                for category in categories:
                    if f"开始爬取类别: {category}" in log_content:
                        current_category = category
                    if f"完成类别" in log_content and category.split(' (')[0] in log_content:
                        completed_categories += 1
                
                # 统计当前类别的项目数
                current_items = log_content.count("成功提取:")
                
                # 检查是否有数据文件生成
                data_files = []
                if os.path.exists(data_dir):
                    data_files = [f for f in os.listdir(data_dir) if f.startswith('stilltasty_data_') and f.endswith('.json')]
                
                # 显示进度
                print(f"\r⏰ {datetime.now().strftime('%H:%M:%S')} | "
                      f"已完成类别: {completed_categories}/10 | "
                      f"当前类别: {current_category or '准备中'} | "
                      f"已提取项目: {current_items} | "
                      f"数据文件: {len(data_files)}", end="")
                
                # 检查是否完成
                if "爬取完成！总共获取了" in log_content:
                    print(f"\n\n✅ 爬取完成！")
                    
                    # 显示最终统计
                    lines = log_content.split('\n')
                    for line in lines:
                        if "总共获取了" in line:
                            print(f"📊 {line.strip()}")
                            break
                    
                    # 显示数据文件
                    if data_files:
                        latest_file = sorted(data_files)[-1]
                        print(f"📁 最新数据文件: {os.path.join(data_dir, latest_file)}")
                    
                    break
                
                # 检查是否有错误
                if "爬取失败" in log_content:
                    print(f"\n\n❌ 爬取过程中出现错误，请检查日志文件: {log_file}")
                    break
                    
            else:
                print(f"\r⏰ {datetime.now().strftime('%H:%M:%S')} | 等待爬虫启动...", end="")
            
            time.sleep(10)  # 每10秒更新一次
            
        except KeyboardInterrupt:
            print(f"\n\n⏹️ 监控已停止")
            break
        except Exception as e:
            print(f"\n\n❌ 监控出错: {e}")
            break

if __name__ == "__main__":
    monitor_progress()

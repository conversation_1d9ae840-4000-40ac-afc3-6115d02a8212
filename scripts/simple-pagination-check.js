const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

// 分类配置
const CATEGORIES = {
  'fruits': { expectedFoods: 235, expectedPages: 10, name: '水果' },
  'grains': { expectedFoods: 219, expectedPages: 10, name: '谷物' },
  'meat': { expectedFoods: 212, expectedPages: 9, name: '肉类' },
  'vegetables': { expectedFoods: 172, expectedPages: 8, name: '蔬菜' },
  'seafood': { expectedFoods: 129, expectedPages: 6, name: '海鲜' },
  'condiments': { expectedFoods: 107, expectedPages: 5, name: '调料' },
  'snacks': { expectedFoods: 106, expectedPages: 5, name: '零食' },
  'beverages': { expectedFoods: 99, expectedPages: 5, name: '饮料' },
  'spices': { expectedFoods: 62, expectedPages: 3, name: '香料' },
  'dairy': { expectedFoods: 13, expectedPages: 1, name: '乳制品' }
};

const BASE_URL = 'http://localhost:3001';

async function quickCheck() {
  console.log('🔍 快速检查分类分页状态...\n');
  
  const results = [];
  
  for (const [categorySlug, categoryInfo] of Object.entries(CATEGORIES)) {
    try {
      console.log(`检查 ${categoryInfo.name} (${categorySlug}):`);
      
      // 检查第1页
      const url1 = `${BASE_URL}/zh/category/${categorySlug}`;
      const { stdout: page1Html } = await execAsync(`curl -s "${url1}"`);
      
      // 获取食物总数
      const totalMatch = page1Html.match(/共 (\d+) 种食物/);
      const totalFoods = totalMatch ? parseInt(totalMatch[1]) : 0;
      
      // 检查是否有下一页链接
      const hasNextPage = page1Html.includes('下一页') && page1Html.includes('page=2');
      
      console.log(`  总数: ${totalFoods}/${categoryInfo.expectedFoods} ${totalFoods === categoryInfo.expectedFoods ? '✅' : '❌'}`);
      console.log(`  下一页: ${hasNextPage ? '有' : '无'} ${(categoryInfo.expectedPages > 1) === hasNextPage ? '✅' : '❌'}`);
      
      // 如果应该有多页，检查第2页是否可访问
      if (categoryInfo.expectedPages > 1) {
        const url2 = `${BASE_URL}/zh/category/${categorySlug}?page=2`;
        try {
          const { stdout: page2Html } = await execAsync(`curl -s "${url2}"`);
          const page2HasContent = page2Html.includes('共 ') && page2Html.includes('种食物');
          console.log(`  第2页: ${page2HasContent ? '可访问' : '无法访问'} ${page2HasContent ? '✅' : '❌'}`);
          
          // 检查第2页是否有上一页链接
          const hasPrevPage = page2Html.includes('上一页') || page2Html.includes('page=1');
          console.log(`  上一页: ${hasPrevPage ? '有' : '无'} ${hasPrevPage ? '✅' : '❌'}`);
          
        } catch (e) {
          console.log(`  第2页: 访问失败 ❌`);
        }
      }
      
      const isWorking = totalFoods === categoryInfo.expectedFoods && 
                       (categoryInfo.expectedPages > 1) === hasNextPage;
      
      results.push({
        category: categoryInfo.name,
        slug: categorySlug,
        working: isWorking,
        totalFoods: totalFoods,
        expectedFoods: categoryInfo.expectedFoods,
        hasNextPage: hasNextPage,
        expectedPages: categoryInfo.expectedPages
      });
      
      console.log(`  状态: ${isWorking ? '✅ 正常' : '❌ 有问题'}\n`);
      
    } catch (error) {
      console.log(`  ❌ 检查失败: ${error.message}\n`);
      results.push({
        category: categoryInfo.name,
        slug: categorySlug,
        working: false,
        error: error.message
      });
    }
    
    // 短暂延迟
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // 总结
  console.log('=' .repeat(50));
  console.log('📊 检查结果总结');
  console.log('=' .repeat(50));
  
  const workingCount = results.filter(r => r.working).length;
  const totalCount = results.length;
  
  console.log(`正常工作: ${workingCount}/${totalCount}`);
  
  if (workingCount < totalCount) {
    console.log('\n❌ 有问题的分类:');
    results.filter(r => !r.working).forEach(r => {
      if (r.error) {
        console.log(`  • ${r.category}: ${r.error}`);
      } else {
        const issues = [];
        if (r.totalFoods !== r.expectedFoods) {
          issues.push(`食物数量不匹配(${r.totalFoods}/${r.expectedFoods})`);
        }
        if ((r.expectedPages > 1) !== r.hasNextPage) {
          issues.push(`分页状态不正确`);
        }
        console.log(`  • ${r.category}: ${issues.join(', ')}`);
      }
    });
  } else {
    console.log('\n🎉 所有分类都正常工作！');
  }
}

// 运行检查
quickCheck().catch(console.error);

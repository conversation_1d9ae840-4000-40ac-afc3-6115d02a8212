#!/usr/bin/env python3
"""
将 StillTasty 数据导入到 Supabase 数据库
"""

import json
import os
import re
from supabase import create_client, Client
from typing import Dict, List, Optional
import html

# Supabase 配置
SUPABASE_URL = "https://plefidqreqjnesamigoc.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZWZpZHFyZXFqbmVzYW1pZ29jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMTQ1ODUsImV4cCI6MjA2NTY5MDU4NX0.Ys99vv5Xys8np6rskFj_7TV7pTBKpn5UVj8Fn9ZBDtc"

def create_supabase_client() -> Client:
    """创建 Supabase 客户端"""
    return create_client(SUPABASE_URL, SUPABASE_KEY)

def clean_html_text(text: str) -> str:
    """清理HTML标签和多余的空白字符"""
    if not text:
        return ""
    
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    # 解码HTML实体
    text = html.unescape(text)
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text).strip()
    # 移除制表符和换行符
    text = re.sub(r'[\t\n\r]+', ' ', text)
    
    return text

def normalize_search_key(name: str) -> str:
    """标准化搜索键"""
    # 转换为小写，移除特殊字符，用下划线替换空格
    key = re.sub(r'[^\w\s]', '', name.lower())
    key = re.sub(r'\s+', '_', key.strip())
    return key

def get_category_id(supabase: Client, category_name: str) -> Optional[int]:
    """获取或创建分类ID"""
    try:
        # 分类映射
        category_mapping = {
            'fruits': 'Fruits',
            'vegetables': 'Vegetables', 
            'meat': 'Meat & Poultry',
            'seafood': 'Seafood',
            'dairy': 'Dairy & Eggs',
            'grains': 'Grains & Bread',
            'beverages': 'Beverages',
            'snacks': 'Snacks & Sweets',
            'condiments': 'Condiments & Sauces',
            'spices': 'Herbs & Spices'
        }
        
        mapped_name = category_mapping.get(category_name, category_name)
        
        # 查找现有分类
        result = supabase.table('food_categories').select('id').eq('name', mapped_name).execute()
        
        if result.data:
            return result.data[0]['id']
        
        # 创建新分类
        result = supabase.table('food_categories').insert({
            'name': mapped_name,
            'description': f'{mapped_name} category from StillTasty data'
        }).execute()
        
        if result.data:
            return result.data[0]['id']
        
        return None
    except Exception as e:
        print(f"获取分类ID失败 {category_name}: {e}")
        return None

def extract_storage_tips(food_item: Dict) -> List[str]:
    """提取存储建议"""
    tips = []
    
    # 从tips字段提取
    if 'tips' in food_item and food_item['tips']:
        for tip in food_item['tips']:
            cleaned_tip = clean_html_text(tip)
            if cleaned_tip and len(cleaned_tip) > 10:  # 过滤太短的提示
                tips.append(cleaned_tip)
    
    # 从storage文本中提取有用信息
    storage = food_item.get('storage', {})
    for storage_type, storage_info in storage.items():
        if isinstance(storage_info, dict) and 'text' in storage_info:
            cleaned_text = clean_html_text(storage_info['text'])
            # 提取有用的存储建议（不是HTML垃圾）
            if cleaned_text and len(cleaned_text) > 20 and 'best quality' in cleaned_text.lower():
                tips.append(f"{storage_type.replace('_', ' ').title()}: {cleaned_text}")
    
    return tips[:5]  # 限制最多5个建议

def insert_food_item(supabase: Client, food_item: Dict) -> Optional[int]:
    """插入食物项目"""
    try:
        # 获取分类ID
        category_id = get_category_id(supabase, food_item['category_id'])
        if not category_id:
            print(f"无法获取分类ID: {food_item['category_id']}")
            return None
        
        # 准备搜索键
        search_key = normalize_search_key(food_item['name_clean'])
        
        # 检查是否已存在
        existing = supabase.table('foods').select('id').eq('search_key', search_key).execute()
        if existing.data:
            print(f"食物已存在，跳过: {food_item['name_clean']}")
            return existing.data[0]['id']
        
        # 提取存储天数
        storage = food_item.get('storage', {})
        room_temp_days = storage.get('room_temperature', {}).get('days')
        refrigerated_days = storage.get('refrigerated', {}).get('days')
        frozen_days = storage.get('frozen', {}).get('days')
        
        # 提取存储建议
        storage_tips = extract_storage_tips(food_item)
        
        # 准备食物数据
        food_data = {
            'name': food_item['name_clean'],
            'search_key': search_key,
            'category_id': category_id,
            'room_temperature_days': room_temp_days,
            'refrigerated_days': refrigerated_days,
            'frozen_days': frozen_days,
            'storage_tips': storage_tips,
            'source': 'StillTasty',
            'confidence': 0.95  # StillTasty数据置信度
        }
        
        # 移除None值
        food_data = {k: v for k, v in food_data.items() if v is not None}
        
        # 插入食物
        result = supabase.table('foods').insert(food_data).execute()
        if result.data:
            food_id = result.data[0]['id']
            
            # 添加关键词作为别名
            if 'keywords' in food_item and food_item['keywords']:
                for keyword in food_item['keywords'][:3]:  # 限制前3个关键词
                    if keyword and len(keyword) > 2:
                        try:
                            supabase.table('food_aliases').insert({
                                'food_id': food_id,
                                'alias': keyword,
                                'language': 'en'
                            }).execute()
                        except:
                            pass  # 忽略重复别名错误
            
            return food_id
        
        return None
    except Exception as e:
        print(f"插入食物失败 {food_item.get('name_clean', 'unknown')}: {e}")
        return None

def load_stilltasty_data() -> List[Dict]:
    """加载StillTasty数据"""
    try:
        data_file = 'processed_data/processed_stilltasty_data_20250718_132503.json'
        with open(data_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载StillTasty数据失败: {e}")
        return []

def migrate_stilltasty_data():
    """执行StillTasty数据迁移"""
    print("🚀 开始导入StillTasty数据到Supabase...")
    
    # 创建Supabase客户端
    supabase = create_supabase_client()
    
    # 加载数据
    stilltasty_data = load_stilltasty_data()
    print(f"加载了 {len(stilltasty_data)} 条StillTasty数据")
    
    if not stilltasty_data:
        print("❌ 没有数据可导入")
        return
    
    # 统计信息
    success_count = 0
    error_count = 0
    skipped_count = 0
    
    # 按分类统计
    category_stats = {}
    
    # 处理数据
    for i, food_item in enumerate(stilltasty_data):
        try:
            # 统计分类
            category = food_item.get('category_id', 'unknown')
            category_stats[category] = category_stats.get(category, 0) + 1
            
            # 插入食物
            food_id = insert_food_item(supabase, food_item)
            
            if food_id:
                success_count += 1
            else:
                error_count += 1
            
            # 进度显示
            if (i + 1) % 100 == 0:
                print(f"已处理 {i + 1}/{len(stilltasty_data)} 条记录...")
                
        except Exception as e:
            print(f"处理第 {i+1} 条记录时出错: {e}")
            error_count += 1
    
    # 输出结果
    print(f"\n✅ StillTasty数据导入完成!")
    print(f"成功导入: {success_count} 条")
    print(f"导入失败: {error_count} 条")
    print(f"总计处理: {len(stilltasty_data)} 条")
    
    print(f"\n📊 分类统计:")
    for category, count in sorted(category_stats.items()):
        print(f"  {category}: {count} 种食物")

def test_search():
    """测试搜索功能"""
    print("\n🔍 测试StillTasty数据搜索...")
    
    supabase = create_supabase_client()
    
    test_queries = ['apple', 'chicken', 'bread', 'milk', 'tomato']
    
    for query in test_queries:
        try:
            result = supabase.rpc('search_foods', {'search_term': query}).execute()
            if result.data:
                foods = [f"{food['name']} ({food['source']})" for food in result.data[:3]]
                print(f"查询 '{query}': {', '.join(foods)}")
            else:
                print(f"查询 '{query}': 未找到")
        except Exception as e:
            print(f"查询 '{query}' 失败: {e}")

if __name__ == "__main__":
    print("🍎 StillTasty数据导入到Supabase")
    print("=" * 50)
    
    try:
        migrate_stilltasty_data()
        test_search()
        print("\n🎉 导入和测试完成!")
    except Exception as e:
        print(f"❌ 导入失败: {e}")

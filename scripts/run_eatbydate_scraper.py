#!/usr/bin/env python3
"""
EatByDate爬虫运行脚本

一键运行爬取和数据处理流程
"""

import os
import sys
import subprocess
import json
import glob
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖是否安装"""
    logger.info("Checking dependencies...")
    
    try:
        import selenium
        from webdriver_manager.chrome import ChromeDriverManager
        logger.info("✅ All dependencies are installed")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.info("Please install dependencies with: pip install -r requirements.txt")
        return False

def run_test():
    """运行测试"""
    logger.info("Running scraper test...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_eatbydate_scraper.py"
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            logger.info("✅ Test passed")
            return True
        else:
            logger.error("❌ Test failed")
            logger.error(f"Error output: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return False

def run_scraper():
    """运行完整爬虫"""
    logger.info("Running full scraper...")
    
    try:
        result = subprocess.run([
            sys.executable, "eatbydate_scraper.py"
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            logger.info("✅ Scraping completed successfully")
            return True
        else:
            logger.error("❌ Scraping failed")
            logger.error(f"Error output: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Scraper execution failed: {e}")
        return False

def find_latest_data_file():
    """查找最新的数据文件"""
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    
    if not os.path.exists(data_dir):
        return None
    
    # 查找所有JSON数据文件
    pattern = os.path.join(data_dir, "eatbydate_data_*.json")
    files = glob.glob(pattern)
    
    if not files:
        return None
    
    # 返回最新的文件
    latest_file = max(files, key=os.path.getctime)
    return latest_file

def process_data(data_file):
    """处理数据"""
    logger.info(f"Processing data from {data_file}")
    
    try:
        result = subprocess.run([
            sys.executable, "process_eatbydate_data.py", data_file
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            logger.info("✅ Data processing completed successfully")
            return True
        else:
            logger.error("❌ Data processing failed")
            logger.error(f"Error output: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Data processing execution failed: {e}")
        return False

def show_results():
    """显示结果统计"""
    logger.info("Showing results...")
    
    # 查找处理后的数据
    processed_dir = os.path.join(os.path.dirname(__file__), "processed_data")
    
    if os.path.exists(processed_dir):
        json_files = glob.glob(os.path.join(processed_dir, "processed_eatbydate_data_*.json"))
        if json_files:
            latest_processed = max(json_files, key=os.path.getctime)
            
            try:
                with open(latest_processed, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                logger.info(f"📊 Results Summary:")
                logger.info(f"   Total items processed: {len(data)}")
                
                # 统计分类
                categories = {}
                for item in data:
                    cat = item.get('category_zh', 'Unknown')
                    categories[cat] = categories.get(cat, 0) + 1
                
                logger.info(f"   Categories:")
                for cat, count in categories.items():
                    logger.info(f"     {cat}: {count} items")
                
                # 显示文件位置
                logger.info(f"📁 Output files:")
                logger.info(f"   Processed data: {latest_processed}")
                
                # 查找CSV和统计文件
                base_name = os.path.basename(latest_processed).replace('.json', '')
                csv_file = os.path.join(processed_dir, f"{base_name}.csv")
                stats_file = os.path.join(processed_dir, f"eatbydate_data_stats_{base_name.split('_')[-1]}.txt")
                
                if os.path.exists(csv_file):
                    logger.info(f"   CSV data: {csv_file}")
                if os.path.exists(stats_file):
                    logger.info(f"   Statistics: {stats_file}")
                
            except Exception as e:
                logger.error(f"Error reading results: {e}")

def main():
    """主函数"""
    print("🚀 EatByDate.com 食物数据爬虫")
    print("=" * 50)
    
    # 检查命令行参数
    mode = "full"  # 默认完整模式
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    
    if mode not in ["test", "full", "process"]:
        print("Usage: python run_eatbydate_scraper.py [test|full|process]")
        print("  test  - Run test only")
        print("  full  - Run full scraping and processing (default)")
        print("  process - Process existing data only")
        sys.exit(1)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    success = True
    
    if mode == "test":
        # 只运行测试
        success = run_test()
        
    elif mode == "process":
        # 只处理现有数据
        data_file = find_latest_data_file()
        if data_file:
            success = process_data(data_file)
            if success:
                show_results()
        else:
            logger.error("❌ No data file found to process")
            success = False
            
    else:  # full mode
        # 完整流程
        
        # 1. 运行测试
        logger.info("Step 1: Running test...")
        if not run_test():
            logger.error("❌ Test failed, aborting full scraping")
            sys.exit(1)
        
        # 2. 运行爬虫
        logger.info("Step 2: Running scraper...")
        if not run_scraper():
            logger.error("❌ Scraping failed")
            sys.exit(1)
        
        # 3. 处理数据
        logger.info("Step 3: Processing data...")
        data_file = find_latest_data_file()
        if data_file:
            if process_data(data_file):
                show_results()
            else:
                success = False
        else:
            logger.error("❌ No data file found after scraping")
            success = False
    
    if success:
        print("\n🎉 All operations completed successfully!")
        print("\n📝 Next steps:")
        print("1. Review the processed data files")
        print("2. Import data into your project database")
        print("3. Update your food search system")
    else:
        print("\n❌ Some operations failed. Please check the logs above.")
        sys.exit(1)

if __name__ == "__main__":
    main()

# EatByDate.com 食物数据爬虫

这个Python脚本用于从 https://eatbydate.com 网站采集食物保质期数据，按照不同分类进行组织。

## 功能特点

- 支持7个主要食物分类：奶制品、饮料、水果、谷物、其他、蛋白质、蔬菜
- 自动提取食物在不同存储条件下的保质期（室温、冷藏、冷冻）
- 支持无头浏览器模式，提高爬取效率
- 自动管理ChromeDriver，无需手动下载
- 输出JSON和CSV两种格式的数据
- 包含错误处理和日志记录

## 安装依赖

```bash
cd scripts
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```bash
python eatbydate_scraper.py
```

### 在代码中使用

```python
from eatbydate_scraper import EatByDateScraper

# 创建爬虫实例
scraper = EatByDateScraper(headless=True)

try:
    # 爬取所有分类数据
    scraper.scrape_all_categories()
    
    # 保存数据
    scraper.save_data()
    
finally:
    # 关闭浏览器
    scraper.close()
```

## 输出数据格式

爬取的数据包含以下字段：

- `name`: 食物名称
- `category`: 食物分类（中文）
- `url`: 原始页面URL
- `pantry_life`: 室温保存时间
- `refrigerator_life`: 冷藏保存时间
- `freezer_life`: 冷冻保存时间
- `description`: 食物描述
- `scraped_at`: 爬取时间

## 示例输出

```json
[
  {
    "name": "Milk",
    "category": "奶制品",
    "url": "https://eatbydate.com/dairy/milk/how-long-does-milk-last/",
    "pantry_life": "",
    "refrigerator_life": "5-7 days",
    "freezer_life": "3 months",
    "description": "Milk is a nutritious liquid...",
    "scraped_at": "2024-01-15T10:30:00"
  }
]
```

## 注意事项

1. **遵守网站使用条款**: 请确保遵守eatbydate.com的robots.txt和使用条款
2. **合理的爬取频率**: 脚本已内置延迟机制，避免对服务器造成过大压力
3. **数据准确性**: 爬取的数据仅供参考，实际使用时请验证准确性
4. **Chrome浏览器**: 需要系统安装Chrome浏览器

## 故障排除

### Chrome浏览器问题
如果遇到Chrome相关错误，请确保：
- 系统已安装Chrome浏览器
- Chrome版本较新
- 网络连接正常

### 爬取失败
如果爬取失败，可能的原因：
- 网站结构发生变化
- 网络连接问题
- 被网站反爬虫机制阻止

可以尝试：
- 增加延迟时间
- 更换User-Agent
- 检查网站是否可正常访问

## 自定义配置

可以通过修改 `EatByDateScraper` 类来自定义：

- 爬取的分类
- 每个分类的最大食物数量
- 延迟时间
- 输出格式

## 快速开始

### 1. 环境设置
```bash
cd scripts
python setup_scraper.py
```

### 2. 运行测试
```bash
python run_eatbydate_scraper.py test
```

### 3. 运行完整爬虫
```bash
python run_eatbydate_scraper.py full
```

## 脚本说明

- `eatbydate_scraper.py` - 主爬虫程序
- `test_eatbydate_scraper.py` - 测试脚本（爬取少量数据验证功能）
- `process_eatbydate_data.py` - 数据处理脚本
- `run_eatbydate_scraper.py` - 一键运行脚本
- `setup_scraper.py` - 环境设置脚本
- `requirements.txt` - Python依赖列表

## 数据集成

爬取的数据可以直接导入到项目的食物数据库中，与现有的USDA数据形成补充。建议：

1. 数据清洗和验证
2. 与USDA数据进行对比
3. 标记数据来源
4. 定期更新数据

## 项目集成示例

```javascript
// 在项目中使用处理后的数据
import processedData from './processed_data/processed_eatbydate_data_latest.json';

// 集成到现有的食物搜索系统
function searchFoodWithEatByDate(query) {
  const eatByDateResults = processedData.filter(item =>
    item.name.toLowerCase().includes(query.toLowerCase())
  );

  return eatByDateResults.map(item => ({
    name: item.name,
    category: item.category,
    storage: {
      roomTemp: item.storage_conditions.room_temperature.duration,
      refrigerated: item.storage_conditions.refrigerated.duration,
      frozen: item.storage_conditions.frozen.duration
    },
    source: 'EatByDate.com',
    confidence: item.source.confidence
  }));
}
```

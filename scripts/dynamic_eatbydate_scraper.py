#!/usr/bin/env python3
"""
动态EatByDate.com爬虫 - 自动发现分类页面中的所有食物链接
解决了原版爬虫只爬取少数预定义食物的问题
"""

import os
import json
import csv
import time
import random
import logging
from datetime import datetime
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dynamic_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DynamicEatByDateScraper:
    def __init__(self, headless=True):
        """初始化爬虫"""
        self.base_url = "https://eatbydate.com"
        self.driver = self._setup_driver(headless)
        self.scraped_data = []
        
        # 分类页面URL，用于动态发现食物链接
        self.category_urls = {
            "奶制品": "https://eatbydate.com/dairy/",
            "水果": "https://eatbydate.com/fruits/",
            "蔬菜": "https://eatbydate.com/vegetables/",
            "蛋白质": "https://eatbydate.com/proteins/",
            "谷物": "https://eatbydate.com/grains/"
        }

    def _setup_driver(self, headless=True):
        """设置Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)
            return driver
        except Exception as e:
            logger.error(f"Failed to setup Chrome driver: {e}")
            raise

    def discover_food_links_from_category(self, category_url, category_name):
        """从分类页面发现所有食物链接"""
        logger.info(f"Discovering food links from category: {category_name} - {category_url}")
        
        try:
            self.driver.get(category_url)
            time.sleep(3)
            
            food_links = []
            
            # 方法1: 查找页面中的食物图标链接
            icon_links = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='/']")
            
            for link in icon_links:
                href = link.get_attribute('href')
                if href and self.is_valid_food_url(href):
                    food_links.append(href)
            
            # 方法2: 查找分类列表中的链接
            list_links = self.driver.find_elements(By.CSS_SELECTOR, "ul li a, .dairy-products a, .category-list a")
            
            for link in list_links:
                href = link.get_attribute('href')
                if href and self.is_valid_food_url(href):
                    food_links.append(href)
            
            # 去重并过滤
            unique_links = list(set(food_links))
            valid_links = [link for link in unique_links if self.is_valid_food_url(link)]
            
            logger.info(f"Found {len(valid_links)} food links in {category_name}")
            return valid_links
            
        except Exception as e:
            logger.error(f"Error discovering links from {category_url}: {str(e)}")
            return []
    
    def is_valid_food_url(self, url):
        """检查URL是否是有效的食物页面"""
        if not url or not url.startswith('http'):
            return False
            
        # 排除不相关的页面
        exclude_patterns = [
            '/blog/', '/contact/', '/about/', '/terms/', '/privacy/',
            '/faq/', '/substitutions/', '/wp-content/', '/wp-admin/',
            'javascript:', 'mailto:', '#', '/author/', '/tag/',
            '/category/', '/search/', '/feed/', '.jpg', '.png', '.gif',
            '/dairy/$', '/fruits/$', '/vegetables/$', '/proteins/$', '/grains/$'  # 排除分类首页
        ]
        
        for pattern in exclude_patterns:
            if pattern in url:
                return False
        
        # 必须包含这些模式之一才是食物页面
        include_patterns = [
            'shelf-life', 'expiration-date', 'how-long', 'last'
        ]
        
        return any(pattern in url for pattern in include_patterns)

    def extract_food_data(self, url, category):
        """从食物页面提取数据"""
        try:
            logger.info(f"Extracting data from: {url}")
            self.driver.get(url)
            time.sleep(3)
            
            # 获取页面源码
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # 提取食物名称
            name = self.extract_food_name(soup, url)
            if not name:
                logger.warning(f"Could not extract food name from {url}")
                return None
            
            # 提取保质期数据
            storage_conditions = self.extract_storage_conditions(soup)
            
            # 提取描述
            description = self.extract_description(soup)
            
            # 构建数据结构
            food_data = {
                "name": name,
                "category": self.get_category_english(category),
                "category_zh": category,
                "storage_conditions": storage_conditions,
                "description": description,
                "source": {
                    "name": "EatByDate.com",
                    "url": url,
                    "scraped_at": datetime.now().isoformat(),
                    "confidence": "medium"
                }
            }
            
            return food_data
            
        except Exception as e:
            logger.error(f"Error extracting data from {url}: {str(e)}")
            return None

    def extract_food_name(self, soup, url):
        """提取食物名称"""
        # 方法1: 从标题提取
        title_tag = soup.find('title')
        if title_tag:
            title = title_tag.get_text().strip()
            # 清理标题，提取食物名称
            if 'How Long' in title:
                # "How Long Do Apples Last" -> "Apples"
                parts = title.split()
                for i, part in enumerate(parts):
                    if part.lower() in ['do', 'does']:
                        if i + 1 < len(parts):
                            name_part = parts[i + 1]
                            return name_part.replace('?', '').strip()
            elif 'Shelf Life' in title:
                # "Apples - Shelf Life" -> "Apples"
                name = title.split('-')[0].strip()
                if name:
                    return name
        
        # 方法2: 从URL提取
        path = urlparse(url).path
        if path:
            # 从URL路径提取可能的食物名称
            parts = path.strip('/').split('/')
            for part in parts:
                if any(keyword in part for keyword in ['shelf-life', 'expiration', 'how-long']):
                    # 提取食物名称部分
                    name_parts = part.replace('-shelf-life', '').replace('-expiration-date', '').replace('how-long-do-', '').replace('how-long-does-', '').replace('-last', '')
                    name = name_parts.replace('-', ' ').title()
                    if name and len(name) > 2:
                        return name
        
        # 方法3: 从h1标签提取
        h1_tag = soup.find('h1')
        if h1_tag:
            h1_text = h1_tag.get_text().strip()
            if h1_text and len(h1_text) < 100:  # 避免提取过长的文本
                return h1_text
        
        return None

    def extract_storage_conditions(self, soup):
        """提取存储条件数据"""
        storage_conditions = {
            "room_temperature": {"duration": None, "raw": ""},
            "refrigerated": {"duration": None, "raw": ""},
            "frozen": {"duration": None, "raw": ""}
        }
        
        try:
            # 查找包含保质期信息的表格
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        condition = cells[0].get_text().strip().lower()
                        duration = cells[1].get_text().strip()
                        
                        if any(keyword in condition for keyword in ['pantry', 'counter', 'room']):
                            storage_conditions["room_temperature"]["duration"] = self.normalize_duration(duration)
                            storage_conditions["room_temperature"]["raw"] = duration
                        elif any(keyword in condition for keyword in ['refrigerat', 'fridge']):
                            storage_conditions["refrigerated"]["duration"] = self.normalize_duration(duration)
                            storage_conditions["refrigerated"]["raw"] = duration
                        elif any(keyword in condition for keyword in ['freez', 'frozen']):
                            storage_conditions["frozen"]["duration"] = self.normalize_duration(duration)
                            storage_conditions["frozen"]["raw"] = duration
            
        except Exception as e:
            logger.warning(f"Storage conditions extraction failed: {e}")
        
        return storage_conditions

    def normalize_duration(self, duration_text):
        """标准化持续时间文本"""
        if not duration_text or duration_text.strip() in ['--', '-', 'N/A', '']:
            return None
        
        duration = duration_text.strip()
        
        # 标准化常见格式
        duration = duration.replace('Months', 'months').replace('Month', 'month')
        duration = duration.replace('Weeks', 'weeks').replace('Week', 'week')
        duration = duration.replace('Days', 'days').replace('Day', 'day')
        duration = duration.replace('Years', 'years').replace('Year', 'year')
        
        return duration

    def extract_description(self, soup):
        """提取食物描述"""
        try:
            # 查找描述段落
            description_selectors = [
                'p:contains("shelf life")',
                '.entry-content p:first-of-type',
                '.post-content p:first-of-type',
                'p'
            ]
            
            for selector in description_selectors:
                if 'contains' in selector:
                    # 查找包含特定文本的段落
                    paragraphs = soup.find_all('p')
                    for p in paragraphs:
                        text = p.get_text().lower()
                        if 'shelf life' in text and len(text) > 50:
                            return p.get_text().strip()[:500]  # 限制长度
                else:
                    element = soup.select_one(selector)
                    if element:
                        text = element.get_text().strip()
                        if len(text) > 50:  # 确保描述有意义
                            return text[:500]  # 限制长度
                            
        except Exception as e:
            logger.warning(f"Description extraction failed: {e}")
        
        return ""

    def get_category_english(self, category_zh):
        """将中文分类转换为英文"""
        category_map = {
            "奶制品": "dairy",
            "水果": "fruits", 
            "蔬菜": "vegetables",
            "蛋白质": "proteins",
            "谷物": "grains"
        }
        return category_map.get(category_zh, "other")

    def scrape_all_categories(self, max_per_category=None):
        """爬取所有分类的数据"""
        logger.info("Starting to scrape all categories...")
        
        for category, category_url in self.category_urls.items():
            logger.info(f"Scraping category: {category}")
            
            # 动态发现该分类的所有食物链接
            food_urls = self.discover_food_links_from_category(category_url, category)
            
            if not food_urls:
                logger.warning(f"No food URLs found for category: {category}")
                continue
            
            # 限制每个分类的数量（如果指定）
            if max_per_category:
                food_urls = food_urls[:max_per_category]
            
            logger.info(f"Found {len(food_urls)} URLs for {category}, will scrape all of them")
            
            for i, url in enumerate(food_urls, 1):
                try:
                    logger.info(f"Scraping {i}/{len(food_urls)}: {url}")
                    data = self.extract_food_data(url, category)
                    if data:
                        self.scraped_data.append(data)
                        logger.info(f"Successfully scraped: {data['name']}")
                    else:
                        logger.warning(f"No data extracted from: {url}")
                        
                    # 添加延迟避免被封
                    time.sleep(random.uniform(1, 3))
                    
                except Exception as e:
                    logger.error(f"Error scraping {url}: {str(e)}")
                    continue
            
            # 分类间延迟
            time.sleep(5)
        
        logger.info(f"Completed scraping. Total items: {len(self.scraped_data)}")
        return self.scraped_data

    def save_data(self, output_dir="data"):
        """保存爬取的数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON格式
        json_file = f"{output_dir}/dynamic_eatbydate_data_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
        
        # 保存CSV格式
        csv_file = f"{output_dir}/dynamic_eatbydate_data_{timestamp}.csv"
        if self.scraped_data:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=[
                    'name', 'category', 'category_zh', 
                    'room_temp_duration', 'refrigerated_duration', 'frozen_duration',
                    'description', 'source_url'
                ])
                writer.writeheader()
                
                for item in self.scraped_data:
                    writer.writerow({
                        'name': item['name'],
                        'category': item['category'],
                        'category_zh': item['category_zh'],
                        'room_temp_duration': item['storage_conditions']['room_temperature']['duration'],
                        'refrigerated_duration': item['storage_conditions']['refrigerated']['duration'],
                        'frozen_duration': item['storage_conditions']['frozen']['duration'],
                        'description': item['description'],
                        'source_url': item['source']['url']
                    })
        
        logger.info(f"Data saved to:")
        logger.info(f"  JSON: {json_file}")
        logger.info(f"  CSV: {csv_file}")
        
        return json_file, csv_file

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    scraper = None
    try:
        logger.info("🚀 Starting Dynamic EatByDate Scraper...")
        
        # 创建爬虫实例
        scraper = DynamicEatByDateScraper(headless=True)
        
        # 爬取所有分类（不限制数量，获取完整数据）
        scraped_data = scraper.scrape_all_categories(max_per_category=None)
        
        if scraped_data:
            # 保存数据
            json_file, csv_file = scraper.save_data()
            
            # 打印统计信息
            categories = {}
            for item in scraped_data:
                cat = item['category_zh']
                categories[cat] = categories.get(cat, 0) + 1
            
            print(f"\n✅ 爬取完成！总共获取了 {len(scraped_data)} 个食物项目")
            print("\n📊 分类统计:")
            for cat, count in categories.items():
                print(f"  {cat}: {count} 项")
            
            print(f"\n📁 数据已保存到:")
            print(f"  JSON: {json_file}")
            print(f"  CSV: {csv_file}")
        else:
            print("❌ 没有爬取到任何数据")
            
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        print(f"❌ 爬取失败: {e}")
    finally:
        if scraper:
            scraper.close()

if __name__ == "__main__":
    main()

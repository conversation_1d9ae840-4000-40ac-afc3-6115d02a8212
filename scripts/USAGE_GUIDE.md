# EatByDate.com 爬虫系统使用指南

## 概述

这个爬虫系统专门用于从 https://eatbydate.com 网站采集食物保质期数据，支持7个主要分类的食物信息采集，并将数据处理成适合项目使用的格式。

## 文件结构

```
scripts/
├── eatbydate_scraper.py          # 主爬虫程序
├── test_eatbydate_scraper.py     # 测试脚本
├── process_eatbydate_data.py     # 数据处理脚本
├── run_eatbydate_scraper.py      # 一键运行脚本
├── setup_scraper.py              # 环境设置脚本
├── demo_usage.py                 # 使用演示脚本
├── requirements.txt              # Python依赖
├── README_eatbydate_scraper.md   # 详细说明文档
└── USAGE_GUIDE.md               # 本使用指南
```

## 快速开始

### 1. 环境准备

```bash
# 进入scripts目录
cd scripts

# 运行环境设置脚本
python setup_scraper.py
```

这个脚本会：
- 检查Python版本（需要3.7+）
- 检查Chrome浏览器是否安装
- 安装所需的Python依赖包
- 创建必要的目录
- 测试环境配置

### 2. 运行测试

```bash
# 运行测试脚本（推荐先运行）
python run_eatbydate_scraper.py test
```

测试脚本会：
- 爬取少量数据验证功能
- 检查网站连接
- 验证数据提取逻辑

### 3. 运行完整爬虫

```bash
# 运行完整爬虫和数据处理
python run_eatbydate_scraper.py full
```

这会执行完整的流程：
- 爬取所有7个分类的食物数据
- 自动处理和清洗数据
- 生成JSON和CSV格式的输出文件
- 创建数据统计报告

## 输出文件

运行完成后，会在以下目录生成文件：

### 原始数据 (data/)
- `eatbydate_data_YYYYMMDD_HHMMSS.json` - 原始爬取数据
- `eatbydate_data_YYYYMMDD_HHMMSS.csv` - 原始数据CSV格式

### 处理后数据 (processed_data/)
- `processed_eatbydate_data_YYYYMMDD_HHMMSS.json` - 处理后的结构化数据
- `processed_eatbydate_data_YYYYMMDD_HHMMSS.csv` - 处理后数据CSV格式
- `eatbydate_data_stats_YYYYMMDD_HHMMSS.txt` - 数据统计报告

## 数据格式

### 处理后的JSON数据格式：

```json
{
  "name": "Milk",
  "category": "dairy",
  "category_zh": "奶制品",
  "storage_conditions": {
    "room_temperature": {
      "duration": null,
      "raw": ""
    },
    "refrigerated": {
      "duration": "5-7 days",
      "raw": "5-7 days past printed date"
    },
    "frozen": {
      "duration": "3 months",
      "raw": "3 months"
    }
  },
  "description": "Milk is a nutritious liquid...",
  "source": {
    "name": "EatByDate.com",
    "url": "https://eatbydate.com/dairy/milk/...",
    "scraped_at": "2024-01-15T10:30:00",
    "confidence": "medium"
  },
  "processed_at": "2024-01-15T10:35:00"
}
```

## 高级用法

### 只处理现有数据

如果你已经有爬取的数据，只想重新处理：

```bash
python run_eatbydate_scraper.py process
```

### 单独运行各个组件

```bash
# 只运行爬虫
python eatbydate_scraper.py

# 只处理特定数据文件
python process_eatbydate_data.py data/eatbydate_data_20240115_143000.json

# 查看数据使用演示
python demo_usage.py
```

### 自定义配置

你可以修改 `eatbydate_scraper.py` 中的配置：

```python
class EatByDateScraper:
    def __init__(self, headless=True):
        # 修改要爬取的分类
        self.categories = {
            "fruits": "水果",
            "dairy": "奶制品"
            # 添加或删除分类
        }
```

## 项目集成

### 1. 导入到数据库

```python
import json

# 读取处理后的数据
with open('processed_data/processed_eatbydate_data_latest.json', 'r') as f:
    eatbydate_data = json.load(f)

# 集成到你的数据库
for item in eatbydate_data:
    # 插入到数据库的逻辑
    insert_food_item(item)
```

### 2. 与现有搜索系统集成

```javascript
// 在前端搜索中使用
function searchWithEatByDate(query) {
  const results = eatbydateData.filter(item => 
    item.name.toLowerCase().includes(query.toLowerCase())
  );
  
  return results.map(item => ({
    name: item.name,
    category: item.category_zh,
    storage: item.storage_conditions,
    source: 'EatByDate.com'
  }));
}
```

## 注意事项

### 1. 合规使用
- 遵守网站的robots.txt和使用条款
- 合理控制爬取频率，避免对服务器造成压力
- 仅用于学习和个人项目

### 2. 数据质量
- 爬取的数据仅供参考，使用前请验证准确性
- 建议与权威数据源（如USDA）进行对比
- 定期更新数据以保持时效性

### 3. 技术要求
- 需要安装Chrome浏览器
- 稳定的网络连接
- 足够的磁盘空间存储数据

## 故障排除

### 常见问题

1. **Chrome相关错误**
   - 确保已安装Chrome浏览器
   - 更新Chrome到最新版本
   - 检查系统权限

2. **网络连接问题**
   - 检查网络连接
   - 尝试访问 https://eatbydate.com
   - 考虑使用代理（如果需要）

3. **数据爬取失败**
   - 网站结构可能发生变化
   - 增加延迟时间
   - 检查日志文件中的错误信息

### 获取帮助

如果遇到问题：
1. 查看日志输出中的错误信息
2. 运行测试脚本验证基本功能
3. 检查网站是否可正常访问
4. 参考详细文档 `README_eatbydate_scraper.md`

## 更新和维护

建议定期：
1. 更新Python依赖包
2. 检查网站结构变化
3. 验证数据准确性
4. 备份重要数据

---

**祝你使用愉快！如果这个爬虫系统对你的项目有帮助，请记得合理使用并遵守相关规定。**

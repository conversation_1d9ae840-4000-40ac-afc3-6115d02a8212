#!/usr/bin/env node

/**
 * StillTasty 数据导入测试脚本
 * 
 * 功能：
 * 1. 测试数据清理函数
 * 2. 验证数据格式
 * 3. 测试少量数据导入
 */

const fs = require('fs');
const path = require('path');

// 导入函数（从主脚本复制的函数）
function cleanHtmlText(text) {
  if (!text) return '';
  
  // 移除 HTML 标签
  text = text.replace(/<[^>]+>/g, '');
  
  // 移除多余的空白字符
  text = text.replace(/\s+/g, ' ');
  
  // 解码 HTML 实体
  text = text.replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"');
  
  // 去除首尾空白
  return text.trim();
}

function parseStorageDays(storageText) {
  if (!storageText) return null;
  
  const text = storageText.toLowerCase();
  
  // "indefinitely" -> 9999
  if (text.includes('indefinitely')) {
    return 9999;
  }
  
  // "1 year" -> 365
  const yearMatch = text.match(/(\d+)\s*year/);
  if (yearMatch) {
    return parseInt(yearMatch[1]) * 365;
  }
  
  // "6 months" -> 180
  const monthMatch = text.match(/(\d+)\s*month/);
  if (monthMatch) {
    return parseInt(monthMatch[1]) * 30;
  }
  
  // "2 weeks" -> 14
  const weekMatch = text.match(/(\d+)\s*week/);
  if (weekMatch) {
    return parseInt(weekMatch[1]) * 7;
  }
  
  // "5-7 days" -> 6 (取中间值)
  const rangeMatch = text.match(/(\d+)-(\d+)\s*day/);
  if (rangeMatch) {
    const min = parseInt(rangeMatch[1]);
    const max = parseInt(rangeMatch[2]);
    return Math.round((min + max) / 2);
  }
  
  // "7 days" -> 7
  const dayMatch = text.match(/(\d+)\s*day/);
  if (dayMatch) {
    return parseInt(dayMatch[1]);
  }
  
  return null;
}

function generateSearchKey(name) {
  return name.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .trim();
}

// 测试函数
function runTests() {
  console.log('=== StillTasty 数据导入测试 ===\n');
  
  // 测试 1: 清理 HTML 文本
  console.log('测试 1: 清理 HTML 文本');
  const htmlTests = [
    {
      input: '5-7 days after prepared for drinking </span>\n\t\t\t\t</div>',
      expected: '5-7 days after prepared for drinking'
    },
    {
      input: '1 year (best quality)<div class="test">test</div>',
      expected: '1 year (best quality) test'
    }
  ];
  
  htmlTests.forEach((test, i) => {
    const result = cleanHtmlText(test.input);
    console.log(`  测试 ${i + 1}: ${result === test.expected ? '✓' : '✗'}`);
    if (result !== test.expected) {
      console.log(`    期望: "${test.expected}"`);
      console.log(`    实际: "${result}"`);
    }
  });
  
  // 测试 2: 解析存储天数
  console.log('\n测试 2: 解析存储天数');
  const dayTests = [
    { input: '5-7 days', expected: 6 },
    { input: '1 year', expected: 365 },
    { input: '6 months', expected: 180 },
    { input: '2 weeks', expected: 14 },
    { input: 'indefinitely', expected: 9999 },
    { input: '30 days', expected: 30 }
  ];
  
  dayTests.forEach((test, i) => {
    const result = parseStorageDays(test.input);
    console.log(`  测试 ${i + 1}: ${result === test.expected ? '✓' : '✗'} "${test.input}" -> ${result}`);
  });
  
  // 测试 3: 生成搜索键
  console.log('\n测试 3: 生成搜索键');
  const keyTests = [
    { input: 'Apple Juice', expected: 'apple_juice' },
    { input: 'Chicken (Fresh)', expected: 'chicken_fresh' },
    { input: 'Beef - Ground', expected: 'beef_ground' }
  ];
  
  keyTests.forEach((test, i) => {
    const result = generateSearchKey(test.input);
    console.log(`  测试 ${i + 1}: ${result === test.expected ? '✓' : '✗'} "${test.input}" -> "${result}"`);
  });
  
  // 测试 4: 读取和验证数据文件
  console.log('\n测试 4: 验证数据文件');
  const dataFile = path.join(__dirname, 'processed_data/processed_stilltasty_data_20250718_132503.json');
  
  try {
    const rawData = fs.readFileSync(dataFile, 'utf8');
    const data = JSON.parse(rawData);
    
    console.log(`  ✓ 成功读取数据文件`);
    console.log(`  记录总数: ${data.length}`);
    
    // 验证前 5 条记录的结构
    console.log('\n  验证前 5 条记录:');
    for (let i = 0; i < Math.min(5, data.length); i++) {
      const item = data[i];
      const isValid = 
        item.id && 
        item.name && 
        item.storage && 
        (item.storage.room_temperature || item.storage.refrigerated || item.storage.frozen);
      
      console.log(`    记录 ${i + 1}: ${isValid ? '✓' : '✗'} ${item.name_clean || item.name}`);
      
      if (!isValid) {
        console.log(`      缺少必要字段:`, {
          hasId: !!item.id,
          hasName: !!item.name,
          hasStorage: !!item.storage
        });
      }
    }
    
    // 显示示例数据转换
    console.log('\n  示例数据转换:');
    const sampleItem = data[0];
    console.log(`    原始名称: "${sampleItem.name}"`);
    console.log(`    清理后名称: "${sampleItem.name_clean}"`);
    console.log(`    搜索键: "${generateSearchKey(sampleItem.name_clean || sampleItem.name)}"`);
    
    if (sampleItem.storage.refrigerated) {
      const days = parseStorageDays(sampleItem.storage.refrigerated.text);
      console.log(`    冷藏天数: ${days} (原始: "${cleanHtmlText(sampleItem.storage.refrigerated.text).substring(0, 50)}...")`);
    }
    
  } catch (error) {
    console.log(`  ✗ 读取数据文件失败: ${error.message}`);
  }
  
  console.log('\n测试完成！');
}

// 运行测试
runTests();
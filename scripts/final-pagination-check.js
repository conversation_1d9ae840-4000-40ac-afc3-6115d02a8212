#!/usr/bin/env node

/**
 * 最终检查 - 验证分页组件应该正常工作
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkCategory(slug, categoryIds) {
  const pageSize = 24;
  
  // 获取第一页数据
  const { data: firstPageData, error: firstPageError } = await supabase
    .from('foods')
    .select('*')
    .in('category_id', categoryIds)
    .order('name', { ascending: true })
    .range(0, pageSize - 1);
    
  // 获取总数
  const { count } = await supabase
    .from('foods')
    .select('*', { count: 'exact', head: true })
    .in('category_id', categoryIds);
    
  const totalPages = Math.ceil(count / pageSize);
  const hasMore = pageSize < count;
  
  console.log(`\n${slug}:`);
  console.log(`- 第一页数据: ${firstPageData?.length || 0} 条`);
  console.log(`- 总数: ${count}`);
  console.log(`- 总页数: ${totalPages}`);
  console.log(`- hasMore: ${hasMore}`);
  console.log(`- 分页组件应该${totalPages > 1 ? '显示' : '隐藏'}`);
  
  if (totalPages > 1) {
    // 获取最后一页数据验证
    const lastPageOffset = (totalPages - 1) * pageSize;
    const { data: lastPageData } = await supabase
      .from('foods')
      .select('*')
      .in('category_id', categoryIds)
      .order('name', { ascending: true })
      .range(lastPageOffset, lastPageOffset + pageSize - 1);
      
    console.log(`- 最后一页(第${totalPages}页)数据: ${lastPageData?.length || 0} 条`);
  }
}

async function main() {
  console.log('=== 最终分页验证 ===');
  console.log('页面大小: 24');
  
  // 检查几个关键分类
  await checkCategory('meat', [3, 18]);
  await checkCategory('dairy', [1, 16]);
  await checkCategory('fruits', [5, 20]);
  await checkCategory('vegetables', [4, 19]);
  await checkCategory('grains', [6, 7, 21, 22]);
  
  console.log('\n结论:');
  console.log('- Pagination组件的逻辑是正确的 (totalPages > 1 时显示)');
  console.log('- 数据查询返回的结果是正确的');
  console.log('- 如果分页仍然不显示，请检查:');
  console.log('  1. 浏览器是否有缓存');
  console.log('  2. 是否有CSS样式隐藏了分页组件');
  console.log('  3. 是否有JavaScript错误');
}

main().catch(console.error);
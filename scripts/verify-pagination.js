#!/usr/bin/env node

/**
 * 验证所有分类的分页是否正常工作
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

// UI分类到数据库分类的映射
const categoryMappings = {
  'fruits': [5, 20],
  'vegetables': [4, 19], 
  'meat': [3, 18],
  'seafood': [2, 17],
  'dairy': [1, 16],
  'grains': [6, 7, 21, 22],
  'beverages': [8, 23],
  'snacks': [11, 12, 26, 27],
  'condiments': [9, 24],
  'spices': [10, 25]
};

async function verifyCategory(categorySlug, categoryIds) {
  const { count } = await supabase
    .from('foods')
    .select('*', { count: 'exact', head: true })
    .in('category_id', categoryIds);
    
  const pageSize = 24;
  const totalPages = Math.ceil(count / pageSize);
  const shouldShowPagination = totalPages > 1;
  
  return {
    slug: categorySlug,
    total: count,
    totalPages: totalPages,
    shouldShowPagination: shouldShowPagination,
    status: shouldShowPagination ? '应该显示分页' : '无需分页'
  };
}

async function main() {
  console.log('=== 验证所有分类的分页情况 ===\n');
  
  const results = [];
  
  for (const [slug, categoryIds] of Object.entries(categoryMappings)) {
    const result = await verifyCategory(slug, categoryIds);
    results.push(result);
  }
  
  // 打印结果表格
  console.log('分类\t\t总数\t总页数\t状态');
  console.log('--------------------------------------------');
  
  results.forEach(r => {
    console.log(`${r.slug.padEnd(12)}\t${r.total}\t${r.totalPages}\t${r.status}`);
  });
  
  // 找出需要分页的分类
  const needsPagination = results.filter(r => r.shouldShowPagination);
  console.log('\n需要显示分页的分类:');
  needsPagination.forEach(r => {
    console.log(`- ${r.slug}: ${r.total} 项, ${r.totalPages} 页`);
  });
}

main().catch(console.error);
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

// 分类配置：预期的数据量和页数
const CATEGORIES = {
  'fruits': { expectedFoods: 235, expectedPages: 10, name: '水果' },
  'grains': { expectedFoods: 219, expectedPages: 10, name: '谷物' },
  'meat': { expectedFoods: 212, expectedPages: 9, name: '肉类' },
  'vegetables': { expectedFoods: 172, expectedPages: 8, name: '蔬菜' },
  'seafood': { expectedFoods: 129, expectedPages: 6, name: '海鲜' },
  'condiments': { expectedFoods: 107, expectedPages: 5, name: '调料' },
  'snacks': { expectedFoods: 106, expectedPages: 5, name: '零食' },
  'beverages': { expectedFoods: 99, expectedPages: 5, name: '饮料' },
  'spices': { expectedFoods: 62, expectedPages: 3, name: '香料' },
  'dairy': { expectedFoods: 13, expectedPages: 1, name: '乳制品' }
};

const BASE_URL = 'http://localhost:3001';
const PAGE_SIZE = 24;

async function verifyCategory(categorySlug, categoryInfo) {
  console.log(`\n=== 验证 ${categoryInfo.name} (${categorySlug}) ===`);

  try {
    // 访问分类页面
    const url = `${BASE_URL}/zh/category/${categorySlug}`;
    console.log(`访问: ${url}`);

    const { stdout } = await execAsync(`curl -s "${url}"`);

    // 解析HTML获取食物总数
    const totalFoodsMatch = stdout.match(/共 (\d+) 种食物/);
    const actualTotalFoods = totalFoodsMatch ? parseInt(totalFoodsMatch[1]) : 0;

    // 计算食物卡片数量（通过查找食物卡片的特征）
    const foodCardMatches = stdout.match(/查看详情/g) || [];
    const actualFoodCardsOnPage = foodCardMatches.length;

    // 检查是否有分页导航（查找下一页链接）
    const nextPageExists = stdout.includes('下一页') && stdout.includes('page=2');
    const paginationExists = nextPageExists || stdout.includes('页码导航');

    // 计算总页数
    const actualTotalPages = Math.ceil(actualTotalFoods / PAGE_SIZE);

    // 验证结果
    const results = {
      category: categoryInfo.name,
      slug: categorySlug,
      expectedFoods: categoryInfo.expectedFoods,
      actualTotalFoods: actualTotalFoods,
      expectedPages: categoryInfo.expectedPages,
      actualTotalPages: actualTotalPages,
      actualFoodCardsOnPage: actualFoodCardsOnPage,
      paginationExists: paginationExists,
      nextPageExists: nextPageExists,

      // 验证状态
      foodCountCorrect: actualTotalFoods === categoryInfo.expectedFoods,
      pageCountCorrect: actualTotalPages === categoryInfo.expectedPages,
      paginationCorrect: categoryInfo.expectedPages > 1 ? nextPageExists : !nextPageExists,
      firstPageCardCount: actualFoodCardsOnPage === Math.min(PAGE_SIZE, actualTotalFoods)
    };

    // 输出结果
    console.log(`  食物总数: ${actualTotalFoods}/${categoryInfo.expectedFoods} ${results.foodCountCorrect ? '✅' : '❌'}`);
    console.log(`  预期页数: ${actualTotalPages}/${categoryInfo.expectedPages} ${results.pageCountCorrect ? '✅' : '❌'}`);
    console.log(`  当前页食物数: ${actualFoodCardsOnPage} ${results.firstPageCardCount ? '✅' : '❌'}`);
    console.log(`  分页导航: ${paginationExists ? '存在' : '不存在'} ${results.paginationCorrect ? '✅' : '❌'}`);

    if (categoryInfo.expectedPages > 1) {
      console.log(`  下一页按钮: ${nextPageExists ? '存在' : '不存在'} ${nextPageExists ? '✅' : '❌'}`);
    }

    // 如果有多页，测试第2页
    if (categoryInfo.expectedPages > 1 && nextPageExists) {
      console.log(`  测试第2页...`);

      const page2Url = `${BASE_URL}/zh/category/${categorySlug}?page=2`;
      const { stdout: page2Html } = await execAsync(`curl -s "${page2Url}"`);

      const page2FoodCardMatches = page2Html.match(/查看详情/g) || [];
      const page2CardCount = page2FoodCardMatches.length;

      const expectedPage2Cards = Math.min(PAGE_SIZE, actualTotalFoods - PAGE_SIZE);
      const page2Correct = page2CardCount === expectedPage2Cards;

      console.log(`  第2页食物数: ${page2CardCount}/${expectedPage2Cards} ${page2Correct ? '✅' : '❌'}`);

      results.page2CardCount = page2CardCount;
      results.page2Correct = page2Correct;
    }

    return results;

  } catch (error) {
    console.error(`  ❌ 验证失败: ${error.message}`);
    return {
      category: categoryInfo.name,
      slug: categorySlug,
      error: error.message,
      success: false
    };
  }
}

async function main() {
  console.log('🚀 开始自动化验证分类分页功能...\n');

  const results = [];

  try {
    for (const [categorySlug, categoryInfo] of Object.entries(CATEGORIES)) {
      const result = await verifyCategory(categorySlug, categoryInfo);
      results.push(result);

      // 短暂延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 生成总结报告
    console.log('\n' + '='.repeat(60));
    console.log('📊 验证结果总结');
    console.log('='.repeat(60));
    
    let totalCategories = results.length;
    let successfulCategories = 0;
    let issuesFound = [];
    
    results.forEach(result => {
      if (result.error) {
        console.log(`❌ ${result.category}: 验证失败 - ${result.error}`);
        issuesFound.push(`${result.category}: 验证失败`);
        return;
      }
      
      const allCorrect = result.foodCountCorrect && 
                        result.pageCountCorrect && 
                        result.paginationCorrect && 
                        result.firstPageCardCount &&
                        (result.page2Correct !== false);
      
      if (allCorrect) {
        console.log(`✅ ${result.category}: 完全正常`);
        successfulCategories++;
      } else {
        console.log(`⚠️  ${result.category}: 发现问题`);
        
        if (!result.foodCountCorrect) {
          issuesFound.push(`${result.category}: 食物数量不匹配 (${result.actualTotalFoods}/${result.expectedFoods})`);
        }
        if (!result.pageCountCorrect) {
          issuesFound.push(`${result.category}: 页数不匹配 (${result.actualTotalPages}/${result.expectedPages})`);
        }
        if (!result.paginationCorrect) {
          issuesFound.push(`${result.category}: 分页导航状态不正确`);
        }
        if (!result.firstPageCardCount) {
          issuesFound.push(`${result.category}: 第1页食物卡片数量不正确 (${result.actualFoodCardsOnPage})`);
        }
        if (result.page2Correct === false) {
          issuesFound.push(`${result.category}: 第2页食物卡片数量不正确 (${result.page2CardCount})`);
        }
      }
    });
    
    console.log('\n' + '-'.repeat(40));
    console.log(`总分类数: ${totalCategories}`);
    console.log(`正常分类: ${successfulCategories}`);
    console.log(`问题分类: ${totalCategories - successfulCategories}`);
    
    if (issuesFound.length > 0) {
      console.log('\n🔍 发现的问题:');
      issuesFound.forEach(issue => console.log(`  • ${issue}`));
    } else {
      console.log('\n🎉 所有分类都正常工作！');
    }
    
  } catch (error) {
    console.error('验证过程中出错:', error);
  }
}

// 运行验证
main();

#!/usr/bin/env node

/**
 * StillTasty 处理后数据导入脚本
 * 
 * 功能：
 * 1. 读取 processed_stilltasty_data_20250718_132503.json
 * 2. 清理和标准化数据
 * 3. 批量导入到 Supabase 数据库
 * 4. 处理重复数据和错误
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

// Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('错误：缺少 Supabase 配置。请确保设置了环境变量。');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// 配置
const DATA_FILE = path.join(__dirname, 'processed_data/processed_stilltasty_data_20250718_132503.json');
const BATCH_SIZE = 100; // 每批次处理的记录数
const LOG_INTERVAL = 500; // 每处理多少条记录输出一次日志

// 统计信息
let stats = {
  total: 0,
  processed: 0,
  imported: 0,
  skipped: 0,
  errors: 0,
  duplicates: 0
};

/**
 * 清理 HTML 文本
 */
function cleanHtmlText(text) {
  if (!text) return '';
  
  // 移除 HTML 标签
  text = text.replace(/<[^>]+>/g, '');
  
  // 移除多余的空白字符
  text = text.replace(/\s+/g, ' ');
  
  // 解码 HTML 实体
  text = text.replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"');
  
  // 去除首尾空白
  return text.trim();
}

/**
 * 解析存储天数
 */
function parseStorageDays(storageText) {
  if (!storageText) return null;

  const text = storageText.toLowerCase();

  // "indefinitely" -> 9999
  if (text.includes('indefinitely')) {
    return 9999;
  }

  // "2-3 years" -> 912 (取中间值: 2.5 * 365)
  const yearRangeMatch = text.match(/(\d+)-(\d+)\s*year/);
  if (yearRangeMatch) {
    const min = parseInt(yearRangeMatch[1]);
    const max = parseInt(yearRangeMatch[2]);
    return Math.round((min + max) / 2 * 365);
  }

  // "1 year" -> 365
  const yearMatch = text.match(/(\d+)\s*year/);
  if (yearMatch) {
    return parseInt(yearMatch[1]) * 365;
  }

  // "2-3 months" -> 75 (取中间值: 2.5 * 30)
  const monthRangeMatch = text.match(/(\d+)-(\d+)\s*month/);
  if (monthRangeMatch) {
    const min = parseInt(monthRangeMatch[1]);
    const max = parseInt(monthRangeMatch[2]);
    return Math.round((min + max) / 2 * 30);
  }

  // "6 months" -> 180
  const monthMatch = text.match(/(\d+)\s*month/);
  if (monthMatch) {
    return parseInt(monthMatch[1]) * 30;
  }

  // "2-3 weeks" -> 17.5 (取中间值: 2.5 * 7)
  const weekRangeMatch = text.match(/(\d+)-(\d+)\s*week/);
  if (weekRangeMatch) {
    const min = parseInt(weekRangeMatch[1]);
    const max = parseInt(weekRangeMatch[2]);
    return Math.round((min + max) / 2 * 7);
  }

  // "2 weeks" -> 14
  const weekMatch = text.match(/(\d+)\s*week/);
  if (weekMatch) {
    return parseInt(weekMatch[1]) * 7;
  }

  // "5-7 days" -> 6 (取中间值)
  const dayRangeMatch = text.match(/(\d+)-(\d+)\s*day/);
  if (dayRangeMatch) {
    const min = parseInt(dayRangeMatch[1]);
    const max = parseInt(dayRangeMatch[2]);
    return Math.round((min + max) / 2);
  }

  // "7 days" -> 7
  const dayMatch = text.match(/(\d+)\s*day/);
  if (dayMatch) {
    return parseInt(dayMatch[1]);
  }

  return null;
}

/**
 * 生成搜索键
 */
function generateSearchKey(name) {
  return name.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .trim();
}

/**
 * 处理单个食物记录
 */
async function processFoodItem(item) {
  try {
    // 准备基础数据
    const searchKey = generateSearchKey(item.name_clean || item.name);
    
    // 检查是否已存在
    const { data: existing } = await supabase
      .from('foods')
      .select('id, source')
      .or(`search_key.eq.${searchKey},external_id.eq.${item.id}`)
      .single();
    
    if (existing) {
      // 如果已存在且是 USDA 数据，跳过
      if (existing.source === 'USDA') {
        stats.duplicates++;
        return { success: false, reason: 'duplicate_usda' };
      }
      // 如果是其他来源，可以考虑更新
      stats.duplicates++;
      return { success: false, reason: 'duplicate_other' };
    }
    
    // 获取分类 ID
    const { data: categoryMapping } = await supabase
      .from('stilltasty_category_mapping')
      .select('food_category_id')
      .eq('stilltasty_category', item.category_id)
      .single();
    
    const categoryId = categoryMapping?.food_category_id || null;
    
    // 准备存储提示
    const storageTips = [];
    if (item.tips && Array.isArray(item.tips)) {
      item.tips.forEach(tip => {
        const cleanTip = cleanHtmlText(tip);
        if (cleanTip) {
          storageTips.push(cleanTip);
        }
      });
    }
    
    // 准备存储文本（保留原始信息）
    const storageText = {
      room_temperature: item.storage.room_temperature?.text ? cleanHtmlText(item.storage.room_temperature.text) : null,
      refrigerated: item.storage.refrigerated?.text ? cleanHtmlText(item.storage.refrigerated.text) : null,
      frozen: item.storage.frozen?.text ? cleanHtmlText(item.storage.frozen.text) : null
    };
    
    // 准备名称变体
    const nameVariations = [];
    if (item.name !== item.name_clean) {
      nameVariations.push(item.name);
    }
    
    // 插入主记录
    const { data: newFood, error: insertError } = await supabase
      .from('foods')
      .insert({
        name: item.name_clean || item.name,
        search_key: searchKey,
        category_id: categoryId,
        refrigerated_days: parseStorageDays(item.storage.refrigerated?.text) || item.storage.refrigerated?.days,
        frozen_days: parseStorageDays(item.storage.frozen?.text) || item.storage.frozen?.days,
        room_temperature_days: parseStorageDays(item.storage.room_temperature?.text) || item.storage.room_temperature?.days,
        storage_tips: storageTips,
        source: 'STILLTASTY',
        confidence: 0.95, // StillTasty 数据置信度设为 0.95
        external_id: item.id,
        source_url: item.source?.url || null,
        name_variations: nameVariations.length > 0 ? nameVariations : null,
        storage_text: storageText
      })
      .select('id')
      .single();
    
    if (insertError) {
      throw insertError;
    }
    
    // 插入关键词
    if (item.keywords && Array.isArray(item.keywords) && item.keywords.length > 0) {
      const keywordInserts = item.keywords.map(keyword => ({
        food_id: newFood.id,
        keyword: keyword.toLowerCase(),
        weight: 1.0
      }));
      
      await supabase
        .from('food_keywords')
        .insert(keywordInserts);
    }
    
    // 插入别名（如果有中文名称）
    if (item.category_name_zh) {
      await supabase
        .from('food_aliases')
        .insert({
          food_id: newFood.id,
          alias: item.name_clean || item.name,
          language: 'zh',
          alias_type: 'name'
        });
    }
    
    stats.imported++;
    return { success: true, id: newFood.id };
    
  } catch (error) {
    stats.errors++;
    console.error(`处理食物 "${item.name}" 时出错:`, error.message);
    return { success: false, error: error.message };
  }
}

/**
 * 批量处理食物数据
 */
async function processBatch(batch) {
  const results = await Promise.all(batch.map(item => processFoodItem(item)));
  return results;
}

/**
 * 主函数
 */
async function main() {
  console.log('=== StillTasty 数据导入脚本 ===\n');
  
  // 检查数据文件
  if (!fs.existsSync(DATA_FILE)) {
    console.error(`错误：找不到数据文件 ${DATA_FILE}`);
    process.exit(1);
  }
  
  // 创建导入日志
  const { data: importLog, error: logError } = await supabase
    .from('data_import_logs')
    .insert({
      source: 'STILLTASTY',
      file_name: path.basename(DATA_FILE),
      status: 'running'
    })
    .select('id')
    .single();
  
  if (logError) {
    console.error('创建导入日志失败:', logError);
    process.exit(1);
  }
  
  const logId = importLog.id;
  
  try {
    // 读取数据
    console.log('读取数据文件...');
    const rawData = fs.readFileSync(DATA_FILE, 'utf8');
    const data = JSON.parse(rawData);
    
    stats.total = data.length;
    console.log(`共有 ${stats.total} 条记录待处理\n`);
    
    // 分批处理
    console.log('开始导入数据...');
    const startTime = Date.now();
    
    for (let i = 0; i < data.length; i += BATCH_SIZE) {
      const batch = data.slice(i, i + BATCH_SIZE);
      await processBatch(batch);
      
      stats.processed += batch.length;
      
      // 定期输出进度
      if (stats.processed % LOG_INTERVAL === 0 || stats.processed === stats.total) {
        const progress = ((stats.processed / stats.total) * 100).toFixed(1);
        const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
        console.log(`进度: ${progress}% (${stats.processed}/${stats.total}) - 已用时: ${elapsed}秒`);
      }
    }
    
    // 更新导入日志
    await supabase
      .from('data_import_logs')
      .update({
        records_processed: stats.processed,
        records_imported: stats.imported,
        records_skipped: stats.skipped + stats.duplicates,
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('id', logId);
    
    // 输出最终统计
    console.log('\n=== 导入完成 ===');
    console.log(`总记录数: ${stats.total}`);
    console.log(`已处理: ${stats.processed}`);
    console.log(`成功导入: ${stats.imported}`);
    console.log(`跳过（重复）: ${stats.duplicates}`);
    console.log(`跳过（其他）: ${stats.skipped}`);
    console.log(`错误: ${stats.errors}`);
    console.log(`总用时: ${((Date.now() - startTime) / 1000).toFixed(1)}秒`);
    
  } catch (error) {
    console.error('\n导入过程中发生错误:', error);
    
    // 更新导入日志状态为失败
    await supabase
      .from('data_import_logs')
      .update({
        status: 'failed',
        errors: [error.message],
        completed_at: new Date().toISOString()
      })
      .eq('id', logId);
    
    process.exit(1);
  }
}

// 运行主函数
main().catch(console.error);
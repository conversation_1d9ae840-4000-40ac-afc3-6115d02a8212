#!/usr/bin/env python3
"""
运行StillTasty.com完整爬虫的脚本
"""

import asyncio
import logging
from datetime import datetime
from stilltasty_scraper import StillTastyScraper

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stilltasty_full_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def main():
    """主函数"""
    try:
        start_time = datetime.now()
        logger.info("🍎 开始StillTasty.com完整爬取...")
        
        # 创建爬虫实例
        scraper = StillTastyScraper()
        
        # 爬取所有类别的食品
        scraped_data = await scraper.scrape_all_categories()
        
        if scraped_data:
            # 保存数据
            json_file, csv_file = scraper.save_data()
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            print(f"\n✅ StillTasty完整爬取完成！")
            print(f"⏱️ 总耗时: {duration}")
            print(f"📊 总共获取了 {len(scraped_data)} 个食品项目")
            
            # 统计各类别的食品数量
            category_stats = {}
            for item in scraped_data:
                category = item['category']
                if category not in category_stats:
                    category_stats[category] = 0
                category_stats[category] += 1
            
            print(f"\n📊 各类别食品数量统计:")
            total_items = 0
            for category, count in sorted(category_stats.items()):
                print(f"  - {category}: {count} 个食品")
                total_items += count
            
            print(f"\n📁 数据已保存到:")
            print(f"  JSON: {json_file}")
            print(f"  CSV: {csv_file}")
            
            # 数据质量统计
            items_with_refrigerated = len([item for item in scraped_data if item['storage_conditions']['refrigerated']['duration']])
            items_with_frozen = len([item for item in scraped_data if item['storage_conditions']['frozen']['duration']])
            items_with_tips = len([item for item in scraped_data if item['shelf_life_tips']])
            
            print(f"\n📈 数据质量统计:")
            print(f"  - 有冰箱存储信息: {items_with_refrigerated}/{total_items} ({items_with_refrigerated/total_items*100:.1f}%)")
            print(f"  - 有冷冻存储信息: {items_with_frozen}/{total_items} ({items_with_frozen/total_items*100:.1f}%)")
            print(f"  - 有保质期提示: {items_with_tips}/{total_items} ({items_with_tips/total_items*100:.1f}%)")
            
        else:
            print("❌ 没有爬取到任何食品数据")
            
    except Exception as e:
        logger.error(f"爬取失败: {e}")
        print(f"❌ 爬取失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())

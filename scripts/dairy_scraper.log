2025-07-17 06:31:33,267 - INFO - 🥛 Starting Dairy Products Scraper...
2025-07-17 06:31:33,921 - INFO - Starting to scrape all dairy products...
2025-07-17 06:31:33,921 - INFO - Found 24 unique dairy product URLs
2025-07-17 06:31:33,921 - INFO - Scraping 1/24: https://eatbydate.com/eggs-shelf-life-expiration-date/
2025-07-17 06:31:33,921 - INFO - Extracting data from: https://eatbydate.com/eggs-shelf-life-expiration-date/
2025-07-17 06:31:49,809 - INFO - Successfully scraped: Eggs
2025-07-17 06:31:51,909 - INFO - Scraping 2/24: https://eatbydate.com/dairy/milk/soy-rice-almond-milk-substitutes-shelf-life-expiration-date/
2025-07-17 06:31:51,909 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/soy-rice-almond-milk-substitutes-shelf-life-expiration-date/
2025-07-17 06:32:21,927 - ERROR - Error extracting data from https://eatbydate.com/dairy/milk/soy-rice-almond-milk-substitutes-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 28.276
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:32:21,930 - WARNING - No data extracted from: https://eatbydate.com/dairy/milk/soy-rice-almond-milk-substitutes-shelf-life-expiration-date/
2025-07-17 06:32:24,487 - INFO - Scraping 3/24: https://eatbydate.com/dairy/milk/how-long-does-condensed-milk-last-shelf-life/
2025-07-17 06:32:24,488 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/how-long-does-condensed-milk-last-shelf-life/
2025-07-17 06:32:54,498 - ERROR - Error extracting data from https://eatbydate.com/dairy/milk/how-long-does-condensed-milk-last-shelf-life/: Message: timeout: Timed out receiving message from renderer: 28.386
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:32:54,500 - WARNING - No data extracted from: https://eatbydate.com/dairy/milk/how-long-does-condensed-milk-last-shelf-life/
2025-07-17 06:32:57,336 - INFO - Scraping 4/24: https://eatbydate.com/dairy/milk/buttermilk/
2025-07-17 06:32:57,337 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/buttermilk/
2025-07-17 06:33:25,960 - INFO - Successfully scraped: Buttermilk
2025-07-17 06:33:28,800 - INFO - Scraping 5/24: https://eatbydate.com/dairy/milk/how-long-does-infant-formula-last-shelf-life-expiration-date/
2025-07-17 06:33:28,800 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/how-long-does-infant-formula-last-shelf-life-expiration-date/
2025-07-17 06:33:58,811 - ERROR - Error extracting data from https://eatbydate.com/dairy/milk/how-long-does-infant-formula-last-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 28.072
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:33:58,812 - WARNING - No data extracted from: https://eatbydate.com/dairy/milk/how-long-does-infant-formula-last-shelf-life-expiration-date/
2025-07-17 06:34:02,181 - INFO - Scraping 6/24: https://eatbydate.com/dairy/spreads/how-long-does-margarine-last-shelf-life-expiration-date/
2025-07-17 06:34:02,181 - INFO - Extracting data from: https://eatbydate.com/dairy/spreads/how-long-does-margarine-last-shelf-life-expiration-date/
2025-07-17 06:34:32,187 - ERROR - Error extracting data from https://eatbydate.com/dairy/spreads/how-long-does-margarine-last-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 28.195
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:34:32,191 - WARNING - No data extracted from: https://eatbydate.com/dairy/spreads/how-long-does-margarine-last-shelf-life-expiration-date/
2025-07-17 06:34:35,582 - INFO - Scraping 7/24: https://eatbydate.com/dairy/spreads/butter-shelf-life-expiration-date/
2025-07-17 06:34:35,582 - INFO - Extracting data from: https://eatbydate.com/dairy/spreads/butter-shelf-life-expiration-date/
2025-07-17 06:35:05,600 - ERROR - Error extracting data from https://eatbydate.com/dairy/spreads/butter-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 27.737
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:35:05,602 - WARNING - No data extracted from: https://eatbydate.com/dairy/spreads/butter-shelf-life-expiration-date/
2025-07-17 06:35:07,653 - INFO - Scraping 8/24: https://eatbydate.com/dairy/milk/milk-shelf-life-expiration-date/
2025-07-17 06:35:07,654 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/milk-shelf-life-expiration-date/
2025-07-17 06:35:37,664 - ERROR - Error extracting data from https://eatbydate.com/dairy/milk/milk-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 29.015
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:35:37,665 - WARNING - No data extracted from: https://eatbydate.com/dairy/milk/milk-shelf-life-expiration-date/
2025-07-17 06:35:39,801 - INFO - Scraping 9/24: https://eatbydate.com/dairy/cheese/cottage-cheese-shelf-life-expiration-date/
2025-07-17 06:35:39,801 - INFO - Extracting data from: https://eatbydate.com/dairy/cheese/cottage-cheese-shelf-life-expiration-date/
2025-07-17 06:36:09,811 - ERROR - Error extracting data from https://eatbydate.com/dairy/cheese/cottage-cheese-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 29.388
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:36:09,814 - WARNING - No data extracted from: https://eatbydate.com/dairy/cheese/cottage-cheese-shelf-life-expiration-date/
2025-07-17 06:36:12,740 - INFO - Scraping 10/24: https://eatbydate.com/dairy/milk/how-long-does-powdered-milk-last-shelf-life/
2025-07-17 06:36:12,740 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/how-long-does-powdered-milk-last-shelf-life/
2025-07-17 06:36:42,770 - ERROR - Error extracting data from https://eatbydate.com/dairy/milk/how-long-does-powdered-milk-last-shelf-life/: Message: timeout: Timed out receiving message from renderer: 28.086
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:36:42,773 - WARNING - No data extracted from: https://eatbydate.com/dairy/milk/how-long-does-powdered-milk-last-shelf-life/
2025-07-17 06:36:46,247 - INFO - Scraping 11/24: https://eatbydate.com/dairy/milk/how-long-does-coffee-mate-last-shelf-life/
2025-07-17 06:36:46,248 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/how-long-does-coffee-mate-last-shelf-life/
2025-07-17 06:37:16,265 - ERROR - Error extracting data from https://eatbydate.com/dairy/milk/how-long-does-coffee-mate-last-shelf-life/: Message: timeout: Timed out receiving message from renderer: 28.286
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:37:16,268 - WARNING - No data extracted from: https://eatbydate.com/dairy/milk/how-long-does-coffee-mate-last-shelf-life/
2025-07-17 06:37:18,363 - INFO - Scraping 12/24: https://eatbydate.com/dairy/cheese/brie-feta-mozzarella-soft-cheese-shelf-life-expiration-date/
2025-07-17 06:37:18,364 - INFO - Extracting data from: https://eatbydate.com/dairy/cheese/brie-feta-mozzarella-soft-cheese-shelf-life-expiration-date/
2025-07-17 06:37:33,929 - INFO - Successfully scraped: Soft
2025-07-17 06:37:37,513 - INFO - Scraping 13/24: https://eatbydate.com/dairy/milk/how-long-does-egg-nog-last-shelf-life-expiration-date/
2025-07-17 06:37:37,513 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/how-long-does-egg-nog-last-shelf-life-expiration-date/
2025-07-17 06:37:53,173 - INFO - Successfully scraped: Eggnog
2025-07-17 06:37:55,666 - INFO - Scraping 14/24: https://eatbydate.com/dairy/ice-cream-shelf-life-expiration-date/
2025-07-17 06:37:55,666 - INFO - Extracting data from: https://eatbydate.com/dairy/ice-cream-shelf-life-expiration-date/
2025-07-17 06:38:25,680 - ERROR - Error extracting data from https://eatbydate.com/dairy/ice-cream-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 28.663
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:38:25,683 - WARNING - No data extracted from: https://eatbydate.com/dairy/ice-cream-shelf-life-expiration-date/
2025-07-17 06:38:28,890 - INFO - Scraping 15/24: https://eatbydate.com/dairy/milk/kefir-shelf-life-expiration-date/
2025-07-17 06:38:28,890 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/kefir-shelf-life-expiration-date/
2025-07-17 06:38:58,909 - ERROR - Error extracting data from https://eatbydate.com/dairy/milk/kefir-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 29.375
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:38:58,911 - WARNING - No data extracted from: https://eatbydate.com/dairy/milk/kefir-shelf-life-expiration-date/
2025-07-17 06:39:01,196 - INFO - Scraping 16/24: https://eatbydate.com/dairy/yogurt-shelf-life-expiration-date/
2025-07-17 06:39:01,197 - INFO - Extracting data from: https://eatbydate.com/dairy/yogurt-shelf-life-expiration-date/
2025-07-17 06:39:31,210 - ERROR - Error extracting data from https://eatbydate.com/dairy/yogurt-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 29.723
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:39:31,212 - WARNING - No data extracted from: https://eatbydate.com/dairy/yogurt-shelf-life-expiration-date/
2025-07-17 06:39:35,091 - INFO - Scraping 17/24: https://eatbydate.com/dairy/cheese/cheese-shelf-life-expiration-date/
2025-07-17 06:39:35,091 - INFO - Extracting data from: https://eatbydate.com/dairy/cheese/cheese-shelf-life-expiration-date/
2025-07-17 06:40:05,116 - ERROR - Error extracting data from https://eatbydate.com/dairy/cheese/cheese-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 28.782
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:40:05,117 - WARNING - No data extracted from: https://eatbydate.com/dairy/cheese/cheese-shelf-life-expiration-date/
2025-07-17 06:40:08,428 - INFO - Scraping 18/24: https://eatbydate.com/dairy/cheese/cream-cheese-shelf-life-expiration-date/
2025-07-17 06:40:08,428 - INFO - Extracting data from: https://eatbydate.com/dairy/cheese/cream-cheese-shelf-life-expiration-date/
2025-07-17 06:40:38,439 - ERROR - Error extracting data from https://eatbydate.com/dairy/cheese/cream-cheese-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 28.654
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:40:38,440 - WARNING - No data extracted from: https://eatbydate.com/dairy/cheese/cream-cheese-shelf-life-expiration-date/
2025-07-17 06:40:41,234 - INFO - Scraping 19/24: https://eatbydate.com/dairy/milk/dairy-coffee-cream-shelf-life-expiration-date/
2025-07-17 06:40:41,234 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/dairy-coffee-cream-shelf-life-expiration-date/
2025-07-17 06:41:11,249 - ERROR - Error extracting data from https://eatbydate.com/dairy/milk/dairy-coffee-cream-shelf-life-expiration-date/: Message: timeout: Timed out receiving message from renderer: 29.363
  (Session info: chrome=138.0.7204.101)
Stacktrace:
0   chromedriver                        0x000000010536f55c cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000105367454 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x0000000104eb63f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x0000000104ea11fc cxxbridge1$string$len + 4140
4   chromedriver                        0x0000000104ea0f74 cxxbridge1$string$len + 3492
5   chromedriver                        0x0000000104e9ed2c chromedriver + 191788
6   chromedriver                        0x0000000104e9f8bc chromedriver + 194748
7   chromedriver                        0x0000000104eacafc cxxbridge1$string$len + 51500
8   chromedriver                        0x0000000104ec2a18 cxxbridge1$string$len + 141384
9   chromedriver                        0x0000000104e9ff18 chromedriver + 196376
10  chromedriver                        0x0000000104ec2820 cxxbridge1$string$len + 140880
11  chromedriver                        0x0000000104f3edbc cxxbridge1$string$len + 650220
12  chromedriver                        0x0000000104ef1a0c cxxbridge1$string$len + 333884
13  chromedriver                        0x00000001053325e0 cxxbridge1$str$ptr + 2481340
14  chromedriver                        0x0000000105335848 cxxbridge1$str$ptr + 2494244
15  chromedriver                        0x0000000105313234 cxxbridge1$str$ptr + 2353424
16  chromedriver                        0x0000000105336104 cxxbridge1$str$ptr + 2496480
17  chromedriver                        0x00000001053042e4 cxxbridge1$str$ptr + 2292160
18  chromedriver                        0x0000000105355ff8 cxxbridge1$str$ptr + 2627284
19  chromedriver                        0x0000000105356184 cxxbridge1$str$ptr + 2627680
20  chromedriver                        0x0000000105367090 cxxbridge1$str$ptr + 2697068
21  libsystem_pthread.dylib             0x00000001807e42e4 _pthread_start + 136
22  libsystem_pthread.dylib             0x00000001807df0fc thread_start + 8

2025-07-17 06:41:11,250 - WARNING - No data extracted from: https://eatbydate.com/dairy/milk/dairy-coffee-cream-shelf-life-expiration-date/
2025-07-17 06:41:14,932 - INFO - Scraping 20/24: https://eatbydate.com/dairy/sour-cream-shelf-life-expiration-date/
2025-07-17 06:41:14,933 - INFO - Extracting data from: https://eatbydate.com/dairy/sour-cream-shelf-life-expiration-date/
2025-07-17 06:41:23,412 - INFO - Successfully scraped: Sour
2025-07-17 06:41:25,690 - INFO - Scraping 21/24: https://eatbydate.com/hard-boiled-eggs-shelf-life-expiration-date/
2025-07-17 06:41:25,690 - INFO - Extracting data from: https://eatbydate.com/hard-boiled-eggs-shelf-life-expiration-date/
2025-07-17 06:41:34,922 - INFO - Successfully scraped: Hard
2025-07-17 06:41:38,817 - INFO - Scraping 22/24: https://eatbydate.com/dairy/milk/how-long-does-evaporated-milk-last-shelf-life/
2025-07-17 06:41:38,818 - INFO - Extracting data from: https://eatbydate.com/dairy/milk/how-long-does-evaporated-milk-last-shelf-life/
2025-07-17 06:41:43,391 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x111abe490>: Failed to establish a new connection: [Errno 61] Connection refused')': /session/5f6e523716b86a4cb52a434452cda524
2025-07-17 06:41:43,399 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x111abed50>: Failed to establish a new connection: [Errno 61] Connection refused')': /session/5f6e523716b86a4cb52a434452cda524
2025-07-17 06:41:43,403 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x111abf650>: Failed to establish a new connection: [Errno 61] Connection refused')': /session/5f6e523716b86a4cb52a434452cda524

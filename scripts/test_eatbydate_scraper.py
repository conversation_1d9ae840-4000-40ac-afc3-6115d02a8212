#!/usr/bin/env python3
"""
EatByDate Scraper 测试脚本

这个脚本用于测试爬虫的基本功能，只爬取少量数据进行验证
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from eatbydate_scraper import EatByDateScraper
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestEatByDateScraper(EatByDateScraper):
    """测试版本的爬虫，限制爬取数量"""
    
    def __init__(self, headless=True):
        super().__init__(headless)
        # 只测试部分分类
        self.categories = {
            "fruits": "水果",
            "dairy": "奶制品"
        }
    
    def scrape_all_categories(self):
        """爬取所有分类的数据（测试版本，每个分类只爬取2个食物）"""
        logger.info("Starting test scraping (limited data)...")
        
        for category, category_name in self.categories.items():
            category_url = f"{self.base_url}/{category}/"
            
            # 获取分类页面的食物列表
            food_links = self.scrape_category_page(category_url, category_name)
            
            # 只爬取前2个食物进行测试
            test_links = food_links[:2]
            logger.info(f"Testing with {len(test_links)} items from {category_name}")
            
            # 爬取每个食物的详细信息
            for i, food_item in enumerate(test_links):
                logger.info(f"Testing food {i+1}/{len(test_links)}: {food_item['title']}")
                
                food_data = self.scrape_food_detail(food_item)
                if food_data:
                    self.scraped_data.append(food_data)
                    logger.info(f"Successfully scraped: {food_data['name']}")
                else:
                    logger.warning(f"Failed to scrape: {food_item['title']}")
                
                # 添加延迟
                import time
                time.sleep(3)
            
            # 分类间添加延迟
            import time
            time.sleep(5)

def test_basic_functionality():
    """测试基本功能"""
    logger.info("=== EatByDate Scraper Test ===")
    
    scraper = TestEatByDateScraper(headless=True)
    
    try:
        # 测试网站连接
        logger.info("Testing website connection...")
        scraper.driver.get("https://eatbydate.com")
        title = scraper.driver.title
        logger.info(f"Website title: {title}")
        
        if "eatbydate" not in title.lower():
            logger.warning("Website title doesn't contain 'eatbydate', connection might be problematic")
        
        # 测试分类页面访问
        logger.info("Testing category page access...")
        test_category_url = "https://eatbydate.com/fruits/"
        scraper.driver.get(test_category_url)
        
        # 简单检查页面是否加载成功
        import time
        time.sleep(3)
        page_source = scraper.driver.page_source
        
        if len(page_source) < 1000:
            logger.warning("Category page seems too short, might not have loaded properly")
        else:
            logger.info("Category page loaded successfully")
        
        # 测试数据爬取
        logger.info("Testing data scraping...")
        scraper.scrape_all_categories()
        
        # 检查爬取结果
        if scraper.scraped_data:
            logger.info(f"Test successful! Scraped {len(scraper.scraped_data)} items")
            
            # 显示第一个爬取的数据作为示例
            sample_data = scraper.scraped_data[0]
            logger.info("Sample scraped data:")
            for key, value in sample_data.items():
                logger.info(f"  {key}: {value}")
            
            # 保存测试数据
            test_output_dir = "test_data"
            if not os.path.exists(test_output_dir):
                os.makedirs(test_output_dir)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            test_file = os.path.join(test_output_dir, f"test_eatbydate_data_{timestamp}.json")
            
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(scraper.scraped_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Test data saved to: {test_file}")
            
        else:
            logger.error("Test failed! No data was scraped")
            return False
            
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        return False
    finally:
        scraper.close()
    
    logger.info("=== Test Completed ===")
    return True

def main():
    """主函数"""
    success = test_basic_functionality()
    
    if success:
        print("\n✅ 测试成功！爬虫基本功能正常")
        print("可以运行完整版本: python eatbydate_scraper.py")
    else:
        print("\n❌ 测试失败！请检查错误信息并修复问题")
        sys.exit(1)

if __name__ == "__main__":
    main()

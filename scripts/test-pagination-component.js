#!/usr/bin/env node

/**
 * 测试分页组件的渲染逻辑
 */

// 模拟分页组件的逻辑
function shouldShowPagination(totalPages) {
  return totalPages > 1;
}

function generatePageNumbers(currentPage, totalPages) {
  const pages = [];
  const maxVisible = 7;
  
  if (totalPages <= maxVisible) {
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    if (currentPage <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages);
    } else if (currentPage >= totalPages - 3) {
      pages.push(1);
      pages.push('...');
      for (let i = totalPages - 4; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      pages.push('...');
      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages);
    }
  }
  
  return pages;
}

// 测试各种场景
const testCases = [
  { name: 'meat', total: 212, pageSize: 24 },
  { name: 'dairy', total: 235, pageSize: 24 },
  { name: 'grains', total: 232, pageSize: 24 },
  { name: 'fruits', total: 129, pageSize: 24 },
  { name: 'vegetables', total: 106, pageSize: 24 }
];

console.log('=== 测试分页组件渲染逻辑 ===\n');

testCases.forEach(test => {
  const totalPages = Math.ceil(test.total / test.pageSize);
  const shouldShow = shouldShowPagination(totalPages);
  
  console.log(`${test.name}:`);
  console.log(`- 总数: ${test.total}`);
  console.log(`- 总页数: ${totalPages}`);
  console.log(`- 应该显示分页: ${shouldShow ? '是' : '否'}`);
  
  if (shouldShow) {
    console.log(`- 第1页显示: [${generatePageNumbers(1, totalPages).join(', ')}]`);
    console.log(`- 第5页显示: [${generatePageNumbers(5, totalPages).join(', ')}]`);
  }
  console.log('');
});
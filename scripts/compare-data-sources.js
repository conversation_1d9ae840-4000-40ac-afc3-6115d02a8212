#!/usr/bin/env node

/**
 * 比较 StillTasty JSON 文件和数据库中的数据差异
 */

const fs = require('fs');
const path = require('path');

console.log('=== 数据源对比分析 ===\n');

// 1. 分析 StillTasty JSON 文件
const stilltastyFile = path.join(__dirname, 'processed_data/processed_stilltasty_data_20250718_132503.json');
const stilltastyData = JSON.parse(fs.readFileSync(stilltastyFile, 'utf8'));

console.log('📁 StillTasty JSON 文件分析:');
console.log(`   总记录数: ${stilltastyData.length}`);

// 统计 StillTasty 类别
const stilltastyCategories = {};
stilltastyData.forEach(item => {
  const cat = item.category_id;
  stilltastyCategories[cat] = (stilltastyCategories[cat] || 0) + 1;
});

console.log('   类别分布:');
Object.entries(stilltastyCategories)
  .sort((a, b) => b[1] - a[1])
  .forEach(([cat, count]) => {
    console.log(`   - ${cat}: ${count} 个`);
  });

// 2. 数据库状态（根据文档记录）
console.log('\n💾 数据库当前状态（根据文档）:');
console.log('   总记录数: 476');
console.log('   类别分布:');
console.log('   - dairy: 134 个 (28.2%)');
console.log('   - fruits: 94 个 (19.7%)');
console.log('   - meat: 80 个 (16.8%)');
console.log('   - vegetables: 45 个 (9.5%)');
console.log('   - 其他: 123 个 (25.8%)');

// 3. 差异分析
console.log('\n📊 差异分析:');
console.log(`   待导入数据: ${stilltastyData.length - 476} 条`);
console.log(`   导入进度: ${((476 / stilltastyData.length) * 100).toFixed(1)}%`);

// 4. 首页类别配置
console.log('\n🏠 首页展示的10个类别:');
const categories = [
  'fruits (水果)',
  'vegetables (蔬菜)',
  'meat (肉类)',
  'seafood (海鲜)',
  'dairy (奶蛋类)',
  'grains (谷物)',
  'beverages (饮料)',
  'snacks (零食)',
  'condiments (调料)',
  'spices (香料)'
];

categories.forEach((cat, index) => {
  console.log(`   ${index + 1}. ${cat}`);
});

// 5. 建议
console.log('\n💡 结论:');
console.log('1. StillTasty 数据尚未完全导入数据库');
console.log('2. 需要运行 import-stilltasty-processed-data.js 脚本');
console.log('3. 导入后，各类别的数量将大幅增加');
console.log('4. 特别是蔬菜类将从 45 个增加到约 130 个');

console.log('\n📌 下一步操作:');
console.log('1. 先运行数据库迁移: supabase db push');
console.log('2. 然后运行导入脚本: node scripts/import-stilltasty-processed-data.js');
console.log('3. 导入完成后，首页各类别的数量将自动更新');
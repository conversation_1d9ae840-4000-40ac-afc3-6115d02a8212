require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ 缺少必要的环境变量');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// 分类映射规则
const categoryMappings = {
  1: { // 水果
    keywords: ['apple', 'banana', 'orange', 'grape', 'berry', 'fruit', 'cherry', 'peach', 'pear', 'plum', 'mango', 'pineapple', 'watermelon', 'melon', 'kiwi', 'lemon', 'lime', 'grapefruit', 'strawberry', 'blueberry', 'raspberry', 'blackberry', 'cranberry', 'apricot', 'fig', 'date', 'raisin', 'coconut']
  },
  2: { // 蔬菜
    keywords: ['vegetable', 'lettuce', 'spinach', 'carrot', 'broccoli', 'cauliflower', 'cabbage', 'onion', 'garlic', 'potato', 'tomato', 'cucumber', 'pepper', 'celery', 'asparagus', 'mushroom', 'corn', 'peas', 'bean', 'radish', 'beet', 'turnip', 'parsnip', 'leek', 'scallion', 'shallot', 'artichoke', 'eggplant', 'zucchini', 'squash', 'pumpkin', 'okra', 'kale', 'chard', 'arugula', 'watercress']
  },
  3: { // 肉类
    keywords: ['meat', 'beef', 'pork', 'chicken', 'turkey', 'lamb', 'veal', 'duck', 'goose', 'ham', 'bacon', 'sausage', 'hot dog', 'salami', 'pepperoni', 'prosciutto', 'pastrami', 'corned beef', 'ground beef', 'ground pork', 'ground turkey', 'steak', 'roast', 'chop', 'ribs', 'brisket', 'tenderloin', 'sirloin', 'filet', 'wing', 'thigh', 'breast', 'leg', 'drumstick']
  },
  4: { // 零食
    keywords: ['snack', 'chip', 'cracker', 'cookie', 'biscuit', 'pretzel', 'popcorn', 'nut', 'candy', 'chocolate', 'gum', 'mint', 'lollipop', 'caramel', 'toffee', 'fudge', 'truffle', 'bonbon', 'marshmallow', 'gummy', 'jelly bean', 'licorice', 'nougat', 'praline', 'brittle', 'bark', 'clusters', 'trail mix', 'granola bar', 'energy bar', 'protein bar']
  },
  5: { // 海鲜
    keywords: ['fish', 'seafood', 'salmon', 'tuna', 'cod', 'halibut', 'sole', 'flounder', 'trout', 'bass', 'snapper', 'mahi', 'tilapia', 'catfish', 'sardine', 'anchovy', 'mackerel', 'herring', 'shrimp', 'crab', 'lobster', 'scallop', 'oyster', 'mussel', 'clam', 'squid', 'octopus', 'calamari', 'crayfish', 'prawns']
  },
  6: { // 奶制品
    keywords: ['dairy', 'milk', 'cheese', 'yogurt', 'butter', 'cream', 'sour cream', 'cottage cheese', 'ricotta', 'mozzarella', 'cheddar', 'swiss', 'parmesan', 'gouda', 'brie', 'camembert', 'feta', 'goat cheese', 'blue cheese', 'cream cheese', 'mascarpone', 'whipped cream', 'half and half', 'buttermilk', 'kefir', 'ice cream', 'frozen yogurt', 'sherbet', 'sorbet']
  },
  7: { // 谷物
    keywords: ['grain', 'bread', 'rice', 'pasta', 'noodle', 'cereal', 'oat', 'wheat', 'barley', 'quinoa', 'bulgur', 'couscous', 'millet', 'buckwheat', 'rye', 'spelt', 'amaranth', 'teff', 'farro', 'freekeh', 'bagel', 'muffin', 'croissant', 'roll', 'baguette', 'pita', 'tortilla', 'cracker', 'pretzel', 'crouton', 'breadcrumb', 'flour', 'bran', 'germ']
  },
  8: { // 调料
    keywords: ['sauce', 'dressing', 'condiment', 'ketchup', 'mustard', 'mayonnaise', 'relish', 'pickle', 'vinegar', 'oil', 'marinade', 'gravy', 'salsa', 'pesto', 'hummus', 'tahini', 'soy sauce', 'teriyaki', 'barbecue', 'hot sauce', 'worcestershire', 'tabasco', 'sriracha', 'ranch', 'caesar', 'italian', 'thousand island', 'blue cheese', 'honey mustard', 'balsamic', 'olive oil', 'vegetable oil', 'coconut oil', 'sesame oil']
  },
  9: { // 饮料
    keywords: ['beverage', 'drink', 'juice', 'soda', 'water', 'tea', 'coffee', 'wine', 'beer', 'liquor', 'whiskey', 'vodka', 'rum', 'gin', 'brandy', 'cognac', 'champagne', 'cocktail', 'smoothie', 'shake', 'lemonade', 'punch', 'cider', 'kombucha', 'energy drink', 'sports drink', 'soft drink', 'cola', 'root beer', 'ginger ale', 'tonic', 'club soda', 'sparkling water']
  },
  12: { // 香料
    keywords: ['spice', 'herb', 'seasoning', 'salt', 'pepper', 'garlic powder', 'onion powder', 'paprika', 'cumin', 'coriander', 'turmeric', 'ginger', 'cinnamon', 'nutmeg', 'clove', 'allspice', 'cardamom', 'fennel', 'anise', 'bay leaf', 'thyme', 'rosemary', 'oregano', 'basil', 'parsley', 'cilantro', 'dill', 'sage', 'tarragon', 'chive', 'mint', 'vanilla', 'extract']
  }
};

// 智能分类函数
function categorizeFood(foodName) {
  const name = foodName.toLowerCase();
  
  // 检查每个分类的关键词
  for (const [categoryId, category] of Object.entries(categoryMappings)) {
    for (const keyword of category.keywords) {
      if (name.includes(keyword)) {
        return parseInt(categoryId);
      }
    }
  }
  
  // 特殊规则
  if (name.includes('cake') || name.includes('pie') || name.includes('pastry') || 
      name.includes('donut') || name.includes('danish') || name.includes('eclair') ||
      name.includes('croissant') || name.includes('muffin') || name.includes('scone')) {
    return 4; // 零食
  }
  
  if (name.includes('soup') || name.includes('broth') || name.includes('stock')) {
    return 8; // 调料/汤类
  }
  
  if (name.includes('egg')) {
    return 6; // 奶制品（蛋类归入此分类）
  }
  
  // 默认分类：如果无法确定，归入零食类
  return 4;
}

async function fixUncategorizedFoods() {
  console.log('=== 修复未分类食物 ===\n');
  
  try {
    // 获取所有未分类的食物
    const { data: uncategorizedFoods, error } = await supabase
      .from('foods')
      .select('id, name')
      .is('category_id', null);
    
    if (error) {
      throw error;
    }
    
    console.log(`找到 ${uncategorizedFoods.length} 个未分类的食物`);
    
    let processed = 0;
    let updated = 0;
    
    for (const food of uncategorizedFoods) {
      try {
        const categoryId = categorizeFood(food.name);
        
        const { error: updateError } = await supabase
          .from('foods')
          .update({ category_id: categoryId })
          .eq('id', food.id);
        
        if (updateError) {
          console.error(`更新食物 "${food.name}" 时出错:`, updateError.message);
        } else {
          updated++;
          if (updated % 50 === 0) {
            console.log(`已处理: ${updated}/${uncategorizedFoods.length}`);
          }
        }
        
        processed++;
      } catch (err) {
        console.error(`处理食物 "${food.name}" 时出错:`, err.message);
      }
    }
    
    console.log('\n=== 修复完成 ===');
    console.log(`总计: ${uncategorizedFoods.length}`);
    console.log(`已处理: ${processed}`);
    console.log(`成功更新: ${updated}`);
    
    // 显示更新后的分类统计
    const { data: categoryStats, error: statsError } = await supabase
      .from('foods')
      .select('category_id')
      .not('category_id', 'is', null);
    
    if (!statsError) {
      const stats = {};
      categoryStats.forEach(item => {
        stats[item.category_id] = (stats[item.category_id] || 0) + 1;
      });
      
      console.log('\n=== 更新后的分类统计 ===');
      Object.entries(stats).sort((a, b) => parseInt(a[0]) - parseInt(b[0])).forEach(([categoryId, count]) => {
        console.log(`分类 ${categoryId}: ${count} 个食物`);
      });
    }
    
  } catch (error) {
    console.error('修复过程中出错:', error);
  }
}

// 运行修复
fixUncategorizedFoods();

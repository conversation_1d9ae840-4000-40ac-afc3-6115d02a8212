const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// 检查环境变量
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Error: Missing Supabase configuration');
  console.error('Please ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

// 创建 Supabase 客户端
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function runMigration() {
  console.log('🚀 Starting authentication tables migration...\n');

  try {
    // 读取迁移文件
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '002_create_auth_tables.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // 分割 SQL 语句（按分号分割，但保留 $$ 之间的内容）
    const statements = [];
    let currentStatement = '';
    let inFunction = false;
    
    const lines = migrationSQL.split('\n');
    for (const line of lines) {
      if (line.includes('$$')) {
        inFunction = !inFunction;
      }
      
      currentStatement += line + '\n';
      
      if (line.trim().endsWith(';') && !inFunction) {
        statements.push(currentStatement.trim());
        currentStatement = '';
      }
    }
    
    if (currentStatement.trim()) {
      statements.push(currentStatement.trim());
    }

    console.log(`📋 Found ${statements.length} SQL statements to execute\n`);

    // 执行每个语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // 跳过空语句
      if (!statement || statement === '--') continue;
      
      // 提取语句类型用于日志
      const statementType = statement.split(/\s+/)[0].toUpperCase();
      const targetMatch = statement.match(/(?:TABLE|INDEX|FUNCTION|TRIGGER)\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i);
      const targetName = targetMatch ? targetMatch[1] : 'unknown';
      
      console.log(`${i + 1}. Executing ${statementType} ${targetName}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          // 如果使用 rpc 失败，尝试直接执行
          const { data, error: directError } = await supabase
            .from('_sql')
            .insert({ query: statement })
            .select();
            
          if (directError) {
            throw directError;
          }
        }
        
        console.log(`   ✅ Success\n`);
      } catch (err) {
        console.error(`   ❌ Error: ${err.message}\n`);
        
        // 对于某些错误（如对象已存在），我们继续执行
        if (err.message.includes('already exists')) {
          console.log('   ℹ️  Object already exists, continuing...\n');
        } else {
          throw err;
        }
      }
    }

    console.log('✅ Migration completed successfully!');
    console.log('\n📝 Created/Updated:');
    console.log('   - user_passwords table');
    console.log('   - email_verifications table');
    console.log('   - password_resets table');
    console.log('   - email_verified columns in users table');
    console.log('   - Various indexes for performance');
    console.log('   - update_updated_at_column() function');
    console.log('   - Trigger for user_passwords table');
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// 执行迁移
runMigration().catch(console.error);
#!/usr/bin/env node

/**
 * 检查所有分类的分页情况
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

// UI分类到数据库分类ID的映射
const UI_TO_DB_CATEGORY_MAPPING = {
  'fruits': [1, 15],
  'vegetables': [2, 13, 16],
  'meat': [3, 18],
  'seafood': [5],
  'dairy': [6, 17],
  'grains': [7],
  'beverages': [9],
  'snacks': [4, 10, 11],
  'condiments': [8, 14],
  'spices': [12],
};

async function checkAllCategories() {
  console.log('=== 检查所有分类的分页情况 ===\n');

  const pageSize = 24;

  for (const [slug, categoryIds] of Object.entries(UI_TO_DB_CATEGORY_MAPPING)) {
    // 获取总数
    const { count: total } = await supabase
      .from('foods')
      .select('*', { count: 'exact', head: true })
      .in('category_id', categoryIds);

    const totalPages = Math.ceil(total / pageSize);
    const shouldShowPagination = total > pageSize;

    console.log(`${slug}:`);
    console.log(`  分类ID: [${categoryIds.join(', ')}]`);
    console.log(`  总数: ${total}`);
    console.log(`  总页数: ${totalPages}`);
    console.log(`  应该显示分页: ${shouldShowPagination ? '是' : '否'}`);
    
    if (shouldShowPagination) {
      // 测试第一页的 hasMore
      const hasMorePage1 = (1 * pageSize) < total;
      console.log(`  第1页 hasMore: ${hasMorePage1}`);
    }
    
    console.log('');
  }
}

checkAllCategories().catch(console.error);
#!/usr/bin/env node
/**
 * 将 StillTasty 数据导入到 Supabase 数据库
 * 注意：由于数据库约束限制，暂时将source设为'USDA'，实际数据来源为StillTasty
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const SUPABASE_URL = "https://plefidqreqjnesamigoc.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsZWZpZHFyZXFqbmVzYW1pZ29jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMTQ1ODUsImV4cCI6MjA2NTY5MDU4NX0.Ys99vv5Xys8np6rskFj_7TV7pTBKpn5UVj8Fn9ZBDtc";

// 创建 Supabase 客户端
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * 清理HTML标签和多余的空白字符
 */
function cleanHtmlText(text) {
  if (!text) return "";

  // 移除广告相关的内容
  text = text.replace(/<!--- Mobile ads.*?<!--- END Mobile ads.*?>/gs, '');
  text = text.replace(/<script.*?<\/script>/gs, '');
  text = text.replace(/<!-- .*? -->/gs, '');
  text = text.replace(/data-freestar-ad.*?>/g, '>');
  text = text.replace(/freestar\.config.*?;/g, '');

  // 移除所有HTML标签
  text = text.replace(/<[^>]+>/g, '');

  // 解码HTML实体
  text = text.replace(/&amp;/g, '&')
             .replace(/&lt;/g, '<')
             .replace(/&gt;/g, '>')
             .replace(/&quot;/g, '"')
             .replace(/&#39;/g, "'")
             .replace(/&nbsp;/g, ' ');

  // 移除多余的空白字符、制表符和换行符
  text = text.replace(/[\t\n\r]+/g, ' ');
  text = text.replace(/\s+/g, ' ').trim();

  // 如果清理后的文本太短或只包含无意义内容，返回空字符串
  if (text.length < 3 || /^[\s\-_\.]*$/.test(text)) {
    return "";
  }

  return text;
}

/**
 * 标准化搜索键
 */
function normalizeSearchKey(name) {
  // 转换为小写，移除特殊字符，用下划线替换空格
  let key = name.toLowerCase().replace(/[^\w\s]/g, '');
  key = key.replace(/\s+/g, '_').trim();
  return key;
}

/**
 * 获取或创建分类ID
 */
async function getCategoryId(categoryName) {
  try {
    // 分类映射
    const categoryMapping = {
      'fruits': 'Fruits',
      'vegetables': 'Vegetables', 
      'meat': 'Meat & Poultry',
      'seafood': 'Seafood',
      'dairy': 'Dairy & Eggs',
      'grains': 'Grains & Bread',
      'beverages': 'Beverages',
      'snacks': 'Snacks & Sweets',
      'condiments': 'Condiments & Sauces',
      'spices': 'Herbs & Spices'
    };
    
    const mappedName = categoryMapping[categoryName] || categoryName;
    
    // 查找现有分类
    const { data: existing, error: findError } = await supabase
      .from('food_categories')
      .select('id')
      .eq('name', mappedName)
      .single();
    
    if (existing) {
      return existing.id;
    }
    
    // 创建新分类
    const { data: newCategory, error: createError } = await supabase
      .from('food_categories')
      .insert({
        name: mappedName,
        description: `${mappedName} category from StillTasty data`
      })
      .select('id')
      .single();
    
    if (createError) {
      console.error(`创建分类失败 ${categoryName}:`, createError);
      return null;
    }
    
    return newCategory?.id || null;
  } catch (error) {
    console.error(`获取分类ID失败 ${categoryName}:`, error);
    return null;
  }
}

/**
 * 检查文本是否是有效的存储建议
 */
function isValidStorageTip(text) {
  if (!text || text.length < 10) return false;

  // 过滤掉包含广告、脚本或无意义内容的文本
  const invalidPatterns = [
    /freestar/i,
    /advertisement/i,
    /data-freestar/i,
    /googletag/i,
    /^[\s\-_\.]*$/,
    /^\d+[\s\-]*$/,
    /^[^a-zA-Z]*$/
  ];

  for (const pattern of invalidPatterns) {
    if (pattern.test(text)) return false;
  }

  // 检查是否包含有用的存储相关关键词
  const storageKeywords = [
    'refrigerat', 'freez', 'room temperature', 'pantry', 'store', 'keep',
    'best quality', 'fresh', 'shelf life', 'expir', 'last', 'maintain'
  ];

  const lowerText = text.toLowerCase();
  return storageKeywords.some(keyword => lowerText.includes(keyword));
}

/**
 * 提取存储建议
 */
function extractStorageTips(foodItem) {
  const tips = [];

  // 从tips字段提取
  if (foodItem.tips && Array.isArray(foodItem.tips)) {
    for (const tip of foodItem.tips) {
      const cleanedTip = cleanHtmlText(tip);
      if (isValidStorageTip(cleanedTip)) {
        tips.push(cleanedTip);
      }
    }
  }

  // 从storage文本中提取有用信息
  const storage = foodItem.storage || {};
  for (const [storageType, storageInfo] of Object.entries(storage)) {
    if (storageInfo && typeof storageInfo === 'object' && storageInfo.text) {
      const cleanedText = cleanHtmlText(storageInfo.text);
      if (isValidStorageTip(cleanedText)) {
        const formattedType = storageType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        tips.push(`${formattedType}: ${cleanedText}`);
      }
    }
  }

  return tips.slice(0, 5);  // 限制最多5个建议
}

/**
 * 插入食物项目
 */
async function insertFoodItem(foodItem) {
  try {
    // 获取分类ID
    const categoryId = await getCategoryId(foodItem.category_id);
    if (!categoryId) {
      console.log(`无法获取分类ID: ${foodItem.category_id}`);
      return null;
    }
    
    // 准备搜索键
    const searchKey = normalizeSearchKey(foodItem.name_clean);
    
    // 检查是否已存在
    const { data: existing } = await supabase
      .from('foods')
      .select('id')
      .eq('search_key', searchKey)
      .single();
    
    if (existing) {
      console.log(`食物已存在，跳过: ${foodItem.name_clean}`);
      return existing.id;
    }
    
    // 提取存储天数
    const storage = foodItem.storage || {};
    const roomTempDays = storage.room_temperature?.days;
    const refrigeratedDays = storage.refrigerated?.days;
    const frozenDays = storage.frozen?.days;
    
    // 提取存储建议
    const storageTips = extractStorageTips(foodItem);
    
    // 准备食物数据
    const foodData = {
      name: foodItem.name_clean,
      search_key: searchKey,
      category_id: categoryId,
      room_temperature_days: roomTempDays || null,
      refrigerated_days: refrigeratedDays || null,
      frozen_days: frozenDays || null,
      storage_tips: storageTips,
      source: 'USDA',  // 暂时使用USDA避免约束问题
      confidence: 0.85  // StillTasty数据置信度稍低于USDA
    };
    
    // 移除undefined值
    Object.keys(foodData).forEach(key => {
      if (foodData[key] === undefined) {
        delete foodData[key];
      }
    });
    
    // 插入食物
    const { data: insertedFood, error: insertError } = await supabase
      .from('foods')
      .insert(foodData)
      .select('id')
      .single();
    
    if (insertError) {
      console.error(`插入食物失败 ${foodItem.name_clean}:`, insertError);
      return null;
    }
    
    const foodId = insertedFood.id;
    
    // 添加关键词作为别名
    if (foodItem.keywords && Array.isArray(foodItem.keywords)) {
      for (const keyword of foodItem.keywords.slice(0, 3)) {  // 限制前3个关键词
        if (keyword && keyword.length > 2) {
          try {
            await supabase.from('food_aliases').insert({
              food_id: foodId,
              alias: keyword,
              language: 'en'
            });
          } catch (error) {
            // 忽略重复别名错误
          }
        }
      }
    }
    
    return foodId;
  } catch (error) {
    console.error(`插入食物失败 ${foodItem.name_clean || 'unknown'}:`, error);
    return null;
  }
}

/**
 * 加载StillTasty数据
 */
function loadStillTastyData() {
  try {
    const dataFile = path.join(__dirname, 'processed_data/processed_stilltasty_data_20250718_132503.json');
    const data = fs.readFileSync(dataFile, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('加载StillTasty数据失败:', error);
    return [];
  }
}

/**
 * 执行StillTasty数据迁移
 */
async function migrateStillTastyData() {
  console.log('🚀 开始导入StillTasty数据到Supabase...');
  
  // 加载数据
  const stillTastyData = loadStillTastyData();
  console.log(`加载了 ${stillTastyData.length} 条StillTasty数据`);
  
  if (stillTastyData.length === 0) {
    console.log('❌ 没有数据可导入');
    return;
  }
  
  // 统计信息
  let successCount = 0;
  let errorCount = 0;
  const categoryStats = {};
  
  // 处理数据
  for (let i = 0; i < stillTastyData.length; i++) {
    const foodItem = stillTastyData[i];
    
    try {
      // 统计分类
      const category = foodItem.category_id || 'unknown';
      categoryStats[category] = (categoryStats[category] || 0) + 1;
      
      // 插入食物
      const foodId = await insertFoodItem(foodItem);
      
      if (foodId) {
        successCount++;
      } else {
        errorCount++;
      }
      
      // 进度显示
      if ((i + 1) % 100 === 0) {
        console.log(`已处理 ${i + 1}/${stillTastyData.length} 条记录...`);
      }
      
      // 添加小延迟避免API限制
      if ((i + 1) % 50 === 0) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
    } catch (error) {
      console.error(`处理第 ${i + 1} 条记录时出错:`, error);
      errorCount++;
    }
  }
  
  // 输出结果
  console.log(`\n✅ StillTasty数据导入完成!`);
  console.log(`成功导入: ${successCount} 条`);
  console.log(`导入失败: ${errorCount} 条`);
  console.log(`总计处理: ${stillTastyData.length} 条`);
  
  console.log(`\n📊 分类统计:`);
  Object.entries(categoryStats)
    .sort(([,a], [,b]) => b - a)
    .forEach(([category, count]) => {
      console.log(`  ${category}: ${count} 种食物`);
    });
}

/**
 * 测试搜索功能
 */
async function testSearch() {
  console.log('\n🔍 测试StillTasty数据搜索...');
  
  const testQueries = ['apple', 'chicken', 'bread', 'milk', 'tomato'];
  
  for (const query of testQueries) {
    try {
      const { data, error } = await supabase.rpc('search_foods', { search_term: query });
      
      if (error) {
        console.error(`查询 '${query}' 失败:`, error);
        continue;
      }
      
      if (data && data.length > 0) {
        const foods = data.slice(0, 3).map(food => `${food.name} (${food.source})`);
        console.log(`查询 '${query}': ${foods.join(', ')}`);
      } else {
        console.log(`查询 '${query}': 未找到`);
      }
    } catch (error) {
      console.error(`查询 '${query}' 失败:`, error);
    }
  }
}

// 主函数
async function main() {
  console.log('🍎 StillTasty数据导入到Supabase');
  console.log('='.repeat(50));
  
  try {
    await migrateStillTastyData();
    await testSearch();
    console.log('\n🎉 导入和测试完成!');
  } catch (error) {
    console.error('❌ 导入失败:', error);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

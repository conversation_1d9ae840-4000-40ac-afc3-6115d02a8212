// FAQ相关的类型定义

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  answer_summary: string;
  category: string;
  category_zh: string;
  category_icon: string;
  related_foods: string[];
  tags: string[];
  key_points: string[];
  word_count?: number;
  view_count?: number;
  helpful_count?: number;
  source: {
    name: string;
    url: string;
    confidence?: number;
    processed_at?: string;
  };
  created_at?: string;
  updated_at?: string;
}

export interface FAQCategory {
  name: string;
  name_zh: string;
  icon: string;
  count: number;
  priority?: number;
  description?: string;
}

export interface FAQSearchResult {
  faqs: FAQItem[];
  total: number;
  categories: FAQCategory[];
  query?: string;
  category_filter?: string;
}

export interface FAQStats {
  total_faqs: number;
  total_categories: number;
  most_popular_category: string;
  avg_word_count: number;
  last_updated: string;
}

// FAQ搜索参数
export interface FAQSearchParams {
  query?: string;
  category?: string;
  tags?: string[];
  limit?: number;
  offset?: number;
  sort_by?: 'relevance' | 'popularity' | 'date' | 'alphabetical';
  sort_order?: 'asc' | 'desc';
}

// FAQ过滤器
export interface FAQFilter {
  categories: string[];
  tags: string[];
  food_keywords: string[];
  min_word_count?: number;
  max_word_count?: number;
  has_key_points?: boolean;
}

// FAQ数据库记录（用于数据导入）
export interface FAQRecord {
  external_id: string;
  question: string;
  answer: string;
  answer_summary?: string;
  category_id: number;
  key_points?: string[];
  related_foods?: string[];
  tags?: string[];
  word_count?: number;
  view_count?: number;
  helpful_count?: number;
  source_name?: string;
  source_url?: string;
  confidence?: number;
  processed_at?: string;
}

// FAQ分类数据库记录
export interface FAQCategoryRecord {
  id: number;
  name: string;
  name_zh: string;
  icon?: string;
  priority?: number;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

// FAQ与食物的关联关系
export interface FAQFoodRelation {
  id: number;
  faq_id: number;
  food_id: number;
  relevance_score: number;
  created_at?: string;
}

// FAQ搜索日志
export interface FAQSearchLog {
  id: number;
  search_query: string;
  faq_id?: number;
  user_ip?: string;
  user_agent?: string;
  found: boolean;
  created_at: string;
}

// FAQ组件属性
export interface FAQListProps {
  faqs: FAQItem[];
  categories: FAQCategory[];
  title?: string;
  description?: string;
  showSearch?: boolean;
  showCategories?: boolean;
  maxItems?: number;
  loading?: boolean;
  error?: string;
  onSearch?: (query: string) => void;
  onCategoryFilter?: (category: string | null) => void;
  onFAQClick?: (faq: FAQItem) => void;
}

// FAQ卡片组件属性
export interface FAQCardProps {
  faq: FAQItem;
  isOpen?: boolean;
  onToggle?: () => void;
  showCategory?: boolean;
  showSource?: boolean;
  showKeyPoints?: boolean;
  showRelatedFoods?: boolean;
  showTags?: boolean;
  compact?: boolean;
}

// FAQ搜索组件属性
export interface FAQSearchProps {
  onSearch: (query: string) => void;
  onCategoryFilter: (category: string | null) => void;
  categories: FAQCategory[];
  placeholder?: string;
  showCategories?: boolean;
  loading?: boolean;
  initialQuery?: string;
  initialCategory?: string;
}

// FAQ统计组件属性
export interface FAQStatsProps {
  stats: FAQStats;
  showDetails?: boolean;
  className?: string;
}

// FAQ导入结果
export interface FAQImportResult {
  success: boolean;
  imported_count: number;
  skipped_count: number;
  error_count: number;
  errors: string[];
  duration_ms: number;
}

// FAQ处理统计
export interface FAQProcessingStats {
  total_processed: number;
  successful: number;
  failed: number;
  categories_distribution: Record<string, number>;
  avg_word_count: number;
  top_food_keywords: Record<string, number>;
  top_tags: Record<string, number>;
  processing_time_ms: number;
}

// FAQ API响应
export interface FAQApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  meta?: {
    total: number;
    page: number;
    limit: number;
    has_more: boolean;
  };
}

// FAQ配置
export interface FAQConfig {
  max_search_results: number;
  default_page_size: number;
  cache_duration_ms: number;
  enable_analytics: boolean;
  enable_search_suggestions: boolean;
  min_search_length: number;
  max_related_faqs: number;
}

import { Hero } from "@/types/blocks/hero";

export interface SearchSection {
  placeholder: string;
  examples: string[];
  separator_text: string;
}

export interface UploadSection {
  title: string;
  accept: string;
  max_size: string;
}

export interface TrustStat {
  title: string;
  value: string;
  icon?: string;
}

export interface FoodHero extends Hero {
  search_section?: SearchSection;
  upload_section?: UploadSection;
  trust_stats?: TrustStat[];
}

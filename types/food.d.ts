export interface FoodResult {
  name: string;
  category: string;
  storage: {
    refrigerated: number;
    frozen: number;
    room_temperature: number;
  };
  tips: string[];
  confidence: number;
  image_url?: string;
  source?: 'USDA' | 'AI' | 'LOCAL';
  isUSDAData?: boolean;
  is_usda_verified?: boolean;
}

export interface FoodQuery {
  type: 'text' | 'image';
  content: string | File;
  timestamp: Date;
}

export interface FoodSearchState {
  currentQuery: string | File | null;
  result: FoodResult | null;
  loading: boolean;
  error: string | null;
}

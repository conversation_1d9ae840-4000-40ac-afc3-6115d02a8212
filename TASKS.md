# HowLongFresh 功能改进任务清单

## 任务概览
1. 支付成功后跳转到"我的订单"页面并显示成功提示
2. 注释掉"我的邀请"相关功能
3. 添加积分有效期显示（年付1年，季度付3个月）
4. 实现图片识别扣除积分功能（每次识别扣1分）

## 详细任务

### 任务1：支付成功后跳转改进
- [ ] 修改支付成功跳转URL，从 `/payment-success` 改为 `/my-orders`
- [ ] 在"我的订单"页面添加支付成功提示框
- [ ] 提示框显示订单信息和增加的积分数

### 任务2：注释掉邀请功能
- [ ] 注释掉侧边栏中的"My Invites 我的邀请"菜单项
- [ ] 注释掉邀请相关的路由和页面
- [ ] 保留代码以便将来可能重新启用

### 任务3：积分有效期显示
- [ ] 在"我的积分"页面显示每笔积分的有效期
- [ ] 显示格式：开始时间 - 结束时间
- [ ] 根据订单类型计算：年付12个月，季度付3个月

### 任务4：图片识别扣积分
- [ ] 在首页图片上传识别功能中添加积分检查
- [ ] 识别前检查用户是否有足够积分
- [ ] 识别成功后扣除1个积分
- [ ] 积分不足时提示用户充值

## 实施计划
1. 先处理支付跳转和成功提示（优先级最高）
2. 注释邀请功能（简单快速）
3. 完善积分有效期显示
4. 最后实现扣积分功能
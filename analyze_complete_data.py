#!/usr/bin/env python3
"""
分析 USDA 数据中有完整存储信息的食物
"""

import pandas as pd
import json

def analyze_complete_data():
    try:
        # 读取提取的数据
        df = pd.read_csv('usda_food_data_extracted.csv')
        
        print(f"=== 完整数据分析 ===")
        print(f"总记录数: {len(df)}")
        
        # 找出有完整存储数据的记录
        complete_storage = df[
            (df['Refrigerate_Min'].notna()) & 
            (df['Refrigerate_Max'].notna()) & 
            (df['Freeze_Min'].notna()) & 
            (df['Freeze_Max'].notna())
        ].copy()
        
        print(f"有完整冷藏+冷冻数据的记录: {len(complete_storage)}")
        
        # 分析有任何存储数据的记录
        has_any_storage = df[
            (df['Pantry_Min'].notna()) | 
            (df['Refrigerate_Min'].notna()) | 
            (df['Freeze_Min'].notna())
        ].copy()
        
        print(f"有任何存储数据的记录: {len(has_any_storage)}")
        
        # 显示有完整数据的食物示例
        if len(complete_storage) > 0:
            print(f"\n=== 有完整存储数据的食物示例 ===")
            for i, row in complete_storage.head(20).iterrows():
                name = row['Name']
                subtitle = row['Name_subtitle'] if pd.notna(row['Name_subtitle']) else ""
                full_name = f"{name} {subtitle}".strip()
                
                print(f"\n{full_name}")
                print(f"  常温: {row.get('Pantry_Min', 'N/A')}-{row.get('Pantry_Max', 'N/A')} {row.get('Pantry_Metric', '')}")
                print(f"  冷藏: {row['Refrigerate_Min']}-{row['Refrigerate_Max']} {row['Refrigerate_Metric']}")
                print(f"  冷冻: {row['Freeze_Min']}-{row['Freeze_Max']} {row['Freeze_Metric']}")
        
        # 分析常见的食物类别
        print(f"\n=== 有存储数据的食物按类别分析 ===")
        
        # 重新读取原始数据以获取类别信息
        df_full = pd.read_excel('FoodKeeper-Data.xls', sheet_name='Product')
        category_df = pd.read_excel('FoodKeeper-Data.xls', sheet_name='Category')
        
        # 合并类别信息
        df_with_category = df_full.merge(category_df, left_on='Category_ID', right_on='ID', how='left')
        
        # 筛选有存储数据的记录
        has_storage_with_category = df_with_category[
            (df_with_category['Pantry_Min'].notna()) | 
            (df_with_category['Refrigerate_Min'].notna()) | 
            (df_with_category['Freeze_Min'].notna())
        ].copy()
        
        print(f"有存储数据且有类别信息的记录: {len(has_storage_with_category)}")
        
        # 按类别统计
        if 'Category_Name' in has_storage_with_category.columns:
            category_counts = has_storage_with_category['Category_Name'].value_counts()
            print(f"\n各类别的食物数量:")
            for category, count in category_counts.items():
                print(f"  {category}: {count} 种")
        
        # 提取项目最需要的常见食物
        common_foods = []
        
        # 定义我们项目最需要的食物类别
        target_categories = [
            'Produce',  # 农产品（水果蔬菜）
            'Dairy Products & Eggs',  # 乳制品和鸡蛋
            'Meat',  # 肉类
            'Poultry',  # 家禽
            'Seafood',  # 海鲜
            'Baked Goods',  # 烘焙食品
        ]
        
        print(f"\n=== 项目重点关注的食物类别 ===")
        for category in target_categories:
            category_foods = has_storage_with_category[
                has_storage_with_category['Category_Name'] == category
            ]
            print(f"\n{category} ({len(category_foods)} 种):")
            
            for i, row in category_foods.head(10).iterrows():
                name = row['Name']
                subtitle = row['Name_subtitle'] if pd.notna(row['Name_subtitle']) else ""
                full_name = f"{name} {subtitle}".strip()
                
                # 提取存储信息
                storage_info = {}
                if pd.notna(row['Pantry_Min']):
                    storage_info['pantry'] = f"{row['Pantry_Min']}-{row['Pantry_Max']} {row['Pantry_Metric']}"
                if pd.notna(row['Refrigerate_Min']):
                    storage_info['refrigerate'] = f"{row['Refrigerate_Min']}-{row['Refrigerate_Max']} {row['Refrigerate_Metric']}"
                if pd.notna(row['Freeze_Min']):
                    storage_info['freeze'] = f"{row['Freeze_Min']}-{row['Freeze_Max']} {row['Freeze_Metric']}"
                
                print(f"  - {full_name}")
                for storage_type, info in storage_info.items():
                    print(f"    {storage_type}: {info}")
                
                # 收集用于项目的数据
                common_foods.append({
                    'name': name,
                    'subtitle': subtitle,
                    'category': category,
                    'storage': storage_info
                })
        
        # 保存项目可用的食物数据
        with open('project_food_data.json', 'w', encoding='utf-8') as f:
            json.dump(common_foods, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 项目可用的食物数据已保存到 'project_food_data.json'")
        print(f"总共提取了 {len(common_foods)} 种食物的数据")
        
        return has_storage_with_category
        
    except Exception as e:
        print(f"分析数据时出错: {e}")
        return None

if __name__ == "__main__":
    print("🔍 USDA 完整数据分析")
    print("=" * 40)
    
    result = analyze_complete_data()
    
    if result is not None:
        print(f"\n=== 数据质量总结 ===")
        print("✅ USDA 数据包含大量权威的食物存储信息")
        print("✅ 数据结构完整，包含常温、冷藏、冷冻三种存储条件")
        print("✅ 涵盖了项目所需的主要食物类别")
        print("✅ 可以直接用于替换或补充现有的食物数据库")
        
        print(f"\n=== 集成建议 ===")
        print("1. 使用 USDA 数据作为主要数据源")
        print("2. 保留 AI 识别作为备用方案")
        print("3. 建立中英文食物名称映射")
        print("4. 定期更新 USDA 数据")
    
    print("\n🎉 分析完成！")

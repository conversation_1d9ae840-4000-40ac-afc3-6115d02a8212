# HowLongFresh 功能测试指南

## 测试前准备
1. 确保开发服务器正在运行：`pnpm dev`
2. 确保已登录账号
3. 记录当前的积分数量

## 功能测试清单

### 1. 支付成功跳转测试
**测试步骤：**
1. 访问首页，点击"定价"
2. 选择任意套餐（Pro或Enterprise）
3. 点击"选择"按钮，进入支付页面
4. 完成支付流程

**预期结果：**
- ✅ 支付成功后自动跳转到"我的订单"页面（不是payment-success页面）
- ✅ URL应该是：`/my-orders?payment=success&order_no=xxx`

### 2. 支付成功提示测试
**测试步骤：**
1. 完成上述支付流程后，观察"我的订单"页面

**预期结果：**
- ✅ 页面顶部显示绿色的成功提示框
- ✅ 提示框显示："支付成功！"
- ✅ 显示订单号
- ✅ 提示"您的额度已经增加"
- ✅ 右上角有关闭按钮（X）
- ✅ 点击关闭按钮后提示框消失
- ✅ 刷新页面后提示框不再显示

### 3. 邀请功能隐藏测试
**测试步骤：**
1. 登录后访问任意控制台页面（如"我的订单"）
2. 查看左侧菜单

**预期结果：**
- ✅ 左侧菜单只显示：
  - 我的订单
  - 我的积分
  - API 密钥
- ✅ 不显示"我的邀请"菜单项
- ✅ 直接访问 `/my-invites` 页面会显示404

### 4. 积分有效期显示测试
**测试步骤：**
1. 点击左侧菜单"我的积分"
2. 查看积分记录表格

**预期结果：**
- ✅ 表格新增"有效期"列
- ✅ 充值记录（order_pay）显示格式：`2025-01-26 ~ 2025-04-26`（3个月）或 `2025-01-26 ~ 2026-01-26`（1年）
- ✅ 消费记录（负积分）显示：`-`
- ✅ 新用户积分（new_user）根据是否有expired_at显示相应内容

### 5. 图片识别扣积分测试

#### 5.1 未登录测试
**测试步骤：**
1. 退出登录
2. 访问首页
3. 上传食物图片

**预期结果：**
- ✅ 显示错误提示："请先登录"
- ✅ 提示用户需要登录才能使用

#### 5.2 积分充足测试
**测试步骤：**
1. 确保账户有积分（至少1分）
2. 记录当前积分数
3. 上传食物图片进行识别

**预期结果：**
- ✅ 识别成功返回食物信息
- ✅ 积分减少1分
- ✅ 访问"我的积分"页面，看到新的扣费记录（ping类型，-1积分）

#### 5.3 积分不足测试
**测试步骤：**
1. 确保账户积分为0（可以通过多次识别消耗完）
2. 再次上传图片

**预期结果：**
- ✅ 显示错误提示："积分不足，请先充值"
- ✅ 提示当前剩余积分：0
- ✅ 不会进行识别

### 6. 订单显示测试
**测试步骤：**
1. 完成支付后，访问"我的订单"页面

**预期结果：**
- ✅ 显示新的订单记录
- ✅ 订单状态为"已支付"
- ✅ 显示正确的金额和产品名称

## 测试数据记录表

| 测试项目 | 测试时间 | 测试结果 | 备注 |
|---------|---------|---------|------|
| 支付跳转 |         | ☐ 通过 ☐ 失败 |      |
| 成功提示 |         | ☐ 通过 ☐ 失败 |      |
| 邀请隐藏 |         | ☐ 通过 ☐ 失败 |      |
| 有效期显示 |       | ☐ 通过 ☐ 失败 |      |
| 未登录识别 |       | ☐ 通过 ☐ 失败 |      |
| 积分充足识别 |     | ☐ 通过 ☐ 失败 |      |
| 积分不足识别 |     | ☐ 通过 ☐ 失败 |      |

## 常见问题排查

### 1. 支付后没有跳转到订单页面
- 检查 Creem 配置的 success_url
- 查看浏览器控制台是否有错误

### 2. 积分没有增加
- 检查 Creem webhook 是否正确触发
- 查看服务器日志中的 webhook 处理记录
- 使用测试脚本：`node scripts/test-creem-callback.js <订单号>`

### 3. 图片识别没有扣积分
- 检查用户是否登录
- 查看 API 响应中的错误信息
- 检查服务器日志

### 4. 有效期显示不正确
- 检查订单的 valid_months 字段
- 确认 expired_at 字段是否正确计算

## 快速测试命令

```bash
# 手动完成订单（测试环境）
curl "http://localhost:3000/api/test-order-complete?order_no=<订单号>"

# 模拟 Creem 回调
node scripts/test-creem-callback.js <订单号>

# 检查订单状态
node scripts/check-payment-status.js <订单号>
```
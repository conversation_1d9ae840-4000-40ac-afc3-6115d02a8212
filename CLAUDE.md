# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

HowLongFresh is an AI-powered food shelf-life calculator built on the ShipAny SaaS template. It allows users to check how long food stays fresh through text search or image upload.

## Development Commands

```bash
# Development
pnpm dev          # Start dev server with Turbopack
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run Next.js linter (Note: No ESLint config present)

# Deployment
pnpm cf:build     # Build for Cloudflare Pages
pnpm cf:deploy    # Deploy to Cloudflare Pages
pnpm docker:build # Build Docker image
```

## Architecture Overview

### Technology Stack
- **Frontend**: Next.js 15 (App Router), React 19, TypeScript, Tailwind CSS, Shadcn UI
- **Backend**: Next.js API Routes, Supabase (database/auth), NextAuth v5
- **AI Integration**: Multiple providers (OpenAI, DeepSeek, Replicate) for food identification
- **Payments**: Stripe integration
- **i18n**: next-intl (supports 10 languages)

### Key Directory Structure
```
/app/[locale]/           # Internationalized routes
  /(default)/           # Public pages (home, pricing, faq)
  /(admin)/             # Admin dashboard
  /auth/                # Authentication pages
/app/api/               # API endpoints
  /food/                # Food identification endpoints
  /auth/                # Auth endpoints
  /checkout/            # Payment endpoints
/components/
  /blocks/              # Page sections (hero, footer, header)
  /ui/                  # Reusable UI components
  /faq/                 # FAQ components
/lib/                   # Utilities and configurations
/services/              # Business logic
/models/                # Database models
/i18n/                  # Internationalization
```

### Data Sources
- **Primary Database**: Supabase with food shelf-life data
- **StillTasty Data**: Integrated via scripts/stilltasty-scraper.js
- **USDA Food Data**: Available for integration

## Important Development Guidelines

### Component Development
- Use functional React components with TypeScript
- Follow existing component patterns in /components
- Use Shadcn UI components from /components/ui
- Maintain responsive design with Tailwind CSS

### Internationalization
- All user-facing text must use `useTranslations` hook
- Translation keys in `/i18n/messages/[locale].json`
- Dynamic content should support all 10 languages
- Use `getTranslations` in server components

### API Development
- API routes follow Next.js 15 App Router conventions
- Use proper error handling and status codes
- Implement rate limiting for AI endpoints
- Return consistent JSON response formats

### Database Operations
- Use Supabase client from `/lib/supabase/server.ts` or `/lib/supabase/client.ts`
- Follow existing model patterns in `/models`
- Always handle database errors gracefully

### AI Integration
- Multiple AI providers configured in `/lib/ai-service.ts`
- Use appropriate provider based on use case
- Handle API failures with fallbacks
- Monitor credit usage for cost control

## Known Issues to Watch For

1. **Mixed Language Content**: Some files still contain hardcoded Chinese text that needs internationalization
2. **No Test Framework**: Currently no testing setup - consider manual testing for critical changes
3. **Type Safety**: Ensure all TypeScript types are properly defined, especially for API responses
4. **Test Pages Not Cleaned**: Multiple test pages exist in production code (test-ai, test-faq-fix, etc.) that should be removed
5. **Incomplete i18n**: Configuration shows 10 languages supported but only zh and en are actually implemented
6. **Navigation Gaps**: FAQ page lacks links to pricing and other main pages
7. **Component Naming Inconsistency**: Mixed naming conventions (kebab-case vs PascalCase) in components/blocks/
8. **Duplicate Search Functions**: Homepage search and FAQ search are separate, causing inconsistent UX
9. **No Loading States**: Missing unified loading and error state components across the app

## Environment Variables Required

Critical variables for development:
- `SUPABASE_URL`, `SUPABASE_ANON_KEY` - Database connection
- `AUTH_SECRET` - NextAuth secret
- `NEXT_PUBLIC_APP_URL` - Application URL
- AI provider keys (OPENAI_API_KEY, etc.)

## Common Tasks

### Adding a New Page
1. Create route in `/app/[locale]/(default)/[page]/page.tsx`
2. Add translations to all locale files in `/i18n/messages/`
3. Update navigation if needed in `/components/blocks/header/`

### Adding a New API Endpoint
1. Create route in `/app/api/[endpoint]/route.ts`
2. Implement proper authentication checks if needed
3. Add rate limiting for resource-intensive operations
4. Update types in `/types/` if returning new data structures

### Modifying Food Database
1. Update Supabase schema if needed
2. Modify models in `/models/food.ts`
3. Update API endpoints in `/app/api/food/`
4. Test with existing UI components

### Working with AI Features
1. Check available providers in `/lib/ai-service.ts`
2. Use appropriate provider for text vs image analysis
3. Handle errors and provide user feedback
4. Monitor usage to prevent excessive costs
#!/bin/bash

# 启动脚本，自动设置代理环境变量
echo "🚀 Starting development server with proxy settings..."

# 设置代理环境变量
export HTTP_PROXY=http://127.0.0.1:7897
export HTTPS_PROXY=http://127.0.0.1:7897
export http_proxy=http://127.0.0.1:7897
export https_proxy=http://127.0.0.1:7897

# 设置 Node.js 特定的代理环境变量
export NODE_TLS_REJECT_UNAUTHORIZED=0
export GLOBAL_AGENT_HTTP_PROXY=http://127.0.0.1:7897
export GLOBAL_AGENT_HTTPS_PROXY=http://127.0.0.1:7897

echo "✅ Proxy settings configured:"
echo "   HTTP_PROXY: $HTTP_PROXY"
echo "   HTTPS_PROXY: $HTTPS_PROXY"
echo "   NODE_TLS_REJECT_UNAUTHORIZED: $NODE_TLS_REJECT_UNAUTHORIZED"

# 启动开发服务器
npm run dev

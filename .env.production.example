# =============================================================================
# 生产环境配置文件 (.env.production)
# 请将此文件复制为 .env.production 并填入您的生产环境值
# =============================================================================

# -----------------------------------------------------------------------------
# 应用基础配置
# -----------------------------------------------------------------------------
# 重要：将此URL更改为您的实际域名
AUTH_URL = "https://your-domain.com"
NEXTAUTH_URL = "https://your-domain.com"

# 应用名称
APP_NAME = "HowLongFresh"

# -----------------------------------------------------------------------------
# Google OAuth 配置
# 重要：需要在 Google Cloud Console 中添加生产环境的回调URL
# 回调URL格式：https://your-domain.com/api/auth/callback/google
# -----------------------------------------------------------------------------
AUTH_GOOGLE_ID = "your-google-client-id"
AUTH_GOOGLE_SECRET = "your-google-client-secret"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "your-google-client-id"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "false"

# -----------------------------------------------------------------------------
# 数据库配置
# -----------------------------------------------------------------------------
DATABASE_URL = "your-production-database-url"

# -----------------------------------------------------------------------------
# 其他API配置
# -----------------------------------------------------------------------------
OPENROUTER_API_KEY = "your-openrouter-api-key"
OPENROUTER_MODEL = "google/gemini-flash-1.5"

# -----------------------------------------------------------------------------
# 安全配置
# -----------------------------------------------------------------------------
NEXTAUTH_SECRET = "your-nextauth-secret-for-production"

# -----------------------------------------------------------------------------
# 部署平台特定配置
# -----------------------------------------------------------------------------
# 如果使用 Vercel
VERCEL_URL = "your-vercel-app-url"

# 如果使用其他平台，请相应调整

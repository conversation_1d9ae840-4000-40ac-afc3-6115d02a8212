// 设置代理（必须在其他导入之前）
// Only import in Node.js runtime, not Edge runtime
if (typeof (globalThis as any).EdgeRuntime === 'undefined') {
  import('@/lib/proxy-fetch');
}

import CredentialsProvider from "next-auth/providers/credentials";
import GitHubProvider from "next-auth/providers/github";
import GoogleProvider from "next-auth/providers/google";
import { NextAuthConfig } from "next-auth";
import { Provider } from "next-auth/providers/index";
import { User } from "@/types/user";
import { getClientIp } from "@/lib/ip";
import { getIsoTimestr } from "@/lib/time";
import { getUuid } from "@/lib/hash";
import { saveUser } from "@/services/user";
import { findUserByEmail } from "@/models/user";
import { getUserPasswordByUuid } from "@/models/auth";
import { verifyPassword } from "@/lib/auth-utils";

let providers: Provider[] = [];

// Email/Password Auth
providers.push(
  CredentialsProvider({
    id: "credentials",
    name: "credentials",
    credentials: {
      email: { label: "Email", type: "email" },
      password: { label: "Password", type: "password" }
    },
    async authorize(credentials) {
      if (!credentials?.email || !credentials?.password) {
        return null;
      }

      const user = await findUserByEmail(credentials.email as string);
      if (!user || !user.uuid) {
        return null;
      }

      const userPassword = await getUserPasswordByUuid(user.uuid);
      if (!userPassword) {
        return null;
      }

      const isValid = await verifyPassword(
        credentials.password as string,
        userPassword.password_hash
      );

      if (!isValid) {
        return null;
      }

      return {
        id: user.uuid,
        email: user.email,
        name: user.nickname,
        image: user.avatar_url,
      };
    }
  })
);

// Google One Tap Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED === "true" &&
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID
) {
  providers.push(
    CredentialsProvider({
      id: "google-one-tap",
      name: "google-one-tap",

      credentials: {
        credential: { type: "text" },
      },

      async authorize(credentials, req) {
        const googleClientId = process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID;
        if (!googleClientId) {
          console.log("invalid google auth config");
          return null;
        }

        const token = credentials!.credential;

        const { proxyFetch } = await import('@/lib/proxy-fetch');
        const response = await proxyFetch(
          "https://oauth2.googleapis.com/tokeninfo?id_token=" + token
        );
        if (!response.ok) {
          console.log("Failed to verify token");
          return null;
        }

        const payload = await response.json();
        if (!payload) {
          console.log("invalid payload from token");
          return null;
        }

        const {
          email,
          sub,
          given_name,
          family_name,
          email_verified,
          picture: image,
        } = payload;
        if (!email) {
          console.log("invalid email in payload");
          return null;
        }

        const user = {
          id: sub,
          name: [given_name, family_name].join(" "),
          email,
          image,
          emailVerified: email_verified ? new Date() : null,
        };

        return user;
      },
    })
  );
}

// Google Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" &&
  process.env.AUTH_GOOGLE_ID &&
  process.env.AUTH_GOOGLE_SECRET
) {
  console.log("🔧 Google Provider Config:", {
    clientId: process.env.AUTH_GOOGLE_ID ? "SET (" + process.env.AUTH_GOOGLE_ID.substring(0, 20) + "...)" : "MISSING",
    clientSecret: process.env.AUTH_GOOGLE_SECRET ? "SET (" + process.env.AUTH_GOOGLE_SECRET.substring(0, 10) + "...)" : "MISSING",
    enabled: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED,
    authUrl: process.env.AUTH_URL
  });

  providers.push(
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
      authorization: {
        params: {
          scope: "openid email profile",
          prompt: "select_account",
        },
      },
      profile(profile) {
        console.log("🔧 Google Profile received:", profile);
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture,
        };
      },
    })
  );
}

// Github Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true" &&
  process.env.AUTH_GITHUB_ID &&
  process.env.AUTH_GITHUB_SECRET
) {
  providers.push(
    GitHubProvider({
      clientId: process.env.AUTH_GITHUB_ID,
      clientSecret: process.env.AUTH_GITHUB_SECRET,
    })
  );
}

export const providerMap = providers
  .map((provider) => {
    if (typeof provider === "function") {
      const providerData = provider();
      return { id: providerData.id, name: providerData.name };
    } else {
      return { id: provider.id, name: provider.name };
    }
  })
  .filter((provider) => provider.id !== "google-one-tap");

export const authOptions: NextAuthConfig = {
  providers,
  pages: {
    signIn: "/auth/signin",
  },
  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      const isAllowedToSignIn = true;
      if (isAllowedToSignIn) {
        return true;
      } else {
        // Return false to display a default error message
        return false;
        // Or you can return a URL to redirect to:
        // return '/unauthorized'
      }
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
    async session({ session, token, user }) {
      console.log("🔧 Session Callback - Input:", {
        hasSession: !!session,
        hasToken: !!token,
        tokenUser: token?.user ? "exists" : "none",
        sessionUserBefore: session?.user?.email || "none"
      });

      if (token && token.user) {
        session.user = {
          ...session.user,
          ...(token.user as any),
          id: (token.user as any).uuid, // 确保id字段存在
        };
        console.log("🔧 Session Callback - User set:", {
          id: session.user.id,
          email: session.user.email,
          uuid: (session.user as any).uuid
        });
      }

      console.log("🔧 Session Callback - Final session user:", session?.user?.email || "none");
      return session;
    },
    async jwt({ token, user, account }) {
      // Persist the OAuth access_token and or the user id to the token right after signin
      console.log("🔧 JWT Callback - Input:", {
        hasUser: !!user,
        userEmail: user?.email,
        hasAccount: !!account,
        provider: account?.provider,
        hasToken: !!token,
        tokenUser: token.user ? "exists" : "none"
      });

      try {
        if (user && user.email) {
          // For credentials provider, user already exists in DB
          if (!account || account.provider === "credentials") {
            console.log("🔧 JWT Callback - Credentials provider");
            const existingUser = await findUserByEmail(user.email);
            if (existingUser) {
              token.user = {
                uuid: existingUser.uuid,
                email: existingUser.email,
                nickname: existingUser.nickname,
                avatar_url: existingUser.avatar_url,
                created_at: existingUser.created_at,
              };
              console.log("🔧 JWT Callback - Credentials user set:", (token.user as any).uuid);
            }
          } else {
            // For OAuth providers, save or update user
            console.log("🔧 JWT Callback - OAuth provider:", account.provider);
            let clientIp = "127.0.0.1";
            try {
              clientIp = await getClientIp();
            } catch (error) {
              console.warn("Failed to get client IP, using default:", error);
            }

            const dbUser: User = {
              uuid: getUuid(),
              email: user.email,
              nickname: user.name || "",
              avatar_url: user.image || "",
              signin_type: account.type,
              signin_provider: account.provider,
              signin_openid: account.providerAccountId,
              created_at: getIsoTimestr(),
              signin_ip: clientIp,
            };

            console.log("🔧 JWT Callback - Creating user:", {
              uuid: dbUser.uuid,
              email: dbUser.email,
              nickname: dbUser.nickname
            });

            try {
              const savedUser = await saveUser(dbUser);
              console.log("🔧 JWT Callback - User saved:", {
                uuid: savedUser.uuid,
                email: savedUser.email
              });

              token.user = {
                uuid: savedUser.uuid,
                email: savedUser.email,
                nickname: savedUser.nickname,
                avatar_url: savedUser.avatar_url,
                created_at: savedUser.created_at,
              };
              console.log("🔧 JWT Callback - Token user set:", (token.user as any).uuid);
            } catch (e) {
              console.error("🔧 JWT Callback - Save user failed:", e);
            }
          }
        }

        console.log("🔧 JWT Callback - Final token user:", token.user ? (token.user as any).uuid : "none");
        return token;
      } catch (e) {
        console.error("🔧 JWT Callback - Error:", e);
        return token;
      }
    },
  },
};

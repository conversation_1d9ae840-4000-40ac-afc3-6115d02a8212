import { FAQItem, FAQCategory } from '@/types/faq';
import { createClient } from '@supabase/supabase-js';

// Supabase客户端
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// 模拟FAQ数据（作为备用）
const MOCK_FAQ_DATA: FAQItem[] = [
  {
    id: "stilltasty_faq_549823",
    question: "Is it Safe to Leave Canned Food Leftovers in the Can?",
    answer: "From a safety standpoint, the answer is yes. The United States Department of Agriculture says it's safe to refrigerate canned foods manufactured in the United States directly in the can. That said, the USDA still doesn't advise it. The reason is that canned foods will better retain their flavor and appearance if you transfer them to glass or plastic storage containers after opening. As detailed here, you may store the opened chicken broth in the refrigerator for 4 to 5 days, or freeze it for longer-term storage.",
    answer_summary: "From a safety standpoint, the answer is yes. The United States Department of Agriculture says it's safe to refrigerate canned foods manufactured in the United States directly in the can. That said, th...",
    category: "refrigerated_foods",
    category_zh: "冷藏食品",
    category_icon: "🧊",
    related_foods: ["chicken", "broth", "canned food"],
    tags: ["safety", "storage", "refrigeration"],
    key_points: [
      "From a safety standpoint, the answer is yes",
      "The United States Department of Agriculture says it's safe to refrigerate canned foods manufactured in the United States directly in the can",
      "As detailed here, you may store the opened chicken broth in the refrigerator for 4 to 5 days, or freeze it for longer-term storage"
    ],
    source: {
      name: "StillTasty.com",
      url: "https://stilltasty.com/questions/index/22"
    }
  },
  {
    id: "stilltasty_faq_123456",
    question: "How Long Can You Keep a Thawed Turkey in the Fridge?",
    answer: "A thawed turkey can be kept in the refrigerator for 1 to 2 days before cooking. If you need to keep it longer, you can refreeze it, but there may be some loss of quality. Make sure your refrigerator is set to 40°F (4°C) or below to maintain food safety.",
    answer_summary: "A thawed turkey can be kept in the refrigerator for 1 to 2 days before cooking. If you need to keep it longer, you can refreeze it...",
    category: "refrigerated_foods",
    category_zh: "冷藏食品", 
    category_icon: "🧊",
    related_foods: ["turkey", "meat", "poultry"],
    tags: ["thawing", "refrigeration", "safety"],
    key_points: [
      "A thawed turkey can be kept in the refrigerator for 1 to 2 days",
      "Make sure your refrigerator is set to 40°F (4°C) or below",
      "You can refreeze it, but there may be some loss of quality"
    ],
    source: {
      name: "StillTasty.com",
      url: "https://stilltasty.com/questions/index/112"
    }
  },
  {
    id: "stilltasty_faq_789012",
    question: "Can You Freeze Cranberry Sauce?",
    answer: "Yes, you can freeze cranberry sauce. Homemade cranberry sauce can be frozen for up to 1 year, while store-bought cranberry sauce can be frozen for up to 2 months past its expiration date. To freeze, transfer the sauce to freezer-safe containers, leaving some space for expansion. Thaw in the refrigerator before serving.",
    answer_summary: "Yes, you can freeze cranberry sauce. Homemade cranberry sauce can be frozen for up to 1 year, while store-bought cranberry sauce can be frozen for up to 2 months...",
    category: "frozen_foods",
    category_zh: "冷冻食品",
    category_icon: "❄️",
    related_foods: ["cranberry", "sauce", "fruits"],
    tags: ["freezing", "storage", "preservation"],
    key_points: [
      "Homemade cranberry sauce can be frozen for up to 1 year",
      "Store-bought cranberry sauce can be frozen for up to 2 months past its expiration date",
      "Transfer the sauce to freezer-safe containers, leaving some space for expansion"
    ],
    source: {
      name: "StillTasty.com",
      url: "https://stilltasty.com/questions/can-you-freeze-cranberry-sauce"
    }
  },
  {
    id: "stilltasty_faq_345678",
    question: "Should You Rinse Raw Chicken Before Cooking It?",
    answer: "No, you should not rinse raw chicken before cooking it. The USDA and food safety experts strongly advise against this practice. Rinsing raw chicken can spread bacteria around your kitchen through splashing water droplets. Proper cooking to an internal temperature of 165°F (74°C) will kill any harmful bacteria present.",
    answer_summary: "No, you should not rinse raw chicken before cooking it. The USDA and food safety experts strongly advise against this practice...",
    category: "food_safety",
    category_zh: "食品安全",
    category_icon: "🛡️",
    related_foods: ["chicken", "poultry", "meat"],
    tags: ["safety", "preparation", "cooking"],
    key_points: [
      "The USDA and food safety experts strongly advise against this practice",
      "Rinsing raw chicken can spread bacteria around your kitchen through splashing water droplets",
      "Proper cooking to an internal temperature of 165°F (74°C) will kill any harmful bacteria present"
    ],
    source: {
      name: "StillTasty.com",
      url: "https://stilltasty.com/questions/should-you-rinse-a-raw-chicken-before-cooking-it"
    }
  },
  {
    id: "stilltasty_faq_901234",
    question: "Does Pure Honey Ever Go Bad?",
    answer: "Pure honey never goes bad when stored properly. Honey has natural antimicrobial properties and an extremely low moisture content that prevents bacterial growth. Archaeological evidence shows honey found in ancient Egyptian tombs was still edible after thousands of years. However, honey may crystallize over time, which is normal and doesn't indicate spoilage.",
    answer_summary: "Pure honey never goes bad when stored properly. Honey has natural antimicrobial properties and an extremely low moisture content that prevents bacterial growth...",
    category: "expiration_dates",
    category_zh: "保质期",
    category_icon: "📅",
    related_foods: ["honey", "sweetener"],
    tags: ["expiration", "storage", "preservation"],
    key_points: [
      "Pure honey never goes bad when stored properly",
      "Honey has natural antimicrobial properties and an extremely low moisture content",
      "Honey may crystallize over time, which is normal and doesn't indicate spoilage"
    ],
    source: {
      name: "StillTasty.com",
      url: "https://stilltasty.com/questions/does-pure-honey-ever-go-bad"
    }
  }
];

const FAQ_CATEGORIES: FAQCategory[] = [
  {
    name: "frozen_foods",
    name_zh: "冷冻食品",
    icon: "❄️",
    count: 1
  },
  {
    name: "refrigerated_foods", 
    name_zh: "冷藏食品",
    icon: "🧊",
    count: 2
  },
  {
    name: "food_safety",
    name_zh: "食品安全",
    icon: "🛡️",
    count: 1
  },
  {
    name: "expiration_dates",
    name_zh: "保质期", 
    icon: "📅",
    count: 1
  },
  {
    name: "room_temperature",
    name_zh: "常温保存",
    icon: "🌡️",
    count: 0
  },
  {
    name: "storage_tips",
    name_zh: "保存技巧",
    icon: "💡",
    count: 0
  },
  {
    name: "preparation",
    name_zh: "食品处理",
    icon: "👨‍🍳",
    count: 0
  },
  {
    name: "general",
    name_zh: "一般问题",
    icon: "❓",
    count: 0
  }
];

/**
 * 获取所有FAQ数据
 */
export async function getAllFAQs(): Promise<FAQItem[]> {
  // 暂时直接返回MOCK数据，确保只显示食品保鲜相关的FAQ
  return MOCK_FAQ_DATA;

  // 注释掉数据库查询，避免显示不相关的FAQ
  /*
  try {
    const { data, error } = await supabase
      .from('faqs')
      .select(`
        id,
        question,
        answer,
        slug,
        sort_order,
        is_active,
        view_count,
        is_featured,
        created_at,
        updated_at,
        faq_categories (
          name,
          slug,
          description
        )
      `)
      .eq('is_active', true)
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('获取FAQ数据失败:', error);
      return MOCK_FAQ_DATA; // 返回备用数据
    }

    // 转换数据格式
    const faqs: FAQItem[] = data.map(item => ({
      id: item.id.toString(),
      question: item.question,
      answer: item.answer,
      answer_summary: '', // 简化版本不包含摘要
      category: item.faq_categories?.slug || 'general',
      category_zh: item.faq_categories?.name || '一般问题',
      category_icon: '❓', // 简化版本使用默认图标
      related_foods: [], // 简化版本不包含相关食物
      tags: [], // 简化版本不包含标签
      key_points: [], // 简化版本不包含关键点
      word_count: item.answer.length || 0,
      view_count: item.view_count || 0,
      helpful_count: 0, // 简化版本不包含有用计数
      source: {
        name: 'HowLongFresh',
        url: '',
        confidence: 0.95,
        processed_at: item.created_at
      },
      created_at: item.created_at,
      updated_at: item.updated_at
    }));

    return faqs;
  } catch (error) {
    console.error('获取FAQ数据失败:', error);
    return MOCK_FAQ_DATA; // 返回备用数据
  }
  */
}

/**
 * 获取FAQ分类数据
 */
export async function getFAQCategories(): Promise<FAQCategory[]> {
  try {
    // 使用我们创建的函数获取分类和计数
    const { data, error } = await supabase
      .rpc('get_category_counts');

    if (error) {
      console.error('获取FAQ分类失败:', error);
      return FAQ_CATEGORIES;
    }

    // 转换数据格式
    const categories: FAQCategory[] = data.map((category: any) => ({
      name: category.slug,
      name_zh: category.name,
      icon: '❓', // 简化版本使用默认图标
      count: parseInt(category.faq_count) || 0,
      priority: category.sort_order || 0,
      description: category.description
    }));

    return categories;
  } catch (error) {
    console.error('获取FAQ分类失败:', error);
    return FAQ_CATEGORIES;
  }
}

/**
 * 根据分类获取FAQ
 */
export async function getFAQsByCategory(category: string): Promise<FAQItem[]> {
  try {
    const allFAQs = await getAllFAQs();
    return allFAQs.filter(faq => faq.category === category);
  } catch (error) {
    console.error('根据分类获取FAQ失败:', error);
    return [];
  }
}

/**
 * 搜索FAQ
 */
export async function searchFAQs(query: string): Promise<FAQItem[]> {
  try {
    const allFAQs = await getAllFAQs();
    const searchTerm = query.toLowerCase();
    
    return allFAQs.filter(faq => 
      faq.question.toLowerCase().includes(searchTerm) ||
      faq.answer.toLowerCase().includes(searchTerm) ||
      faq.related_foods.some(food => food.toLowerCase().includes(searchTerm)) ||
      faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  } catch (error) {
    console.error('搜索FAQ失败:', error);
    return [];
  }
}

/**
 * 获取热门FAQ（按查看次数排序）
 */
export async function getPopularFAQs(limit: number = 5): Promise<FAQItem[]> {
  try {
    const allFAQs = await getAllFAQs();
    // 在实际项目中，这里应该根据view_count排序
    // 现在返回前几个作为示例
    return allFAQs.slice(0, limit);
  } catch (error) {
    console.error('获取热门FAQ失败:', error);
    return [];
  }
}

/**
 * 获取相关FAQ
 */
export async function getRelatedFAQs(faqId: string, limit: number = 3): Promise<FAQItem[]> {
  try {
    const allFAQs = await getAllFAQs();
    const targetFAQ = allFAQs.find(faq => faq.id === faqId);
    
    if (!targetFAQ) {
      return [];
    }
    
    // 简单的相关性算法：相同分类或相同标签的FAQ
    const relatedFAQs = allFAQs
      .filter(faq => faq.id !== faqId)
      .filter(faq => 
        faq.category === targetFAQ.category ||
        faq.tags.some(tag => targetFAQ.tags.includes(tag)) ||
        faq.related_foods.some(food => targetFAQ.related_foods.includes(food))
      )
      .slice(0, limit);
    
    return relatedFAQs;
  } catch (error) {
    console.error('获取相关FAQ失败:', error);
    return [];
  }
}

/**
 * 从处理后的JSON文件加载FAQ数据
 */
export async function loadFAQsFromFile(): Promise<FAQItem[]> {
  try {
    // 在实际项目中，这里可以从静态文件或API加载数据
    // 例如：从 /data/processed_faq_data.json 加载
    
    // 这里返回模拟数据，实际项目中应该替换为真实的数据加载逻辑
    return MOCK_FAQ_DATA;
  } catch (error) {
    console.error('从文件加载FAQ数据失败:', error);
    return [];
  }
}

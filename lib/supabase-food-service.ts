import { createClient } from '@supabase/supabase-js';
import { FoodResult } from '@/types/food';
import { mapCategoryToSlug } from '@/lib/food-categories';

// Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// 创建 Supabase 客户端
const supabase = createClient(supabaseUrl, supabaseKey);

// 数据库类型定义
interface FoodRecord {
  id: number;
  name: string;
  category_name: string;
  refrigerated_days: number | null;
  frozen_days: number | null;
  room_temperature_days: number | null;
  storage_tips: string[];
  source: string;
  confidence: number;
  match_type: string;
}

/**
 * 搜索食物信息 - 使用迁移数据
 * @param searchTerm 搜索词
 * @param language 语言 ('en', 'zh', 'all')
 * @returns 食物信息或 null
 */
export async function searchFood(
  searchTerm: string,
  language: string = 'all'
): Promise<FoodResult | null> {
  try {
    const trimmedTerm = searchTerm.trim().toLowerCase();

    // 首先在食物名称中搜索
    let { data, error } = await supabase
      .from('foods')
      .select(`
        id,
        name,
        search_key,
        refrigerated_days,
        frozen_days,
        room_temperature_days,
        storage_tips,
        source,
        confidence,
        food_categories(
          id,
          name,
          description
        )
      `)
      .or(`name.ilike.%${trimmedTerm}%,search_key.ilike.%${trimmedTerm}%`)
      .limit(1);

    // 如果没找到，在别名中搜索
    if (!data || data.length === 0) {
      const { data: aliasData, error: aliasError } = await supabase
        .from('food_aliases')
        .select(`
          food_id,
          foods(
            id,
            name,
            search_key,
            refrigerated_days,
            frozen_days,
            room_temperature_days,
            storage_tips,
            source,
            confidence,
            food_categories(
              id,
              name,
              description
            )
          )
        `)
        .ilike('alias', `%${trimmedTerm}%`)
        .limit(1);

      if (aliasError) {
        console.error('Alias search error:', aliasError);
        return null;
      }

      if (aliasData && aliasData.length > 0) {
        data = aliasData[0].foods;
      }
    }

    if (error) {
      console.error('Food search error:', error);
      return null;
    }

    if (!data || (Array.isArray(data) && data.length === 0)) {
      return null;
    }

    const food: any = Array.isArray(data) ? data[0] : data;

    // 转换为 FoodResult 格式
    const result: FoodResult = {
      name: food.name,
      category: food.food_categories?.name || 'Unknown',
      storage: {
        refrigerated: food.refrigerated_days || 0,
        frozen: food.frozen_days || 0,
        room_temperature: food.room_temperature_days || 0
      },
      tips: food.storage_tips || [],
      confidence: food.confidence || 0.95,
      source: food.source === 'USDA' ? 'USDA' : 'LOCAL',
      isUSDAData: food.source === 'USDA'
    };

    return result;

  } catch (error) {
    console.error('Food search error:', error);
    return null;
  }
}

/**
 * 获取所有食物类别
 */
export async function getFoodCategories() {
  try {
    const { data, error } = await supabase
      .from('food_categories')
      .select('id, name, description')
      .order('name');

    if (error) {
      console.error('Get categories error:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Get categories error:', error);
    return [];
  }
}

/**
 * 根据类别ID获取食物列表
 */
export async function getFoodsByCategoryId(categoryId: number, limit: number = 20) {
  try {
    const { data, error } = await supabase
      .from('foods')
      .select(`
        id,
        name,
        search_key,
        refrigerated_days,
        frozen_days,
        room_temperature_days,
        storage_tips,
        source,
        confidence,
        food_categories(name)
      `)
      .eq('category_id', categoryId)
      .limit(limit)
      .order('name');

    if (error) {
      console.error('Get foods by category error:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Get foods by category error:', error);
    return [];
  }
}

/**
 * 添加新的食物记录
 */
export async function addFood(foodData: {
  name: string;
  categoryId: number;
  refrigeratedDays?: number;
  frozenDays?: number;
  roomTemperatureDays?: number;
  storageTips?: string[];
  source?: string;
  confidence?: number;
}) {
  try {
    const { data, error } = await supabase
      .from('foods')
      .insert({
        name: foodData.name,
        search_key: foodData.name.toLowerCase().replace(/\s+/g, '_'),
        category_id: foodData.categoryId,
        refrigerated_days: foodData.refrigeratedDays,
        frozen_days: foodData.frozenDays,
        room_temperature_days: foodData.roomTemperatureDays,
        storage_tips: foodData.storageTips || [],
        source: foodData.source || 'LOCAL',
        confidence: foodData.confidence || 0.90
      })
      .select()
      .single();

    if (error) {
      console.error('Add food error:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Add food error:', error);
    return null;
  }
}

/**
 * 添加食物别名
 */
export async function addFoodAlias(
  foodId: number, 
  alias: string, 
  language: string = 'en'
) {
  try {
    const { data, error } = await supabase
      .from('food_aliases')
      .insert({
        food_id: foodId,
        alias: alias,
        language: language,
        alias_type: 'name'
      })
      .select()
      .single();

    if (error) {
      console.error('Add alias error:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Add alias error:', error);
    return null;
  }
}

/**
 * 获取食物统计信息
 */
export async function getFoodStats() {
  try {
    // 获取总食物数量
    const { count: totalFoods } = await supabase
      .from('foods')
      .select('*', { count: 'exact', head: true });

    // 获取各来源的数量
    const { data: sourceStats } = await supabase
      .from('foods')
      .select('source')
      .then(({ data }) => {
        const stats = data?.reduce((acc, item) => {
          acc[item.source] = (acc[item.source] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {};
        return { data: stats };
      });

    // 获取类别统计
    const { data: categoryStats } = await supabase
      .from('food_categories')
      .select(`
        name,
        foods(count)
      `);

    return {
      totalFoods: totalFoods || 0,
      sourceStats: sourceStats || {},
      categoryStats: categoryStats || []
    };
  } catch (error) {
    console.error('Get stats error:', error);
    return {
      totalFoods: 0,
      sourceStats: {},
      categoryStats: []
    };
  }
}

/**
 * 根据分类slug获取食物列表（支持分页）- 使用迁移数据
 */
export async function getFoodsByCategory(
  categorySlug: string,
  page: number = 1,
  pageSize: number = 24,
  sortBy: string = 'name',
  searchQuery: string = ''
): Promise<{
  foods: FoodResult[];
  total: number;
  hasMore: boolean;
}> {
  try {
    // 导入映射函数
    const { mapSlugToDbCategories } = await import('@/lib/migrated-category-mapping');

    const offset = (page - 1) * pageSize;
    const categoryIds = mapSlugToDbCategories(categorySlug);

    if (categoryIds.length === 0) {
      return { foods: [], total: 0, hasMore: false };
    }

    // 构建排序字段
    let orderField = 'name';
    let ascending = true;

    switch (sortBy) {
      case 'refrigerated_days':
        orderField = 'refrigerated_days';
        ascending = false; // 天数多的在前
        break;
      case 'frozen_days':
        orderField = 'frozen_days';
        ascending = false;
        break;
      case 'popularity':
        orderField = 'confidence';
        ascending = false;
        break;
      default:
        orderField = 'name';
        ascending = true;
    }

    // 构建查询
    let query = supabase
      .from('foods')
      .select(`
        id,
        name,
        search_key,
        refrigerated_days,
        frozen_days,
        room_temperature_days,
        storage_tips,
        source,
        confidence,
        food_categories(
          id,
          name,
          description
        )
      `)
      .in('category_id', categoryIds);

    // 添加搜索条件
    if (searchQuery) {
      query = query.or(`name.ilike.%${searchQuery}%,search_key.ilike.%${searchQuery}%`);
    }

    // 获取食物数据
    const { data, error } = await query
      .order(orderField, { ascending })
      .range(offset, offset + pageSize - 1);

    if (error) {
      console.error('Get foods by category error:', error);
      return { foods: [], total: 0, hasMore: false };
    }

    // 获取总数
    let countQuery = supabase
      .from('foods')
      .select('*', { count: 'exact', head: true })
      .in('category_id', categoryIds);
    
    // 添加搜索条件到计数查询
    if (searchQuery) {
      countQuery = countQuery.or(`name.ilike.%${searchQuery}%,search_key.ilike.%${searchQuery}%`);
    }
    
    const { count: total } = await countQuery;

    // 转换数据格式
    const foods: FoodResult[] = (data || []).map((food: any) => ({
      name: food.name,
      category: food.food_categories?.name || 'Unknown',
      storage: {
        refrigerated: food.refrigerated_days || 0,
        frozen: food.frozen_days || 0,
        room_temperature: food.room_temperature_days || 0
      },
      tips: food.storage_tips || [],
      confidence: food.confidence || 0.95,
      source: food.source === 'USDA' ? 'USDA' : 'LOCAL',
      isUSDAData: food.source === 'USDA',
      is_usda_verified: food.source === 'USDA'
    }));

    const hasMore = (page * pageSize) < (total || 0);

    return {
      foods,
      total: total || 0,
      hasMore
    };

  } catch (error) {
    console.error('Get foods by category error:', error);
    return { foods: [], total: 0, hasMore: false };
  }
}

/**
 * 获取各分类的食物数量统计 - 使用迁移数据
 */
export async function getCategoryCounts(): Promise<Record<string, number>> {
  try {
    // 导入映射函数
    const { aggregateCategoryCountsForUI } = await import('@/lib/migrated-category-mapping');

    // 获取数据库分类的统计
    const { data, error } = await supabase
      .from('food_categories')
      .select(`
        id,
        foods(count)
      `);

    if (error) {
      console.error('Get category counts error:', error);
      return {};
    }

    // 构建数据库分类ID到数量的映射
    const dbCategoryCounts: Record<number, number> = {};
    data?.forEach((category: any) => {
      dbCategoryCounts[category.id] = category.foods?.[0]?.count || 0;
    });

    // 聚合到UI分类
    const uiCounts = aggregateCategoryCountsForUI(dbCategoryCounts);

    return uiCounts;
  } catch (error) {
    console.error('Get category counts error:', error);
    return {};
  }
}

/**
 * 搜索建议（自动完成）- 使用迁移数据
 */
export async function getFoodSuggestions(
  query: string,
  limit: number = 10
): Promise<string[]> {
  try {
    if (query.length < 2) return [];

    // 从食物名称获取建议
    const { data: foodData } = await supabase
      .from('foods')
      .select('name')
      .ilike('name', `${query}%`)
      .limit(limit);

    // 从别名获取建议
    const { data: aliasData } = await supabase
      .from('food_aliases')
      .select('alias')
      .ilike('alias', `${query}%`)
      .limit(limit);

    const suggestions = [
      ...(foodData?.map(item => item.name) || []),
      ...(aliasData?.map(item => item.alias) || [])
    ];

    // 去重并限制数量
    return [...new Set(suggestions)].slice(0, limit);

  } catch (error) {
    console.error('Get suggestions error:', error);
    return [];
  }
}

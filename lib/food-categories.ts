export interface FoodCategory {
  id: string;
  slug: string;
  name: {
    en: string;
    zh: string;
  };
  description: {
    en: string;
    zh: string;
  };
  icon: string;
  emoji: string;
  color: string;
  bgColor: string;
  count?: number; // 该分类下的食物数量
}

export const FOOD_CATEGORIES: FoodCategory[] = [
  {
    id: 'fruits',
    slug: 'fruits',
    name: {
      en: 'Fruits',
      zh: '水果'
    },
    description: {
      en: 'Fresh fruits and their storage guidelines',
      zh: '新鲜水果及其保存指南'
    },
    icon: '🍎',
    emoji: '🍎',
    color: 'text-gray-900 dark:text-white',
    bgColor: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
  },
  {
    id: 'vegetables',
    slug: 'vegetables',
    name: {
      en: 'Vegetables',
      zh: '蔬菜'
    },
    description: {
      en: 'Fresh vegetables and leafy greens',
      zh: '新鲜蔬菜和绿叶菜'
    },
    icon: '🥬',
    emoji: '🥬',
    color: 'text-gray-900 dark:text-white',
    bgColor: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
  },
  {
    id: 'meat',
    slug: 'meat',
    name: {
      en: 'Meat & Poultry',
      zh: '肉类'
    },
    description: {
      en: 'Fresh and cooked meat products',
      zh: '新鲜和熟制肉类产品'
    },
    icon: '🥩',
    emoji: '🥩',
    color: 'text-gray-900 dark:text-white',
    bgColor: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
  },
  {
    id: 'seafood',
    slug: 'seafood',
    name: {
      en: 'Seafood',
      zh: '海鲜'
    },
    description: {
      en: 'Fish, shellfish and marine products',
      zh: '鱼类、贝类和海洋产品'
    },
    icon: '🐟',
    emoji: '🐟',
    color: 'text-gray-900 dark:text-white',
    bgColor: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
  },
  {
    id: 'dairy',
    slug: 'dairy',
    name: {
      en: 'Dairy & Eggs',
      zh: '奶制品'
    },
    description: {
      en: 'Milk, cheese, yogurt and egg products',
      zh: '牛奶、奶酪、酸奶和蛋类产品'
    },
    icon: '🥛',
    emoji: '🥛',
    color: 'text-gray-900 dark:text-white',
    bgColor: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
  },
  {
    id: 'grains',
    slug: 'grains',
    name: {
      en: 'Grains & Bread',
      zh: '谷物'
    },
    description: {
      en: 'Rice, pasta, bread and grain products',
      zh: '米饭、面食、面包和谷物产品'
    },
    icon: '🍞',
    emoji: '🍞',
    color: 'text-gray-900 dark:text-white',
    bgColor: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
  },
  {
    id: 'beverages',
    slug: 'beverages',
    name: {
      en: 'Beverages',
      zh: '饮料'
    },
    description: {
      en: 'Juices, soft drinks and other beverages',
      zh: '果汁、软饮料和其他饮品'
    },
    icon: '🧃',
    emoji: '🧃',
    color: 'text-gray-900 dark:text-white',
    bgColor: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
  },
  {
    id: 'snacks',
    slug: 'snacks',
    name: {
      en: 'Snacks & Sweets',
      zh: '零食'
    },
    description: {
      en: 'Cookies, candies and snack foods',
      zh: '饼干、糖果和零食'
    },
    icon: '🍪',
    emoji: '🍪',
    color: 'text-gray-900 dark:text-white',
    bgColor: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
  },
  {
    id: 'condiments',
    slug: 'condiments',
    name: {
      en: 'Condiments & Sauces',
      zh: '调料'
    },
    description: {
      en: 'Sauces, dressings and condiments',
      zh: '酱料、调味品和调料'
    },
    icon: '🍯',
    emoji: '🍯',
    color: 'text-gray-900 dark:text-white',
    bgColor: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
  },
  {
    id: 'spices',
    slug: 'spices',
    name: {
      en: 'Herbs & Spices',
      zh: '香料'
    },
    description: {
      en: 'Dried herbs, spices and seasonings',
      zh: '干草药、香料和调味料'
    },
    icon: '🌿',
    emoji: '🌿',
    color: 'text-gray-900 dark:text-white',
    bgColor: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
  }
];

// 根据分类slug获取分类信息
export function getCategoryBySlug(slug: string): FoodCategory | undefined {
  return FOOD_CATEGORIES.find(category => category.slug === slug);
}

// 根据分类ID获取分类信息
export function getCategoryById(id: string): FoodCategory | undefined {
  return FOOD_CATEGORIES.find(category => category.id === id);
}

// 获取所有分类的slug列表（用于静态生成）
export function getAllCategorySlugs(): string[] {
  return FOOD_CATEGORIES.map(category => category.slug);
}

// 分类映射：将现有数据库中的分类映射到新的分类系统
export const CATEGORY_MAPPING: Record<string, string> = {
  // 水果类
  'Produce': 'fruits',
  'Fruit': 'fruits',
  'Fruits': 'fruits',

  // 蔬菜类
  'Vegetables': 'vegetables',
  'Vegetable': 'vegetables',
  'Leafy Greens': 'vegetables',

  // 肉类
  'Meat': 'meat',
  'Poultry': 'meat',
  'Beef': 'meat',
  'Pork': 'meat',
  'Chicken': 'meat',

  // 海鲜
  'Seafood': 'seafood',
  'Fish': 'seafood',
  'Shellfish': 'seafood',

  // 乳制品
  'Dairy': 'dairy',
  'Milk': 'dairy',
  'Cheese': 'dairy',
  'Eggs': 'dairy',

  // 谷物
  'Grains': 'grains',
  'Bread': 'grains',
  'Rice': 'grains',
  'Pasta': 'grains',

  // 饮料
  'Beverages': 'beverages',
  'Drinks': 'beverages',
  'Juice': 'beverages',

  // 零食
  'Snacks': 'snacks',
  'Sweets': 'snacks',
  'Desserts': 'snacks',

  // 调料
  'Condiments': 'condiments',
  'Sauces': 'condiments',
  'Dressings': 'condiments',

  // 香料
  'Spices': 'spices',
  'Herbs': 'spices',
  'Herbs & Spices': 'spices',
  'Seasonings': 'spices',

  // 默认分类
  'Unknown': 'snacks',
  'Other': 'snacks'
};

// 根据原始分类名称获取新的分类slug
export function mapCategoryToSlug(originalCategory: string): string {
  return CATEGORY_MAPPING[originalCategory] || 'leftovers';
}

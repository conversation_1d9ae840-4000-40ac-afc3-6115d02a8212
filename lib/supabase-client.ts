/**
 * 简化的 Supabase 客户端
 * 使用原生 fetch API，无需额外依赖
 */

import { FoodResult } from '@/types/food';

// Supabase 配置
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// 数据库查询结果类型
interface SupabaseFoodResult {
  id: number;
  name: string;
  category_name: string;
  refrigerated_days: number | null;
  frozen_days: number | null;
  room_temperature_days: number | null;
  storage_tips: string[];
  source: string;
  confidence: string;
  match_type: string;
}

/**
 * 创建 Supabase 请求头
 */
function createHeaders(): HeadersInit {
  return {
    'apikey': SUPABASE_ANON_KEY,
    'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
    'Content-Type': 'application/json',
    'Prefer': 'return=representation'
  };
}

/**
 * 执行 Supabase RPC 调用
 */
async function callSupabaseRPC(
  functionName: string, 
  params: Record<string, any>
): Promise<any> {
  try {
    const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/${functionName}`, {
      method: 'POST',
      headers: createHeaders(),
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      console.error(`Supabase RPC error: ${response.status} ${response.statusText}`);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Supabase RPC call failed:', error);
    return null;
  }
}

/**
 * 执行 Supabase 查询
 */
async function querySupabase(
  table: string,
  options: {
    select?: string;
    filters?: Record<string, any>;
    limit?: number;
  } = {}
): Promise<any> {
  try {
    let url = `${SUPABASE_URL}/rest/v1/${table}`;
    
    // 添加 select 参数
    if (options.select) {
      url += `?select=${encodeURIComponent(options.select)}`;
    } else {
      url += '?select=*';
    }
    
    // 添加过滤器
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        url += `&${key}=eq.${encodeURIComponent(value)}`;
      });
    }
    
    // 添加限制
    if (options.limit) {
      url += `&limit=${options.limit}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: createHeaders()
    });

    if (!response.ok) {
      console.error(`Supabase query error: ${response.status} ${response.statusText}`);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Supabase query failed:', error);
    return null;
  }
}

/**
 * 转换 Supabase 结果为 FoodResult 格式
 */
function formatFoodResult(data: SupabaseFoodResult): FoodResult {
  return {
    name: data.name,
    category: data.category_name || 'Unknown',
    storage: {
      refrigerated: data.refrigerated_days || 0,
      frozen: data.frozen_days || 0,
      room_temperature: data.room_temperature_days || 0
    },
    tips: data.storage_tips || [],
    confidence: parseFloat(data.confidence) || 0.95
  };
}

/**
 * 搜索食物信息
 * @param searchTerm 搜索词
 * @param language 语言 ('en', 'zh', 'all')
 * @returns 食物信息或 null
 */
export async function searchFood(
  searchTerm: string, 
  language: string = 'all'
): Promise<FoodResult | null> {
  try {
    // 调用 Supabase 搜索函数
    const results = await callSupabaseRPC('search_foods', {
      search_term: searchTerm.trim(),
      lang: language
    });

    if (!results || results.length === 0) {
      return null;
    }

    // 取第一个匹配结果
    const food: SupabaseFoodResult = results[0];
    return formatFoodResult(food);

  } catch (error) {
    console.error('Food search error:', error);
    return null;
  }
}

/**
 * 获取所有食物类别
 */
export async function getFoodCategories() {
  try {
    const data = await querySupabase('food_categories', {
      select: 'id,name,description',
      limit: 20
    });

    return data || [];
  } catch (error) {
    console.error('Get categories error:', error);
    return [];
  }
}

/**
 * 根据类别获取食物列表
 */
export async function getFoodsByCategory(categoryId: number, limit: number = 20) {
  try {
    const data = await querySupabase('foods', {
      select: `
        id,name,search_key,
        refrigerated_days,frozen_days,room_temperature_days,
        storage_tips,source,confidence,
        food_categories(name)
      `,
      filters: { category_id: categoryId },
      limit
    });

    return data || [];
  } catch (error) {
    console.error('Get foods by category error:', error);
    return [];
  }
}

/**
 * 获取食物统计信息
 */
export async function getFoodStats() {
  try {
    // 获取总食物数量
    const totalResult = await querySupabase('foods', {
      select: 'count'
    });

    // 获取各来源的数量
    const sourceResult = await querySupabase('foods', {
      select: 'source'
    });

    // 获取类别统计
    const categoryResult = await querySupabase('food_categories', {
      select: 'name'
    });

    const totalFoods = totalResult?.length || 0;
    const sourceStats = sourceResult?.reduce((acc: Record<string, number>, item: any) => {
      acc[item.source] = (acc[item.source] || 0) + 1;
      return acc;
    }, {}) || {};

    return {
      totalFoods,
      sourceStats,
      categoryStats: categoryResult || []
    };
  } catch (error) {
    console.error('Get stats error:', error);
    return {
      totalFoods: 0,
      sourceStats: {},
      categoryStats: []
    };
  }
}

/**
 * 搜索建议（自动完成）
 */
export async function getFoodSuggestions(
  query: string, 
  limit: number = 10
): Promise<string[]> {
  try {
    if (query.length < 2) return [];

    const data = await querySupabase('food_aliases', {
      select: 'alias',
      limit
    });

    if (!data) return [];

    // 过滤匹配的别名
    return data
      .filter((item: any) => 
        item.alias.toLowerCase().includes(query.toLowerCase())
      )
      .map((item: any) => item.alias)
      .slice(0, limit);

  } catch (error) {
    console.error('Get suggestions error:', error);
    return [];
  }
}

/**
 * 测试 Supabase 连接
 */
export async function testSupabaseConnection(): Promise<boolean> {
  try {
    const result = await querySupabase('food_categories', {
      select: 'count',
      limit: 1
    });
    
    return result !== null;
  } catch (error) {
    console.error('Supabase connection test failed:', error);
    return false;
  }
}

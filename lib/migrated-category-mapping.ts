import { FoodCategory } from '@/lib/food-categories';

// 迁移数据库分类到UI分类的映射
// 基于实际数据库分类ID和数据分布
export const MIGRATED_CATEGORY_MAPPING: Record<number, string> = {
  1: 'fruits',        // Produce -> 水果 (235个)
  2: 'vegetables',    // Dairy Products & Eggs -> 蔬菜 (171个，实际包含蔬菜数据)
  3: 'meat',          // Meat -> 肉类 (212个)
  4: 'snacks',        // Poultry -> 零食 (106个，实际包含零食数据)
  5: 'seafood',       // Seafood -> 海鲜 (129个)
  6: 'dairy',         // Baked Goods -> 乳制品 (13个，实际包含乳制品数据)
  7: 'grains',        // Grains, Beans & Pasta -> 谷物 (219个)
  8: 'condiments',    // Condiments, Sauces & Canned Goods -> 调料 (107个)
  9: 'beverages',     // Beverages -> 饮料 (99个)
  10: 'snacks',       // Shelf Stable Foods -> 零食 (0个)
  11: 'snacks',       // Food Purchased Frozen -> 零食 (0个)
  12: 'spices',       // Deli & Prepared Foods -> 香料 (62个，实际包含香料数据)
  13: 'vegetables',   // Vegetarian Proteins -> 蔬菜 (1个)
  14: 'condiments',   // Asian Specialties -> 调料 (0个)
  15: 'fruits',       // Fruits -> 水果 (0个)
  16: 'vegetables',   // Vegetables -> 蔬菜 (0个)
  17: 'dairy',        // Dairy & Eggs -> 乳制品 (0个)
  18: 'meat'          // Meat & Poultry -> 肉类 (0个)
};

// 反向映射：UI分类到数据库分类ID
export const UI_TO_DB_CATEGORY_MAPPING: Record<string, number[]> = {
  'fruits': [1, 15],                // 水果
  'vegetables': [2, 13, 16],        // 蔬菜 (包含实际的蔬菜数据)
  'meat': [3, 18],                  // 肉类
  'seafood': [5],                   // 海鲜
  'dairy': [6, 17],                 // 乳制品
  'grains': [7],                    // 谷物
  'beverages': [9],                 // 饮料
  'snacks': [4, 10, 11],            // 零食
  'condiments': [8, 14],            // 调料
  'spices': [12],                   // 香料
};

/**
 * 将数据库分类ID映射到UI分类slug
 */
export function mapDbCategoryToSlug(categoryId: number): string {
  return MIGRATED_CATEGORY_MAPPING[categoryId] || 'leftovers';
}

/**
 * 将UI分类slug映射到数据库分类ID数组
 */
export function mapSlugToDbCategories(slug: string): number[] {
  return UI_TO_DB_CATEGORY_MAPPING[slug] || [];
}

/**
 * 获取UI分类的食物数量统计
 */
export function aggregateCategoryCountsForUI(dbCategoryCounts: Record<number, number>): Record<string, number> {
  const uiCounts: Record<string, number> = {};
  
  // 初始化所有UI分类的计数为0
  Object.keys(UI_TO_DB_CATEGORY_MAPPING).forEach(slug => {
    uiCounts[slug] = 0;
  });
  
  // 聚合数据库分类的计数到UI分类
  Object.entries(dbCategoryCounts).forEach(([categoryId, count]) => {
    const slug = mapDbCategoryToSlug(parseInt(categoryId));
    uiCounts[slug] = (uiCounts[slug] || 0) + count;
  });
  
  return uiCounts;
}

/**
 * 数据库分类名称到中文名称的映射
 */
export const DB_CATEGORY_NAMES: Record<number, { zh: string; en: string }> = {
  1: { zh: '农产品', en: 'Produce' },
  2: { zh: '乳制品和鸡蛋', en: 'Dairy & Eggs' },
  3: { zh: '肉类', en: 'Meat' },
  4: { zh: '家禽', en: 'Poultry' },
  5: { zh: '海鲜', en: 'Seafood' },
  6: { zh: '烘焙食品', en: 'Baked Goods' },
  7: { zh: '谷物、豆类和意大利面', en: 'Grains, Beans & Pasta' },
  8: { zh: '调料、酱料和罐装食品', en: 'Condiments, Sauces & Canned' },
  9: { zh: '饮料', en: 'Beverages' },
  10: { zh: '零食和甜点', en: 'Snacks & Sweets' },
  11: { zh: '蔬菜', en: 'Vegetables' },
  12: { zh: '熟食和预制食品', en: 'Prepared Foods' },
  13: { zh: '素食蛋白', en: 'Plant Proteins' },
  14: { zh: '油脂和调味品', en: 'Oils & Seasonings' },
  15: { zh: '坚果和种子', en: 'Nuts & Seeds' },
  16: { zh: '香料和草药', en: 'Spices & Herbs' },
  17: { zh: '酒精饮料', en: 'Alcoholic Beverages' },
  18: { zh: '其他', en: 'Others' }
};

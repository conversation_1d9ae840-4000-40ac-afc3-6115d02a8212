/**
 * 食物图片服务 - 提供无版权风险的食物图片
 * 使用 Unsplash API 和本地默认图片
 */

// Unsplash 免费图片集合，这些都是无版权风险的高质量食物图片
export const FOOD_IMAGE_COLLECTION = {
  // 水果类
  fruits: {
    apple: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=300&fit=crop&auto=format&q=80',
    banana: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400&h=300&fit=crop&auto=format&q=80',
    orange: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=400&h=300&fit=crop&auto=format&q=80',
    strawberry: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=400&h=300&fit=crop&auto=format&q=80',
    grape: 'https://images.unsplash.com/photo-1537640538966-79f369143f8f?w=400&h=300&fit=crop&auto=format&q=80',
    watermelon: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400&h=300&fit=crop&auto=format&q=80',
    pineapple: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400&h=300&fit=crop&auto=format&q=80',
    mango: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400&h=300&fit=crop&auto=format&q=80',
    avocado: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400&h=300&fit=crop&auto=format&q=80',
    lemon: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&auto=format&q=80'
  },
  
  // 蔬菜类
  vegetables: {
    tomato: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=400&h=300&fit=crop&auto=format&q=80',
    carrot: 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400&h=300&fit=crop&auto=format&q=80',
    lettuce: 'https://images.unsplash.com/photo-1622206151226-18ca2c9ab4a1?w=400&h=300&fit=crop&auto=format&q=80',
    broccoli: 'https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?w=400&h=300&fit=crop&auto=format&q=80',
    spinach: 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400&h=300&fit=crop&auto=format&q=80',
    cucumber: 'https://images.unsplash.com/photo-1449300079323-02e209d9d3a6?w=400&h=300&fit=crop&auto=format&q=80',
    onion: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=300&fit=crop&auto=format&q=80',
    potato: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=300&fit=crop&auto=format&q=80',
    bell_pepper: 'https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400&h=300&fit=crop&auto=format&q=80',
    cabbage: 'https://images.unsplash.com/photo-1594282486552-05b4d80fbb9f?w=400&h=300&fit=crop&auto=format&q=80'
  },
  
  // 肉类
  meat: {
    chicken: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400&h=300&fit=crop&auto=format&q=80',
    beef: 'https://images.unsplash.com/photo-1588347818133-38c4106ca7b8?w=400&h=300&fit=crop&auto=format&q=80',
    pork: 'https://images.unsplash.com/photo-1602470520998-f4a52199a3d6?w=400&h=300&fit=crop&auto=format&q=80',
    turkey: 'https://images.unsplash.com/photo-1574672280600-4accfa5b6f98?w=400&h=300&fit=crop&auto=format&q=80',
    lamb: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400&h=300&fit=crop&auto=format&q=80',
    sausage: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400&h=300&fit=crop&auto=format&q=80'
  },
  
  // 海鲜类
  seafood: {
    fish: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400&h=300&fit=crop&auto=format&q=80',
    salmon: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?w=400&h=300&fit=crop&auto=format&q=80',
    shrimp: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400&h=300&fit=crop&auto=format&q=80',
    crab: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=300&fit=crop&auto=format&q=80',
    lobster: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=300&fit=crop&auto=format&q=80',
    tuna: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400&h=300&fit=crop&auto=format&q=80'
  },
  
  // 乳制品
  dairy: {
    milk: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400&h=300&fit=crop&auto=format&q=80',
    cheese: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?w=400&h=300&fit=crop&auto=format&q=80',
    yogurt: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?w=400&h=300&fit=crop&auto=format&q=80',
    butter: 'https://images.unsplash.com/photo-1589985270826-4b7bb135bc9d?w=400&h=300&fit=crop&auto=format&q=80',
    cream: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400&h=300&fit=crop&auto=format&q=80',
    egg: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400&h=300&fit=crop&auto=format&q=80'
  },
  
  // 谷物面包
  grains: {
    bread: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop&auto=format&q=80',
    rice: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&auto=format&q=80',
    pasta: 'https://images.unsplash.com/photo-1551892374-ecf8754cf8b0?w=400&h=300&fit=crop&auto=format&q=80',
    cereal: 'https://images.unsplash.com/photo-1574085733277-851d9d856a3a?w=400&h=300&fit=crop&auto=format&q=80',
    oats: 'https://images.unsplash.com/photo-1574085733277-851d9d856a3a?w=400&h=300&fit=crop&auto=format&q=80',
    wheat: 'https://images.unsplash.com/photo-1574085733277-851d9d856a3a?w=400&h=300&fit=crop&auto=format&q=80'
  },
  
  // 饮料
  beverages: {
    juice: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?w=400&h=300&fit=crop&auto=format&q=80',
    coffee: 'https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=300&fit=crop&auto=format&q=80',
    tea: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop&auto=format&q=80',
    soda: 'https://images.unsplash.com/photo-1581636625402-29b2a704ef13?w=400&h=300&fit=crop&auto=format&q=80',
    water: 'https://images.unsplash.com/photo-1548839140-29a749e1cf4d?w=400&h=300&fit=crop&auto=format&q=80',
    wine: 'https://images.unsplash.com/photo-1510812431401-41d2bd2722f3?w=400&h=300&fit=crop&auto=format&q=80'
  },
  
  // 零食甜品
  snacks: {
    cookies: 'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?w=400&h=300&fit=crop&auto=format&q=80',
    chocolate: 'https://images.unsplash.com/photo-1511381939415-e44015466834?w=400&h=300&fit=crop&auto=format&q=80',
    candy: 'https://images.unsplash.com/photo-1582058091505-f87a2e55a40f?w=400&h=300&fit=crop&auto=format&q=80',
    chips: 'https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400&h=300&fit=crop&auto=format&q=80',
    nuts: 'https://images.unsplash.com/photo-1508747703725-719777637510?w=400&h=300&fit=crop&auto=format&q=80',
    cake: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop&auto=format&q=80'
  },
  
  // 剩菜剩饭
  leftovers: {
    cooked_rice: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&auto=format&q=80',
    pizza: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400&h=300&fit=crop&auto=format&q=80',
    soup: 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format&q=80',
    sandwich: 'https://images.unsplash.com/photo-1539252554453-80ab65ce3586?w=400&h=300&fit=crop&auto=format&q=80',
    salad: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format&q=80',
    default: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&auto=format&q=80'
  }
};

// 默认分类图片
export const DEFAULT_CATEGORY_IMAGES = {
  fruits: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=300&fit=crop&auto=format&q=80',
  vegetables: 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=400&h=300&fit=crop&auto=format&q=80',
  meat: 'https://images.unsplash.com/photo-1588347818133-38c4106ca7b8?w=400&h=300&fit=crop&auto=format&q=80',
  seafood: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400&h=300&fit=crop&auto=format&q=80',
  dairy: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400&h=300&fit=crop&auto=format&q=80',
  grains: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop&auto=format&q=80',
  beverages: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?w=400&h=300&fit=crop&auto=format&q=80',
  snacks: 'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?w=400&h=300&fit=crop&auto=format&q=80',
  leftovers: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&auto=format&q=80',
  default: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&auto=format&q=80'
};

/**
 * 根据食物名称获取合适的图片URL
 * @param foodName 食物名称
 * @param category 食物分类
 * @returns 图片URL
 */
export function getFoodImageUrl(foodName: string, category?: string): string {
  const lowerName = foodName.toLowerCase().replace(/\s+/g, '_');
  
  // 1. 尝试在所有分类中精确匹配食物名称
  for (const [categoryKey, foods] of Object.entries(FOOD_IMAGE_COLLECTION)) {
    if (foods[lowerName as keyof typeof foods]) {
      return foods[lowerName as keyof typeof foods];
    }
  }
  
  // 2. 尝试部分匹配
  for (const [categoryKey, foods] of Object.entries(FOOD_IMAGE_COLLECTION)) {
    for (const [foodKey, imageUrl] of Object.entries(foods)) {
      if (lowerName.includes(foodKey) || foodKey.includes(lowerName)) {
        return imageUrl;
      }
    }
  }
  
  // 3. 根据分类返回默认图片
  if (category) {
    const categoryKey = category.toLowerCase();
    if (DEFAULT_CATEGORY_IMAGES[categoryKey as keyof typeof DEFAULT_CATEGORY_IMAGES]) {
      return DEFAULT_CATEGORY_IMAGES[categoryKey as keyof typeof DEFAULT_CATEGORY_IMAGES];
    }
  }
  
  // 4. 返回通用默认图片
  return DEFAULT_CATEGORY_IMAGES.default;
}

/**
 * 根据分类获取默认图片
 * @param category 分类名称
 * @returns 图片URL
 */
export function getCategoryImageUrl(category: string): string {
  const categoryKey = category.toLowerCase();
  return DEFAULT_CATEGORY_IMAGES[categoryKey as keyof typeof DEFAULT_CATEGORY_IMAGES] || DEFAULT_CATEGORY_IMAGES.default;
}

/**
 * 预加载关键图片以提升用户体验
 */
export function preloadCriticalImages(): void {
  if (typeof window !== 'undefined') {
    // 预加载默认分类图片
    Object.values(DEFAULT_CATEGORY_IMAGES).forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = url;
      document.head.appendChild(link);
    });
  }
}

/**
 * 图片加载错误时的回退处理
 * @param event 错误事件
 */
export function handleImageError(event: Event): void {
  const img = event.target as HTMLImageElement;
  if (img && img.src !== DEFAULT_CATEGORY_IMAGES.default) {
    img.src = DEFAULT_CATEGORY_IMAGES.default;
  }
}

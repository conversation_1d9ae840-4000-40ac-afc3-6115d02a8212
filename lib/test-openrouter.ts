/**
 * 测试 OpenRouter API 连接的工具函数
 */

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * 测试 OpenRouter API 连接
 */
export async function testOpenRouterConnection(): Promise<TestResult> {
  try {
    const apiKey = process.env.OPENROUTER_API_KEY;
    const model = process.env.OPENROUTER_MODEL || 'google/gemini-flash-1.5';
    
    if (!apiKey) {
      return {
        success: false,
        message: 'OpenRouter API key not found in environment variables'
      };
    }

    // 测试简单的文本请求
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000',
        'X-Title': 'HowLongFresh - Food Storage Assistant'
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: 'Hello, can you identify food items in images?'
              }
            ]
          }
        ],
        max_tokens: 100,
        temperature: 0.3
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        message: `API request failed with status ${response.status}`,
        details: errorText
      };
    }

    const data = await response.json();
    
    if (!data.choices || data.choices.length === 0) {
      return {
        success: false,
        message: 'No response from AI model',
        details: data
      };
    }

    return {
      success: true,
      message: 'OpenRouter API connection successful',
      details: {
        model,
        response: data.choices[0].message.content
      }
    };

  } catch (error) {
    return {
      success: false,
      message: 'Connection test failed',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * 测试图像识别功能
 */
export async function testImageRecognition(base64Image: string): Promise<TestResult> {
  try {
    const apiKey = process.env.OPENROUTER_API_KEY;
    const model = process.env.OPENROUTER_MODEL || 'google/gemini-flash-1.5';
    
    if (!apiKey) {
      return {
        success: false,
        message: 'OpenRouter API key not found'
      };
    }

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000',
        'X-Title': 'HowLongFresh - Food Storage Assistant'
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: 'What food item do you see in this image? Please respond with just the name.'
              },
              {
                type: 'image_url',
                image_url: {
                  url: base64Image
                }
              }
            ]
          }
        ],
        max_tokens: 50,
        temperature: 0.3
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        message: `Image recognition test failed with status ${response.status}`,
        details: errorText
      };
    }

    const data = await response.json();
    
    if (!data.choices || data.choices.length === 0) {
      return {
        success: false,
        message: 'No response from AI model for image',
        details: data
      };
    }

    return {
      success: true,
      message: 'Image recognition test successful',
      details: {
        model,
        identified: data.choices[0].message.content
      }
    };

  } catch (error) {
    return {
      success: false,
      message: 'Image recognition test failed',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * AI 增强的食物查询服务
 * 结合 USDA 权威数据和大模型智能处理
 */

import { searchFood } from '@/lib/supabase-client';
import { FoodResult } from '@/types/food';

// OpenRouter 配置
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const OPENROUTER_MODEL = process.env.OPENROUTER_MODEL || 'google/gemini-flash-1.5';

/**
 * 调用大模型进行智能处理
 */
async function callAIModel(prompt: string): Promise<string> {
  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3001',
        'X-Title': 'HowLongFresh.site'
      },
      body: JSON.stringify({
        model: OPENROUTER_MODEL,
        messages: [
          {
            role: 'system',
            content: `你是一个专业的食物保鲜专家，基于美国农业部(USDA)的权威数据为用户提供准确的食物存储建议。

你的任务是：
1. 分析用户查询的食物名称
2. 基于提供的USDA数据库查询结果
3. 生成准确、实用、易懂的食物保鲜建议
4. 确保信息的科学性和权威性

输出格式要求：
- 使用JSON格式返回
- 包含食物名称、类别、存储时间、保鲜建议
- 语言要简洁明了，适合普通用户理解
- 优化食物名称，使其更符合用户查询习惯`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  } catch (error) {
    console.error('AI model call failed:', error);
    throw error;
  }
}

/**
 * 检测用户查询的语言
 */
function detectLanguage(query: string): 'zh' | 'en' {
  // 检测是否包含中文字符
  const chineseRegex = /[\u4e00-\u9fff]/;
  return chineseRegex.test(query) ? 'zh' : 'en';
}

/**
 * 构建给大模型的提示词
 */
function buildPrompt(userQuery: string, databaseResult: FoodResult): string {
  const userLanguage = detectLanguage(userQuery);
  const isChineseQuery = userLanguage === 'zh';

  const storage = [];
  if (databaseResult.storage.refrigerated > 0) {
    storage.push(isChineseQuery ? `冷藏: ${databaseResult.storage.refrigerated}天` : `Refrigerated: ${databaseResult.storage.refrigerated} days`);
  }
  if (databaseResult.storage.frozen > 0) {
    storage.push(isChineseQuery ? `冷冻: ${databaseResult.storage.frozen}天` : `Frozen: ${databaseResult.storage.frozen} days`);
  }
  if (databaseResult.storage.room_temperature > 0) {
    storage.push(isChineseQuery ? `常温: ${databaseResult.storage.room_temperature}天` : `Room temperature: ${databaseResult.storage.room_temperature} days`);
  }

  if (isChineseQuery) {
    return `
用户查询: "${userQuery}"

USDA数据库查询结果:
- 食物名称: ${databaseResult.name}
- 类别: ${databaseResult.category}
- 存储时间: ${storage.join(', ')}
- 保鲜建议: ${databaseResult.tips.join('; ')}
- 数据置信度: ${(databaseResult.confidence * 100).toFixed(0)}%

请基于以上USDA权威数据，为用户查询的食物"${userQuery}"提供最准确的保鲜建议。

要求：
1. 优化食物名称，使其更符合用户查询习惯（如果用户查询"苹果"，返回"苹果"而不是"Apples"）
2. 提供实用的存储建议和注意事项
3. 保持数据的科学性和权威性
4. 存储时间保持与USDA数据一致
5. 所有内容使用中文

请以以下JSON格式返回：
{
  "name": "优化后的食物名称（中文）",
  "category": "食物类别（中文）",
  "storage": {
    "refrigerated": ${databaseResult.storage.refrigerated},
    "frozen": ${databaseResult.storage.frozen},
    "room_temperature": ${databaseResult.storage.room_temperature}
  },
  "tips": ["实用的保鲜建议1（中文）", "实用的保鲜建议2（中文）"],
  "confidence": ${databaseResult.confidence}
}

注意：存储时间必须是纯数字，与USDA数据保持一致。`;
  } else {
    return `
User query: "${userQuery}"

USDA database result:
- Food name: ${databaseResult.name}
- Category: ${databaseResult.category}
- Storage time: ${storage.join(', ')}
- Storage tips: ${databaseResult.tips.join('; ')}
- Data confidence: ${(databaseResult.confidence * 100).toFixed(0)}%

Please provide the most accurate food storage advice for "${userQuery}" based on the above USDA authoritative data.

Requirements:
1. Optimize the food name to match user query habits (if user queries "cake", return "Cake" not "Apples")
2. Provide practical storage advice and precautions
3. Maintain scientific accuracy and authority
4. Keep storage times consistent with USDA data
5. All content should be in English

Please return in the following JSON format:
{
  "name": "Optimized food name (English)",
  "category": "Food category (English)",
  "storage": {
    "refrigerated": ${databaseResult.storage.refrigerated},
    "frozen": ${databaseResult.storage.frozen},
    "room_temperature": ${databaseResult.storage.room_temperature}
  },
  "tips": ["Practical storage tip 1 (English)", "Practical storage tip 2 (English)"],
  "confidence": ${databaseResult.confidence}
}

Note: Storage times must be pure numbers, consistent with USDA data.`;
  }
}

/**
 * 解析存储时间值，处理各种格式
 */
function parseStorageValue(value: any): number {
  if (typeof value === 'number') {
    return value;
  }

  if (typeof value === 'string') {
    // 提取数字部分
    const match = value.match(/(\d+)/);
    if (match) {
      const num = parseInt(match[1]);

      // 处理月份转换为天数
      if (value.includes('月') || value.includes('month')) {
        return num * 30; // 1个月 = 30天
      }

      return num;
    }
  }

  return 0;
}

/**
 * 解析大模型返回的JSON结果
 */
function parseAIResponse(aiResponse: string): FoodResult | null {
  try {
    // 尝试提取JSON部分
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in AI response');
    }

    let jsonStr = jsonMatch[0];

    // 清理可能的格式问题
    jsonStr = jsonStr.replace(/```json\s*/, '').replace(/```\s*$/, '');

    const parsed = JSON.parse(jsonStr);

    // 验证必要字段
    if (!parsed.name || !parsed.storage) {
      throw new Error('Invalid JSON structure');
    }

    return {
      name: parsed.name,
      category: parsed.category || 'Unknown',
      storage: {
        refrigerated: parseStorageValue(parsed.storage.refrigerated),
        frozen: parseStorageValue(parsed.storage.frozen),
        room_temperature: parseStorageValue(parsed.storage.room_temperature)
      },
      tips: Array.isArray(parsed.tips) ? parsed.tips : [],
      confidence: parsed.confidence || 0.95,
      source: 'USDA',
      isUSDAData: true
    };
  } catch (error) {
    console.error('Failed to parse AI response:', error);
    console.error('AI response:', aiResponse);
    return null;
  }
}

/**
 * 构建纯AI查询的提示词（当数据库中没有数据时）
 */
function buildPureAIPrompt(userQuery: string): string {
  const userLanguage = detectLanguage(userQuery);
  const isChineseQuery = userLanguage === 'zh';

  if (isChineseQuery) {
    return `
用户查询: "${userQuery}"

数据库状态: 未找到相关的USDA数据

请基于你的知识库，为用户查询的食物"${userQuery}"提供准确的保鲜建议。

要求：
1. 提供科学准确的存储时间建议
2. 优化食物名称，使其符合用户查询习惯
3. 提供实用的存储建议和注意事项
4. 如果是不常见的食物，请说明并提供最佳猜测
5. 置信度应该相对较低（0.7-0.8），因为不是基于USDA权威数据
6. 所有内容使用中文

请以以下JSON格式返回：
{
  "name": "优化后的食物名称（中文）",
  "category": "食物类别（中文）",
  "storage": {
    "refrigerated": 冷藏天数（纯数字，如7），
    "frozen": 冷冻天数（纯数字，如90），
    "room_temperature": 常温天数（纯数字，如3）
  },
  "tips": ["保鲜建议1（中文）", "保鲜建议2（中文）", "保鲜建议3（中文）"],
  "confidence": 0.75
}

注意：存储时间必须是纯数字，不要包含单位或范围，如果是月份请转换为天数（1个月=30天）。`;
  } else {
    return `
User query: "${userQuery}"

Database status: No relevant USDA data found

Please provide accurate food storage advice for "${userQuery}" based on your knowledge base.

Requirements:
1. Provide scientifically accurate storage time recommendations
2. Optimize food name to match user query habits
3. Provide practical storage advice and precautions
4. If it's an uncommon food, please explain and provide best estimate
5. Confidence should be relatively lower (0.7-0.8) since it's not based on USDA authoritative data
6. All content should be in English

Please return in the following JSON format:
{
  "name": "Optimized food name (English)",
  "category": "Food category (English)",
  "storage": {
    "refrigerated": refrigerated_days (pure number, e.g. 7),
    "frozen": frozen_days (pure number, e.g. 90),
    "room_temperature": room_temperature_days (pure number, e.g. 3)
  },
  "tips": ["Storage tip 1 (English)", "Storage tip 2 (English)", "Storage tip 3 (English)"],
  "confidence": 0.75
}

Note: Storage times must be pure numbers, no units or ranges. Convert months to days (1 month = 30 days).`;
  }
}

/**
 * 解析纯AI查询的JSON结果
 */
function parsePureAIResponse(aiResponse: string): FoodResult | null {
  try {
    // 尝试提取JSON部分
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in AI response');
    }

    let jsonStr = jsonMatch[0];

    // 清理可能的格式问题
    jsonStr = jsonStr.replace(/```json\s*/, '').replace(/```\s*$/, '');

    const parsed = JSON.parse(jsonStr);

    // 验证必要字段
    if (!parsed.name || !parsed.storage) {
      throw new Error('Invalid JSON structure');
    }

    return {
      name: parsed.name,
      category: parsed.category || 'Unknown',
      storage: {
        refrigerated: parseStorageValue(parsed.storage.refrigerated),
        frozen: parseStorageValue(parsed.storage.frozen),
        room_temperature: parseStorageValue(parsed.storage.room_temperature)
      },
      tips: Array.isArray(parsed.tips) ? parsed.tips : [],
      confidence: parsed.confidence || 0.75,
      source: 'AI',
      isUSDAData: false
    };
  } catch (error) {
    console.error('Failed to parse pure AI response:', error);
    console.error('AI response:', aiResponse);
    return null;
  }
}

/**
 * 纯AI查询（当数据库中没有数据时）
 */
async function pureAISearch(userQuery: string): Promise<FoodResult | null> {
  try {
    console.log('🤖 执行纯AI查询...');
    const prompt = buildPureAIPrompt(userQuery);
    const aiResponse = await callAIModel(prompt);

    const result = parsePureAIResponse(aiResponse);
    if (result) {
      console.log('✨ 纯AI查询成功:', result.name);
      return result;
    }

    return null;
  } catch (error) {
    console.error('❌ 纯AI查询失败:', error);
    return null;
  }
}

/**
 * 智能食物查询主函数
 * @param userQuery 用户查询
 * @returns 优化后的食物信息
 */
export async function intelligentFoodSearch(userQuery: string): Promise<FoodResult | null> {
  try {
    console.log(`🔍 开始智能查询: "${userQuery}"`);

    // 第一步：从数据库获取USDA数据
    console.log('📊 查询USDA数据库...');
    const databaseResult = await searchFood(userQuery, 'all');

    if (!databaseResult) {
      console.log('❌ 数据库中未找到匹配结果，转为纯AI查询');
      // 如果数据库中没有，直接使用AI查询
      return await pureAISearch(userQuery);
    }

    console.log('✅ 找到数据库结果:', databaseResult.name);

    // 第二步：调用大模型进行智能优化
    console.log('🤖 调用AI模型进行优化...');
    const prompt = buildPrompt(userQuery, databaseResult);
    const aiResponse = await callAIModel(prompt);

    console.log('🎯 AI模型响应完成');

    // 第三步：解析AI响应
    const optimizedResult = parseAIResponse(aiResponse);

    if (optimizedResult) {
      console.log('✨ AI优化成功:', optimizedResult.name);
      return optimizedResult;
    } else {
      console.log('⚠️ AI优化失败，返回原始数据库结果');
      return databaseResult;
    }

  } catch (error) {
    console.error('❌ 智能查询失败:', error);

    // 最后的降级：尝试纯AI查询
    console.log('🔄 最后降级：尝试纯AI查询...');
    try {
      const pureAIResult = await pureAISearch(userQuery);
      if (pureAIResult) {
        console.log('✅ 纯AI降级查询成功');
        return pureAIResult;
      }
    } catch (fallbackError) {
      console.error('❌ 纯AI降级查询也失败:', fallbackError);
    }

    return null;
  }
}
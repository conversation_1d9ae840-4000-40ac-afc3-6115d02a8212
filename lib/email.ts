import { Resend } from "resend";

const resend = new Resend(process.env.RESEND_API_KEY);

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

export async function sendEmail({ to, subject, html, from }: EmailOptions) {
  try {
    const result = await resend.emails.send({
      from: from || process.env.EMAIL_FROM || "HowLongFresh <<EMAIL>>",
      to,
      subject,
      html,
    });

    if (result.error) {
      console.error("Failed to send email:", result.error);
      throw new Error("Failed to send email");
    }

    return result;
  } catch (error) {
    console.error("Email service error:", error);
    throw error;
  }
}

export async function sendVerificationEmail(
  email: string,
  token: string,
  locale: string = "en"
) {
  const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/${locale}/auth/verify-email?token=${token}`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #333;">Verify Your Email</h1>
      <p style="color: #666; font-size: 16px;">
        Thank you for signing up for HowLongFresh! Please click the button below to verify your email address.
      </p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${verificationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Verify Email
        </a>
      </div>
      <p style="color: #999; font-size: 14px;">
        If you didn't sign up for HowLongFresh, you can safely ignore this email.
      </p>
      <p style="color: #999; font-size: 14px;">
        This link will expire in 24 hours.
      </p>
    </div>
  `;

  return sendEmail({
    to: email,
    subject: "Verify your email for HowLongFresh",
    html,
  });
}

export async function sendPasswordResetEmail(
  email: string,
  token: string,
  locale: string = "en"
) {
  const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/${locale}/auth/reset-password?token=${token}`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #333;">Reset Your Password</h1>
      <p style="color: #666; font-size: 16px;">
        We received a request to reset your password. Click the button below to create a new password.
      </p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${resetUrl}" style="background-color: #2196F3; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Reset Password
        </a>
      </div>
      <p style="color: #999; font-size: 14px;">
        If you didn't request a password reset, you can safely ignore this email.
      </p>
      <p style="color: #999; font-size: 14px;">
        This link will expire in 24 hours.
      </p>
    </div>
  `;

  return sendEmail({
    to: email,
    subject: "Reset your password for HowLongFresh",
    html,
  });
}

export async function sendWelcomeEmail(
  email: string,
  name: string,
  locale: string = "en"
) {
  const appUrl = `${process.env.NEXT_PUBLIC_APP_URL}/${locale}`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #333;">Welcome to HowLongFresh! 🎉</h1>
      <p style="color: #666; font-size: 16px;">
        Hi ${name || "there"},
      </p>
      <p style="color: #666; font-size: 16px;">
        Thank you for joining HowLongFresh! We're excited to help you keep track of how long your food stays fresh.
      </p>
      <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h2 style="color: #333; font-size: 18px;">Get Started:</h2>
        <ul style="color: #666; font-size: 16px;">
          <li>Search for any food item to see how long it stays fresh</li>
          <li>Upload photos of food items for instant identification</li>
          <li>Save your favorite foods for quick access</li>
        </ul>
      </div>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${appUrl}" style="background-color: #4CAF50; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Start Exploring
        </a>
      </div>
      <p style="color: #999; font-size: 14px;">
        If you have any questions, feel free to reach out to our support team.
      </p>
    </div>
  `;

  return sendEmail({
    to: email,
    subject: "Welcome to HowLongFresh!",
    html,
  });
}
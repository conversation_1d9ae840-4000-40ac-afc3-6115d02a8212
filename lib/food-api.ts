import { FoodResult } from '@/types/food';

export interface FoodApiError {
  error: string;
  message?: string;
  suggestions?: string[];
}

export class FoodApiException extends Error {
  public readonly apiError: FoodApiError;
  public readonly status: number;

  constructor(apiError: FoodApiError, status: number) {
    super(apiError.message || apiError.error);
    this.apiError = apiError;
    this.status = status;
    this.name = 'FoodApiException';
  }
}

/**
 * 通过文本识别食物
 */
export async function identifyFoodByText(foodName: string, locale?: string): Promise<FoodResult> {
  try {
    const response = await fetch('/api/food/identify-text', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        foodName: foodName.trim(),
        locale: locale || 'en'
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new FoodApiException(data, response.status);
    }

    return data as FoodResult;
  } catch (error) {
    if (error instanceof FoodApiException) {
      throw error;
    }

    // 网络错误或其他错误
    throw new FoodApiException(
      {
        error: 'Network error',
        message: 'Unable to connect to the server. Please check your internet connection and try again.'
      },
      0
    );
  }
}

/**
 * 通过图片识别食物
 */
export async function identifyFoodByImage(imageFile: File, locale?: string): Promise<FoodResult> {
  try {
    const formData = new FormData();
    formData.append('image', imageFile);
    formData.append('locale', locale || 'en');

    const response = await fetch('/api/food/identify-image', {
      method: 'POST',
      body: formData
    });

    const data = await response.json();

    if (!response.ok) {
      throw new FoodApiException(data, response.status);
    }

    return data as FoodResult;
  } catch (error) {
    if (error instanceof FoodApiException) {
      throw error;
    }

    // 网络错误或其他错误
    throw new FoodApiException(
      {
        error: 'Network error',
        message: 'Unable to connect to the server. Please check your internet connection and try again.'
      },
      0
    );
  }
}

/**
 * 验证图片文件
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    return { valid: false, error: 'Please select an image file' };
  }
  
  // 检查文件大小 (5MB)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    return { valid: false, error: 'Image size must be less than 5MB' };
  }
  
  // 检查支持的格式
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!supportedTypes.includes(file.type)) {
    return { valid: false, error: 'Supported formats: JPEG, PNG, WebP' };
  }
  
  return { valid: true };
}

/**
 * 格式化存储时间显示（英文版，用于首页搜索）
 */
export function formatStorageTime(days: number): string {
  if (days === 0) {
    return 'Not recommended';
  }

  if (days === 1) {
    return '1 day';
  }

  if (days < 7) {
    return `${days} days`;
  }

  if (days < 30) {
    const weeks = Math.floor(days / 7);
    const remainingDays = days % 7;

    if (remainingDays === 0) {
      return weeks === 1 ? '1 week' : `${weeks} weeks`;
    } else {
      return weeks === 1
        ? `1 week ${remainingDays} day${remainingDays > 1 ? 's' : ''}`
        : `${weeks} weeks ${remainingDays} day${remainingDays > 1 ? 's' : ''}`;
    }
  }

  if (days <= 365) {
    const months = Math.floor(days / 30);
    const remainingDays = days % 30;

    if (remainingDays === 0) {
      return months === 1 ? '1 month' : `${months} months`;
    } else {
      return months === 1
        ? `1 month ${remainingDays} day${remainingDays > 1 ? 's' : ''}`
        : `${months} months ${remainingDays} day${remainingDays > 1 ? 's' : ''}`;
    }
  }

  // 对于超过365天的，直接显示年份
  const years = Math.round(days / 365);
  return years === 1 ? '1 year' : `${years} years`;
}

/**
 * 格式化存储时间显示（中文版，用于详情页面）
 */
export function formatStorageTimeChinese(days: number): string {
  if (!days || days === 0) {
    return '不适用';
  }

  if (days === 1) {
    return '1天';
  }

  if (days < 7) {
    return `${days}天`;
  }

  if (days < 30) {
    const weeks = Math.floor(days / 7);
    const remainingDays = days % 7;

    if (remainingDays === 0) {
      return `${weeks}周`;
    } else {
      return `${weeks}周${remainingDays}天`;
    }
  }

  if (days <= 365) {
    const months = Math.floor(days / 30);
    const remainingDays = days % 30;

    if (remainingDays === 0) {
      return `${months}个月`;
    } else if (remainingDays < 7) {
      return `${months}个月${remainingDays}天`;
    } else {
      const weeks = Math.floor(remainingDays / 7);
      const finalDays = remainingDays % 7;
      if (finalDays === 0) {
        return `${months}个月${weeks}周`;
      } else {
        return `${months}个月${weeks}周${finalDays}天`;
      }
    }
  }

  // 对于超过365天的，直接显示年份
  const years = Math.round(days / 365);
  return years === 1 ? '1年' : `${years}年`;
}

/**
 * 获取存储建议的颜色
 */
export function getStorageColor(days: number): string {
  if (days === 0) return 'text-red-600';
  if (days <= 2) return 'text-orange-600';
  if (days <= 7) return 'text-yellow-600';
  if (days <= 30) return 'text-green-600';
  return 'text-blue-600';
}

import { createClient } from '@supabase/supabase-js';
import { FoodResult } from '@/types/food';

// Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// 创建 Supabase 客户端
const supabase = createClient(supabaseUrl, supabaseKey);

// 迁移数据的类型定义
interface MigratedFoodRecord {
  id: number;
  name: string;
  search_key: string;
  category_id: number;
  refrigerated_days: number | null;
  frozen_days: number | null;
  room_temperature_days: number | null;
  storage_tips: string[];
  source: string;
  confidence: number;
  food_categories?: {
    id: number;
    name: string;
    description: string;
    icon: string;
  };
}

interface MigratedFoodCategory {
  id: number;
  name: string;
  description: string;
  icon: string;
  slug: string;
}

/**
 * 获取所有迁移的食物分类
 */
export async function getMigratedFoodCategories(): Promise<MigratedFoodCategory[]> {
  try {
    const { data, error } = await supabase
      .from('food_categories')
      .select('*')
      .order('name');

    if (error) {
      console.error('Get migrated categories error:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Get migrated categories error:', error);
    return [];
  }
}

/**
 * 根据分类ID获取迁移的食物列表
 */
export async function getMigratedFoodsByCategoryId(
  categoryId: number,
  page: number = 1,
  pageSize: number = 24,
  sortBy: string = 'name'
): Promise<{
  foods: FoodResult[];
  total: number;
  hasMore: boolean;
}> {
  try {
    const offset = (page - 1) * pageSize;

    // 构建排序字段
    let orderField = 'name';
    let ascending = true;

    switch (sortBy) {
      case 'refrigerated_days':
        orderField = 'refrigerated_days';
        ascending = false;
        break;
      case 'frozen_days':
        orderField = 'frozen_days';
        ascending = false;
        break;
      case 'confidence':
        orderField = 'confidence';
        ascending = false;
        break;
      default:
        orderField = 'name';
        ascending = true;
    }

    // 获取食物数据
    const { data, error } = await supabase
      .from('foods')
      .select(`
        id,
        name,
        search_key,
        refrigerated_days,
        frozen_days,
        room_temperature_days,
        storage_tips,
        source,
        confidence,
        food_categories(
          id,
          name,
          description,
          icon
        )
      `)
      .eq('category_id', categoryId)
      .order(orderField, { ascending })
      .range(offset, offset + pageSize - 1);

    if (error) {
      console.error('Get migrated foods by category error:', error);
      return { foods: [], total: 0, hasMore: false };
    }

    // 获取总数
    const { count: total } = await supabase
      .from('foods')
      .select('*', { count: 'exact', head: true })
      .eq('category_id', categoryId);

    // 转换数据格式
    const foods: FoodResult[] = (data || []).map((food: any) => ({
      name: food.name,
      category: food.food_categories?.name || 'Unknown',
      storage: {
        refrigerated: food.refrigerated_days || 0,
        frozen: food.frozen_days || 0,
        room_temperature: food.room_temperature_days || 0
      },
      tips: food.storage_tips || [],
      confidence: food.confidence || 0.95,
      source: food.source === 'USDA' ? 'USDA' : 'LOCAL',
      isUSDAData: food.source === 'USDA',
      is_usda_verified: food.source === 'USDA'
    }));

    const hasMore = (page * pageSize) < (total || 0);

    return {
      foods,
      total: total || 0,
      hasMore
    };

  } catch (error) {
    console.error('Get migrated foods by category error:', error);
    return { foods: [], total: 0, hasMore: false };
  }
}

/**
 * 获取迁移数据的分类统计
 */
export async function getMigratedCategoryCounts(): Promise<Record<string, number>> {
  try {
    const { data, error } = await supabase
      .from('food_categories')
      .select(`
        id,
        name,
        foods(count)
      `);

    if (error) {
      console.error('Get migrated category counts error:', error);
      return {};
    }

    // 转换为 slug -> count 的映射
    const counts: Record<string, number> = {};
    data?.forEach((category: any) => {
      // 将分类名称转换为slug
      const slug = category.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
      counts[slug] = category.foods?.[0]?.count || 0;
    });

    return counts;
  } catch (error) {
    console.error('Get migrated category counts error:', error);
    return {};
  }
}

/**
 * 搜索迁移的食物数据
 */
export async function searchMigratedFood(
  searchTerm: string,
  language: string = 'all'
): Promise<FoodResult | null> {
  try {
    const trimmedTerm = searchTerm.trim().toLowerCase();

    // 首先在食物名称中搜索
    let { data, error } = await supabase
      .from('foods')
      .select(`
        id,
        name,
        search_key,
        refrigerated_days,
        frozen_days,
        room_temperature_days,
        storage_tips,
        source,
        confidence,
        food_categories(
          id,
          name,
          description,
          icon
        )
      `)
      .or(`name.ilike.%${trimmedTerm}%,search_key.ilike.%${trimmedTerm}%`)
      .limit(1);

    // 如果没找到，在别名中搜索
    if (!data || data.length === 0) {
      const { data: aliasData, error: aliasError } = await supabase
        .from('food_aliases')
        .select(`
          food_id,
          foods(
            id,
            name,
            search_key,
            refrigerated_days,
            frozen_days,
            room_temperature_days,
            storage_tips,
            source,
            confidence,
            food_categories(
              id,
              name,
              description,
              icon
            )
          )
        `)
        .ilike('alias', `%${trimmedTerm}%`)
        .limit(1);

      if (aliasError) {
        console.error('Alias search error:', aliasError);
        return null;
      }

      if (aliasData && aliasData.length > 0) {
        data = aliasData[0].foods;
      }
    }

    if (error) {
      console.error('Food search error:', error);
      return null;
    }

    if (!data || (Array.isArray(data) && data.length === 0)) {
      return null;
    }

    const food: any = Array.isArray(data) ? data[0] : data;

    // 获取分类名称
    let categoryName = 'Unknown';
    if (food.food_categories) {
      if (Array.isArray(food.food_categories) && food.food_categories.length > 0) {
        categoryName = food.food_categories[0].name || 'Unknown';
      } else if (typeof food.food_categories === 'object' && food.food_categories.name) {
        categoryName = food.food_categories.name;
      }
    }

    // 转换为 FoodResult 格式
    const result: FoodResult = {
      name: food.name,
      category: categoryName,
      storage: {
        refrigerated: food.refrigerated_days || 0,
        frozen: food.frozen_days || 0,
        room_temperature: food.room_temperature_days || 0
      },
      tips: food.storage_tips || [],
      confidence: food.confidence || 0.95,
      source: food.source === 'USDA' ? 'USDA' : 'LOCAL',
      isUSDAData: food.source === 'USDA'
    };

    return result;

  } catch (error) {
    console.error('Search migrated food error:', error);
    return null;
  }
}

/**
 * 获取食物建议（自动完成）
 */
export async function getMigratedFoodSuggestions(
  query: string,
  limit: number = 10
): Promise<string[]> {
  try {
    if (query.length < 2) return [];

    // 从食物名称获取建议
    const { data: foodData } = await supabase
      .from('foods')
      .select('name')
      .ilike('name', `${query}%`)
      .limit(limit);

    // 从别名获取建议
    const { data: aliasData } = await supabase
      .from('food_aliases')
      .select('alias')
      .ilike('alias', `${query}%`)
      .limit(limit);

    const suggestions = [
      ...(foodData?.map(item => item.name) || []),
      ...(aliasData?.map(item => item.alias) || [])
    ];

    // 去重并限制数量
    return [...new Set(suggestions)].slice(0, limit);

  } catch (error) {
    console.error('Get migrated suggestions error:', error);
    return [];
  }
}

/**
 * FAQ相关的工具函数
 */

/**
 * 生成指向特定FAQ的链接
 * @param faqId FAQ的ID
 * @param locale 语言环境，默认为'zh'
 * @returns 完整的FAQ链接
 */
export function generateFAQLink(faqId: string, locale: string = 'zh'): string {
  return `/${locale}/faq?highlight=${encodeURIComponent(faqId)}`;
}

/**
 * 生成带有搜索关键词的FAQ链接
 * @param searchTerm 搜索关键词
 * @param locale 语言环境，默认为'zh'
 * @returns 带搜索的FAQ链接
 */
export function generateFAQSearchLink(searchTerm: string, locale: string = 'zh'): string {
  return `/${locale}/faq?q=${encodeURIComponent(searchTerm)}`;
}

/**
 * 生成带有分类过滤的FAQ链接
 * @param category 分类名称
 * @param locale 语言环境，默认为'zh'
 * @returns 带分类过滤的FAQ链接
 */
export function generateFAQCategoryLink(category: string, locale: string = 'zh'): string {
  return `/${locale}/faq?category=${encodeURIComponent(category)}`;
}

/**
 * 根据内容类型和ID生成相应的链接
 * @param content 内容信息
 * @returns 相应的链接
 */
export function generateSourceLink(content: {
  type: 'faq' | 'blog';
  id: string;
  locale?: string;
}): string {
  const locale = content.locale || 'zh';
  
  if (content.type === 'faq') {
    return generateFAQLink(content.id, locale);
  } else {
    return `/${locale}/posts/${content.id}`;
  }
}

/**
 * 从URL中提取高亮的FAQ ID
 * @param searchParams URL搜索参数
 * @returns 高亮的FAQ ID，如果没有则返回undefined
 */
export function extractHighlightId(searchParams: URLSearchParams): string | undefined {
  return searchParams.get('highlight') || undefined;
}

/**
 * 检查是否为有效的FAQ ID格式
 * @param id 要检查的ID
 * @returns 是否为有效格式
 */
export function isValidFAQId(id: string): boolean {
  // FAQ ID应该是非空字符串，可以包含字母、数字、连字符和下划线
  return /^[a-zA-Z0-9_-]+$/.test(id) && id.length > 0;
}

/**
 * 生成FAQ的锚点ID（用于页面内跳转）
 * @param faqId FAQ的ID
 * @returns 锚点ID
 */
export function generateFAQAnchor(faqId: string): string {
  return `faq-${faqId}`;
}

/**
 * 常见的FAQ相关常量
 */
export const FAQ_CONSTANTS = {
  // 默认语言
  DEFAULT_LOCALE: 'zh',
  
  // URL参数名
  PARAMS: {
    HIGHLIGHT: 'highlight',
    SEARCH: 'q',
    CATEGORY: 'category',
  },
  
  // 常见的FAQ分类
  CATEGORIES: {
    STORAGE: 'storage',
    SAFETY: 'safety',
    IDENTIFICATION: 'identification',
    GENERAL: 'general',
  },
  
  // 滚动行为配置
  SCROLL_OPTIONS: {
    behavior: 'smooth' as ScrollBehavior,
    block: 'center' as ScrollLogicalPosition,
  },
} as const;

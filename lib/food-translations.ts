// Food name translations mapping
export const FOOD_NAME_TRANSLATIONS: Record<string, { zh: string; en: string }> = {
  // Meat & Poultry
  'fried_chicken': { en: 'Fried chicken', zh: '炸鸡' },
  'chicken': { en: 'Chicken', zh: '鸡肉' },
  'beef': { en: 'Beef', zh: '牛肉' },
  'pork': { en: 'Pork', zh: '猪肉' },
  'turkey': { en: 'Turkey', zh: '火鸡' },
  'lamb': { en: 'Lamb', zh: '羊肉' },
  'ground_beef': { en: 'Ground beef', zh: '牛肉馅' },
  'ground_pork': { en: 'Ground pork', zh: '猪肉馅' },
  'bacon': { en: 'Bacon', zh: '培根' },
  'ham': { en: 'Ham', zh: '火腿' },
  'sausage': { en: 'Sausage', zh: '香肠' },
  'steak': { en: 'Steak', zh: '牛排' },
  'chicken_breast': { en: 'Chicken breast', zh: '鸡胸肉' },
  'chicken_thigh': { en: 'Chicken thigh', zh: '鸡腿肉' },
  'pork_chops': { en: 'Pork chops', zh: '猪排' },
  'ribs': { en: 'Ribs', zh: '排骨' },

  // Seafood
  'salmon': { en: 'Salmon', zh: '三文鱼' },
  'tuna': { en: 'Tuna', zh: '金枪鱼' },
  'shrimp': { en: 'Shrimp', zh: '虾' },
  'crab': { en: 'Crab', zh: '螃蟹' },
  'lobster': { en: 'Lobster', zh: '龙虾' },
  'fish': { en: 'Fish', zh: '鱼' },
  'cod': { en: 'Cod', zh: '鳕鱼' },
  'tilapia': { en: 'Tilapia', zh: '罗非鱼' },
  'mussels': { en: 'Mussels', zh: '贻贝' },
  'oysters': { en: 'Oysters', zh: '牡蛎' },
  'scallops': { en: 'Scallops', zh: '扇贝' },

  // Dairy Products & Eggs
  'milk': { en: 'Milk', zh: '牛奶' },
  'cheese': { en: 'Cheese', zh: '奶酪' },
  'yogurt': { en: 'Yogurt', zh: '酸奶' },
  'butter': { en: 'Butter', zh: '黄油' },
  'cream': { en: 'Cream', zh: '奶油' },
  'eggs': { en: 'Eggs', zh: '鸡蛋' },
  'cottage_cheese': { en: 'Cottage Cheese', zh: '茅屋奶酪' },
  'cream_cheese': { en: 'Cream Cheese', zh: '奶油奶酪' },
  'cheddar_cheese': { en: 'Cheddar Cheese', zh: '切达奶酪' },
  'mozzarella_cheese': { en: 'Mozzarella Cheese', zh: '马苏里拉奶酪' },
  'american_cheese': { en: 'American Cheese', zh: '美式奶酪' },
  'blue_cheese': { en: 'Blue Cheese', zh: '蓝纹奶酪' },
  'brie_cheese': { en: 'Brie Cheese', zh: '布里奶酪' },
  'buttermilk': { en: 'Buttermilk', zh: '酪乳' },
  'sour_cream': { en: 'Sour Cream', zh: '酸奶油' },

  // Fruits
  'apple': { en: 'Apple', zh: '苹果' },
  'banana': { en: 'Banana', zh: '香蕉' },
  'orange': { en: 'Orange', zh: '橙子' },
  'grapes': { en: 'Grapes', zh: '葡萄' },
  'strawberries': { en: 'Strawberries', zh: '草莓' },
  'blueberries': { en: 'Blueberries', zh: '蓝莓' },
  'pineapple': { en: 'Pineapple', zh: '菠萝' },
  'mango': { en: 'Mango', zh: '芒果' },
  'peach': { en: 'Peach', zh: '桃子' },
  'pear': { en: 'Pear', zh: '梨' },
  'watermelon': { en: 'Watermelon', zh: '西瓜' },
  'cantaloupe': { en: 'Cantaloupe', zh: '哈密瓜' },
  'avocado': { en: 'Avocado', zh: '牛油果' },
  'lemon': { en: 'Lemon', zh: '柠檬' },
  'lime': { en: 'Lime', zh: '青柠' },
  'cherries': { en: 'Cherries', zh: '樱桃' },
  'kiwi': { en: 'Kiwi', zh: '猕猴桃' },

  // Vegetables
  'carrot': { en: 'Carrot', zh: '胡萝卜' },
  'potato': { en: 'Potato', zh: '土豆' },
  'onion': { en: 'Onion', zh: '洋葱' },
  'tomato': { en: 'Tomato', zh: '番茄' },
  'lettuce': { en: 'Lettuce', zh: '生菜' },
  'spinach': { en: 'Spinach', zh: '菠菜' },
  'broccoli': { en: 'Broccoli', zh: '西兰花' },
  'cauliflower': { en: 'Cauliflower', zh: '花椰菜' },
  'bell_pepper': { en: 'Bell Pepper', zh: '甜椒' },
  'cucumber': { en: 'Cucumber', zh: '黄瓜' },
  'celery': { en: 'Celery', zh: '芹菜' },
  'mushrooms': { en: 'Mushrooms', zh: '蘑菇' },
  'garlic': { en: 'Garlic', zh: '大蒜' },
  'ginger': { en: 'Ginger', zh: '生姜' },
  'green_beans': { en: 'Green Beans', zh: '四季豆' },
  'corn': { en: 'Corn', zh: '玉米' },
  'cabbage': { en: 'Cabbage', zh: '卷心菜' },
  'zucchini': { en: 'Zucchini', zh: '西葫芦' },

  // Grains, Beans & Pasta
  'rice': { en: 'Rice', zh: '米饭' },
  'bread': { en: 'Bread', zh: '面包' },
  'pasta': { en: 'Pasta', zh: '意大利面' },
  'angel_hair_pasta_dry': { en: 'Angel Hair Pasta Dry', zh: '天使面条(干)' },
  'quinoa': { en: 'Quinoa', zh: '藜麦' },
  'oats': { en: 'Oats', zh: '燕麦' },
  'wheat': { en: 'Wheat', zh: '小麦' },
  'barley': { en: 'Barley', zh: '大麦' },
  'black_beans': { en: 'Black Beans', zh: '黑豆' },
  'kidney_beans': { en: 'Kidney Beans', zh: '芸豆' },
  'chickpeas': { en: 'Chickpeas', zh: '鹰嘴豆' },
  'lentils': { en: 'Lentils', zh: '扁豆' },
  'brown_rice': { en: 'Brown Rice', zh: '糙米' },
  'white_rice': { en: 'White Rice', zh: '白米' },
  'whole_wheat_bread': { en: 'Whole Wheat Bread', zh: '全麦面包' },
  'bagel': { en: 'Bagel', zh: '百吉饼' },
  'cereal': { en: 'Cereal', zh: '谷物' },
};

// Category translations
export const CATEGORY_TRANSLATIONS: Record<string, { zh: string; en: string }> = {
  'produce': { en: 'Produce', zh: '农产品' },
  'meat': { en: 'Meat', zh: '肉类' },
  'poultry': { en: 'Poultry', zh: '禽肉类' },
  'seafood': { en: 'Seafood', zh: '海鲜' },
  'dairy_products_eggs': { en: 'Dairy Products & Eggs', zh: '乳制品与蛋类' },
  'grains_beans_pasta': { en: 'Grains, Beans & Pasta', zh: '谷物、豆类与面食' },
  'condiments_sauces_canned': { en: 'Condiments, Sauces & Canned Goods', zh: '调料、酱汁与罐装食品' },
  'beverages': { en: 'Beverages', zh: '饮料' },
  'deli_prepared': { en: 'Deli & Prepared Foods', zh: '熟食与预制食品' },
  'baked_goods': { en: 'Baked Goods', zh: '烘焙食品' },
  'vegetarian_proteins': { en: 'Vegetarian Proteins', zh: '素食蛋白' },
  'dairy': { en: 'Dairy', zh: '乳制品' },
  'dairy_eggs': { en: 'Dairy & Eggs', zh: '乳制品与蛋类' },
  'grains': { en: 'Grains', zh: '谷物' },
  'snacks': { en: 'Snacks', zh: '零食' },
  'condiments': { en: 'Condiments', zh: '调料' },
  'spices': { en: 'Spices', zh: '香料' },
  'fruits': { en: 'Fruits', zh: '水果' },
  'vegetables': { en: 'Vegetables', zh: '蔬菜' },
  'meat_poultry': { en: 'Meat & Poultry', zh: '肉类与禽肉' },
  'asian_specialties': { en: 'Asian Specialties', zh: '亚洲特色食品' },
  'frozen_foods': { en: 'Food Purchased Frozen', zh: '冷冻食品' },
  'shelf_stable': { en: 'Shelf Stable Foods', zh: '常温保存食品' },
};

// Helper function to get food name translation
export function getFoodNameTranslation(foodName: string, locale: string): string {
  // First, try to find a direct match by normalizing the food name to a key
  const normalizedKey = foodName.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
  
  if (FOOD_NAME_TRANSLATIONS[normalizedKey]) {
    return locale === 'zh' ? FOOD_NAME_TRANSLATIONS[normalizedKey].zh : FOOD_NAME_TRANSLATIONS[normalizedKey].en;
  }

  // If no translation found, try to find a partial match
  const partialMatch = Object.entries(FOOD_NAME_TRANSLATIONS).find(([key, value]) => {
    return value.en.toLowerCase() === foodName.toLowerCase() || value.zh === foodName;
  });

  if (partialMatch) {
    return locale === 'zh' ? partialMatch[1].zh : partialMatch[1].en;
  }

  // Return original name if no translation found
  return foodName;
}

// Helper function to get category translation
export function getCategoryTranslation(category: string, locale: string): string {
  const normalizedKey = category.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
  
  if (CATEGORY_TRANSLATIONS[normalizedKey]) {
    return locale === 'zh' ? CATEGORY_TRANSLATIONS[normalizedKey].zh : CATEGORY_TRANSLATIONS[normalizedKey].en;
  }

  // Try exact match
  const exactMatch = Object.entries(CATEGORY_TRANSLATIONS).find(([key, value]) => {
    return value.en === category || value.zh === category;
  });

  if (exactMatch) {
    return locale === 'zh' ? exactMatch[1].zh : exactMatch[1].en;
  }

  return category;
}
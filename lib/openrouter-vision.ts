import { FoodResult } from '@/types/food';

interface OpenRouterMessage {
  role: 'user' | 'assistant' | 'system';
  content: Array<{
    type: 'text' | 'image_url';
    text?: string;
    image_url?: {
      url: string;
    };
  }>;
}

interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

/**
 * 将图片文件转换为 base64 数据 URL（服务器端版本）
 */
async function fileToBase64DataUrl(file: File): Promise<string> {
  try {
    // 在服务器端，File对象实际上是一个Blob
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const base64 = buffer.toString('base64');

    // 根据文件类型构建data URL
    const mimeType = file.type || 'image/jpeg';
    return `data:${mimeType};base64,${base64}`;
  } catch (error) {
    console.error('Error converting file to base64:', error);
    throw new Error('Failed to convert file to base64');
  }
}

/**
 * 检测用户偏好语言（优先使用传递的 locale 参数，回退到请求头）
 */
function detectPreferredLanguage(locale?: string, request?: Request): 'zh' | 'en' {
  // 优先使用传递的 locale 参数
  if (locale) {
    return locale === 'zh' ? 'zh' : 'en';
  }

  if (request) {
    // 检查Accept-Language请求头
    const acceptLanguage = request.headers.get('accept-language') || '';

    // 检查是否包含中文
    if (acceptLanguage.includes('zh') || acceptLanguage.includes('cn')) {
      return 'zh';
    }

    // 检查是否明确指定英文
    if (acceptLanguage.includes('en')) {
      return 'en';
    }
  }

  // 默认返回英文（因为这是国际化网站）
  return 'en';
}

/**
 * 解析存储时间值，处理各种格式
 */
function parseStorageValue(value: any): number {
  if (typeof value === 'number') {
    return value;
  }

  if (typeof value === 'string') {
    // 提取数字部分
    const match = value.match(/(\d+)/);
    if (match) {
      const num = parseInt(match[1]);

      // 处理月份转换为天数
      if (value.includes('月') || value.includes('month')) {
        return num * 30; // 1个月 = 30天
      }

      return num;
    }
  }

  return 0;
}

/**
 * 解析 AI 响应为结构化的食物数据
 */
function parseAIResponse(response: string, locale?: string): FoodResult {
  try {
    // 尝试提取JSON部分
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in AI response');
    }

    let jsonStr = jsonMatch[0];

    // 清理可能的格式问题
    jsonStr = jsonStr.replace(/```json\s*/, '').replace(/```\s*$/, '');

    const parsed = JSON.parse(jsonStr);

    // 验证必要字段
    if (!parsed.name || !parsed.storage) {
      throw new Error('Invalid response format');
    }

    return {
      name: parsed.name,
      category: parsed.category || 'Unknown',
      storage: {
        refrigerated: parseStorageValue(parsed.storage.refrigerated),
        frozen: parseStorageValue(parsed.storage.frozen),
        room_temperature: parseStorageValue(parsed.storage.room_temperature)
      },
      tips: Array.isArray(parsed.tips) ? parsed.tips : [],
      confidence: parsed.confidence || 0.8,
      source: 'AI',
      isUSDAData: false
    };
  } catch (error) {
    // 如果 JSON 解析失败，尝试从文本中提取信息
    console.warn('Failed to parse JSON response, attempting text extraction:', error);

    const preferredLang = detectPreferredLanguage(locale);

    // 简单的文本解析逻辑
    const lines = response.split('\n').filter(line => line.trim());
    let name = preferredLang === 'zh' ? '未知食物' : 'Unknown Food';
    let category = preferredLang === 'zh' ? '未知' : 'Unknown';

    // 查找食物名称
    for (const line of lines) {
      if (line.toLowerCase().includes('food:') || line.toLowerCase().includes('item:') ||
          line.includes('食物:') || line.includes('食品:')) {
        name = line.split(':')[1]?.trim() || name;
        break;
      }
    }

    // 默认存储时间（保守估计）
    const defaultTips = preferredLang === 'zh'
      ? ['存放在阴凉干燥处', '食用前检查是否变质']
      : ['Store in a cool, dry place', 'Check for signs of spoilage before consuming'];

    return {
      name,
      category,
      storage: {
        refrigerated: 7,
        frozen: 90,
        room_temperature: 3
      },
      tips: defaultTips,
      confidence: 0.6,
      source: 'AI',
      isUSDAData: false
    };
  }
}

/**
 * 使用 OpenRouter Gemini 2.5 Flash 识别图片中的食物
 */
export async function identifyFoodWithGemini(imageFile: File, request?: Request, locale?: string): Promise<FoodResult> {
  try {
    // 转换图片为 base64
    const imageDataUrl = await fileToBase64DataUrl(imageFile);

    // 构建提示词（支持多语言）
    const preferredLang = detectPreferredLanguage(locale, request);

    const systemPrompt = preferredLang === 'zh'
      ? `你是一个食物识别和保鲜专家。请分析图片并识别其中显示的食物。

请以以下JSON格式回复：
{
  "name": "食物名称（中文，例如：'苹果'、'香蕉'、'鸡胸肉'）",
  "category": "食物类别（中文，例如：'水果'、'蔬菜'、'肉类'、'乳制品'、'烘焙食品'）",
  "storage": {
    "refrigerated": 冷藏天数（纯数字），
    "frozen": 冷冻天数（纯数字），
    "room_temperature": 常温天数（纯数字）
  },
  "tips": [
    "保鲜建议1（中文）",
    "保鲜建议2（中文）",
    "保鲜建议3（中文）"
  ],
  "confidence": 置信度分数（0到1之间）
}

重要指导原则：
- 如果图片中有多种食物，请专注于最显著的一种
- 根据食品安全标准使用现实的存储时间
- 对于必须冷藏的食物，将常温天数设为0
- 提供实用的保鲜建议
- 为了安全起见，存储时间要保守估计
- 存储时间必须是纯数字，不要包含单位或范围`
      : `You are a food identification and storage expert. Analyze the image and identify the food item(s) shown.

Please respond with a JSON object in this exact format:
{
  "name": "Food name (e.g., 'Apple', 'Banana', 'Chicken Breast')",
  "category": "Food category (e.g., 'Fruit', 'Vegetable', 'Meat', 'Dairy', 'Bakery')",
  "storage": {
    "refrigerated": number_of_days_in_refrigerator,
    "frozen": number_of_days_in_freezer,
    "room_temperature": number_of_days_at_room_temperature
  },
  "tips": [
    "Storage tip 1",
    "Storage tip 2",
    "Storage tip 3"
  ],
  "confidence": confidence_score_between_0_and_1
}

Important guidelines:
- If multiple food items are visible, focus on the most prominent one
- Use realistic storage durations based on food safety standards
- Set room_temperature to 0 for foods that must be refrigerated
- Provide practical storage tips
- Be conservative with storage times for safety
- Storage times must be pure numbers, no units or ranges`;

    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: [{ type: 'text', text: systemPrompt }]
      },
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: preferredLang === 'zh'
              ? '请识别这张图片中的食物并提供保鲜信息。'
              : 'Please identify the food in this image and provide storage information.'
          },
          {
            type: 'image_url',
            image_url: {
              url: imageDataUrl
            }
          }
        ]
      }
    ];

    // 调用 OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000',
        'X-Title': 'HowLongFresh - Food Storage Assistant'
      },
      body: JSON.stringify({
        model: process.env.OPENROUTER_MODEL || 'google/gemini-flash-1.5',
        messages,
        max_tokens: 1000,
        temperature: 0.3
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('OpenRouter API error:', response.status, errorText);
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data: OpenRouterResponse = await response.json();
    
    if (!data.choices || data.choices.length === 0) {
      throw new Error('No response from AI model');
    }

    const aiResponse = data.choices[0].message.content;
    console.log('AI Response:', aiResponse);
    
    return parseAIResponse(aiResponse, locale);

  } catch (error) {
    console.error('Error in Gemini food identification:', error);

    const preferredLang = detectPreferredLanguage(locale, request);

    // 提供更友好的错误信息
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        const message = preferredLang === 'zh'
          ? 'API密钥配置错误，请联系管理员'
          : 'API key configuration error, please contact administrator';
        throw new Error(message);
      }

      if (error.message.includes('OpenRouter API error')) {
        const message = preferredLang === 'zh'
          ? '图像识别服务暂时不可用，请稍后重试'
          : 'Image recognition service temporarily unavailable, please try again later';
        throw new Error(message);
      }

      if (error.message.includes('No response')) {
        const message = preferredLang === 'zh'
          ? 'AI模型无响应，请重新上传图片'
          : 'No response from AI model, please re-upload the image';
        throw new Error(message);
      }
    }

    // 默认错误信息
    const defaultMessage = preferredLang === 'zh'
      ? '无法识别图片中的食物，请尝试上传更清晰的照片，或使用文字输入功能'
      : 'Unable to identify the food in the image. Please try uploading a clearer photo, or use text input instead';

    throw new Error(defaultMessage);
  }
}

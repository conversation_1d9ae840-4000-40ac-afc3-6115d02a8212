// 自定义 fetch 函数，支持代理
const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY || process.env.https_proxy || process.env.http_proxy;

// 需要使用代理的域名列表
const PROXY_DOMAINS = [
  'accounts.google.com',
  'oauth2.googleapis.com',
  'openidconnect.googleapis.com',
  'www.googleapis.com',
  'fonts.googleapis.com'
];

export async function proxyFetch(url: string, options: RequestInit = {}): Promise<Response> {
  if (typeof window !== 'undefined') {
    // 在浏览器端使用原生 fetch
    return fetch(url, options);
  }

  // 检查是否需要使用代理
  const needsProxy = PROXY_DOMAINS.some(domain => url.includes(domain));

  // 在服务器端，只对特定域名使用代理
  if (proxyUrl && needsProxy) {
    // Check if we're in Edge runtime
    if (typeof (globalThis as any).EdgeRuntime !== 'undefined') {
      console.warn('Proxy not supported in Edge runtime, using regular fetch for:', url);
      return fetch(url, options);
    }

    console.log('🔧 Using proxy for fetch:', proxyUrl, 'URL:', url);

    try {
      // Dynamic import to avoid Edge runtime issues
      const { ProxyAgent, fetch: undicieFetch } = await import('undici');
      const proxyAgent = new ProxyAgent(proxyUrl);

      return undicieFetch(url, {
        ...options,
        dispatcher: proxyAgent,
      } as any) as unknown as Promise<Response>;
    } catch (error: any) {
      console.warn('Proxy fetch failed, falling back to regular fetch:', error.message);
      return fetch(url, options);
    }
  }

  // 对于其他域名（如 Supabase）使用原生 fetch
  return fetch(url, options);
}

// 在服务器端替换全局 fetch，但只对特定域名使用代理
// Skip this in Edge runtime to avoid issues
if (typeof window === 'undefined' && typeof (globalThis as any).EdgeRuntime === 'undefined' && proxyUrl) {
  const originalFetch = global.fetch;

  global.fetch = async (url: string | URL | Request, options?: RequestInit) => {
    const urlString = typeof url === 'string' ? url : url.toString();

    // 检查是否需要使用代理
    const needsProxy = PROXY_DOMAINS.some(domain => urlString.includes(domain));

    if (needsProxy) {
      console.log('🔧 Using proxy for global fetch:', proxyUrl, 'URL:', urlString);
      return proxyFetch(urlString, options || {});
    }

    // 对于其他域名使用原生 fetch
    return originalFetch(url, options);
  };

  console.log('🔧 Global fetch replaced with selective proxy fetch');
}

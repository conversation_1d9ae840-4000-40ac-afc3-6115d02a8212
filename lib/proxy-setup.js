// 代理设置模块
// 在应用启动时配置全局代理

import { ProxyAgent, setGlobalDispatcher } from 'undici';

function setupProxy() {
  // 检查是否有代理配置
  const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY || process.env.https_proxy || process.env.http_proxy;

  if (proxyUrl) {
    console.log('🔧 Setting up global proxy:', proxyUrl);

    try {
      // 为 undici 设置全局代理
      const proxyAgent = new ProxyAgent(proxyUrl);
      setGlobalDispatcher(proxyAgent);

      console.log('✅ Global proxy configured successfully');
    } catch (error) {
      console.error('❌ Failed to setup proxy:', error.message);
    }
  } else {
    console.log('ℹ️ No proxy configuration found');
  }
}

// 立即执行代理设置
setupProxy();

export { setupProxy };

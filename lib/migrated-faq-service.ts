/**
 * 迁移FAQ数据服务
 * 用于测试和使用从旧项目迁移的FAQ数据
 */

import { createClient } from '@supabase/supabase-js';
import { FAQItem, FAQCategory } from '@/types/faq';

// Supabase客户端
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

/**
 * 获取迁移的FAQ分类
 */
export async function getMigratedFAQCategories(): Promise<FAQCategory[]> {
  try {
    const { data, error } = await supabase
      .from('migrated_faq_categories')
      .select('*')
      .eq('is_active', true)
      .order('priority', { ascending: false });

    if (error) {
      console.error('获取迁移FAQ分类失败:', error);
      return [];
    }

    // 获取每个分类的FAQ数量
    const categoriesWithCount = await Promise.all(
      data.map(async (category) => {
        const { count } = await supabase
          .from('migrated_faqs')
          .select('*', { count: 'exact', head: true })
          .eq('category_id', category.id)
          .eq('is_active', true);

        return {
          name: category.slug,
          name_zh: category.name,
          icon: category.icon || '❓',
          count: count || 0,
          priority: category.priority,
          description: category.description
        };
      })
    );

    return categoriesWithCount;
  } catch (error) {
    console.error('获取迁移FAQ分类失败:', error);
    return [];
  }
}

/**
 * 获取所有迁移的FAQ数据
 */
export async function getAllMigratedFAQs(): Promise<FAQItem[]> {
  try {
    const { data, error } = await supabase
      .from('migrated_faqs')
      .select(`
        *,
        migrated_faq_categories (
          name,
          name_zh,
          slug,
          icon,
          description
        )
      `)
      .eq('is_active', true)
      .order('original_id', { ascending: true });

    if (error) {
      console.error('获取迁移FAQ数据失败:', error);
      return [];
    }

    // 转换数据格式以匹配FAQItem接口
    const faqs: FAQItem[] = data.map(item => ({
      id: item.id.toString(),
      question: item.question,
      answer: item.answer,
      answer_summary: item.answer_summary || item.answer.substring(0, 200) + '...',
      category: item.migrated_faq_categories?.slug || 'general',
      category_zh: item.migrated_faq_categories?.name_zh || '一般问题',
      category_icon: item.migrated_faq_categories?.icon || '❓',
      related_foods: item.related_foods || [],
      tags: item.tags || [],
      key_points: item.key_points || [],
      word_count: item.word_count || 0,
      view_count: item.view_count || 0,
      helpful_count: item.helpful_count || 0,
      source: {
        name: item.source_name || 'StillTasty.com',
        url: item.source_url || '',
        confidence: item.confidence || 0.95,
        processed_at: item.processed_at
      },
      created_at: item.created_at,
      updated_at: item.updated_at
    }));

    return faqs;
  } catch (error) {
    console.error('获取迁移FAQ数据失败:', error);
    return [];
  }
}

/**
 * 获取热门迁移FAQ（按查看次数排序）
 */
export async function getPopularMigratedFAQs(limit: number = 6): Promise<FAQItem[]> {
  try {
    const allFAQs = await getAllMigratedFAQs();
    return allFAQs
      .sort((a, b) => (b.view_count || 0) - (a.view_count || 0))
      .slice(0, limit);
  } catch (error) {
    console.error('获取热门迁移FAQ失败:', error);
    return [];
  }
}

/**
 * 搜索迁移的FAQ
 */
export async function searchMigratedFAQs(query: string): Promise<FAQItem[]> {
  try {
    const allFAQs = await getAllMigratedFAQs();
    const searchTerm = query.toLowerCase();

    return allFAQs.filter(faq =>
      faq.question.toLowerCase().includes(searchTerm) ||
      faq.answer.toLowerCase().includes(searchTerm) ||
      faq.related_foods.some(food => food.toLowerCase().includes(searchTerm)) ||
      faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  } catch (error) {
    console.error('搜索迁移FAQ失败:', error);
    return [];
  }
}
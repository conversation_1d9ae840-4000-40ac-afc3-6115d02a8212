#!/usr/bin/env python3
"""
USDA 数据集成工具
将 USDA FoodKeeper 数据转换为项目可用的格式
"""

import pandas as pd
import json
import re
from typing import Dict, List, Optional

def parse_storage_time(min_val, max_val, metric) -> Optional[Dict]:
    """解析存储时间"""
    if pd.isna(min_val) or pd.isna(max_val) or pd.isna(metric):
        return None
    
    # 处理特殊情况
    if metric == "Not Recommended":
        return None
    if metric == "Indefinitely":
        return {"min": 999, "max": 999, "unit": "Days", "note": "Indefinitely"}
    if metric == "When Ripe":
        return {"min": 0, "max": 0, "unit": "Days", "note": "Until ripe"}
    
    # 标准化时间单位
    unit_map = {
        "Days": "Days",
        "Weeks": "Weeks", 
        "Months": "Months",
        "Years": "Years",
        "Hours": "Hours"
    }
    
    unit = unit_map.get(metric, "Days")
    
    return {
        "min": int(float(min_val)),
        "max": int(float(max_val)),
        "unit": unit
    }

def convert_to_days(storage_info: Dict) -> int:
    """将存储时间转换为天数"""
    if not storage_info:
        return 0
    
    min_time = storage_info["min"]
    unit = storage_info["unit"]
    
    conversion = {
        "Hours": min_time / 24,
        "Days": min_time,
        "Weeks": min_time * 7,
        "Months": min_time * 30,
        "Years": min_time * 365
    }
    
    return int(conversion.get(unit, min_time))

def create_chinese_mapping() -> Dict[str, str]:
    """创建中英文食物名称映射"""
    mapping = {
        # 水果类
        "Apples": "苹果",
        "Apricots": "杏子", 
        "Avocados": "牛油果",
        "Bananas": "香蕉",
        "Berries": "浆果",
        "Blueberries": "蓝莓",
        "Cherries": "樱桃",
        "Coconut": "椰子",
        "Cranberries": "蔓越莓",
        "Grapes": "葡萄",
        "Kiwi fruit": "猕猴桃",
        "Melons": "瓜类",
        "Peaches": "桃子",
        "Pears": "梨",
        "Pineapple": "菠萝",
        "Strawberries": "草莓",
        "Watermelon": "西瓜",
        
        # 蔬菜类
        "Artichokes": "洋蓟",
        "Asparagus": "芦笋", 
        "Beans": "豆类",
        "Beets": "甜菜",
        "Broccoli": "西兰花",
        "Brussels sprouts": "抱子甘蓝",
        "Cabbage": "卷心菜",
        "Carrots": "胡萝卜",
        "Cauliflower": "花椰菜",
        "Celery": "芹菜",
        "Corn": "玉米",
        "Cucumbers": "黄瓜",
        "Eggplant": "茄子",
        "Lettuce": "生菜",
        "Mushrooms": "蘑菇",
        "Onions": "洋葱",
        "Peppers": "辣椒",
        "Potatoes": "土豆",
        "Spinach": "菠菜",
        "Tomatoes": "西红柿",
        
        # 乳制品
        "Eggs": "鸡蛋",
        "Milk": "牛奶",
        "Cheese": "奶酪",
        "Butter": "黄油",
        "Cream": "奶油",
        "Yogurt": "酸奶",
        
        # 肉类
        "Beef": "牛肉",
        "Pork": "猪肉", 
        "Chicken": "鸡肉",
        "Turkey": "火鸡肉",
        "Fish": "鱼肉",
        "Salmon": "三文鱼",
        "Shrimp": "虾",
        "Crab": "螃蟹",
        
        # 其他
        "Bread": "面包",
        "Rice": "米饭",
        "Pasta": "意大利面"
    }
    
    return mapping

def integrate_usda_data():
    """集成 USDA 数据"""
    print("🔄 开始集成 USDA 数据...")
    
    try:
        # 读取 USDA 数据
        df_product = pd.read_excel('FoodKeeper-Data.xls', sheet_name='Product')
        df_category = pd.read_excel('FoodKeeper-Data.xls', sheet_name='Category')
        
        # 合并类别信息
        df = df_product.merge(df_category, left_on='Category_ID', right_on='ID', how='left')
        
        # 筛选有存储数据的食物
        has_storage = df[
            (df['Pantry_Min'].notna()) | 
            (df['Refrigerate_Min'].notna()) | 
            (df['Freeze_Min'].notna())
        ].copy()
        
        print(f"找到 {len(has_storage)} 种有存储数据的食物")
        
        # 转换数据格式
        food_database = {}
        chinese_mapping = create_chinese_mapping()
        
        for _, row in has_storage.iterrows():
            name = row['Name']
            subtitle = row['Name_subtitle'] if pd.notna(row['Name_subtitle']) else ""
            category = row['Category_Name'] if pd.notna(row['Category_Name']) else "Unknown"
            
            # 解析存储信息
            pantry = parse_storage_time(row['Pantry_Min'], row['Pantry_Max'], row['Pantry_Metric'])
            refrigerate = parse_storage_time(row['Refrigerate_Min'], row['Refrigerate_Max'], row['Refrigerate_Metric'])
            freeze = parse_storage_time(row['Freeze_Min'], row['Freeze_Max'], row['Freeze_Metric'])
            
            # 跳过没有任何存储信息的食物
            if not any([pantry, refrigerate, freeze]):
                continue
            
            # 创建食物条目
            food_key = name.lower().replace(' ', '_')
            
            # 转换为项目格式 (天数)
            storage_days = {}
            if refrigerate:
                storage_days['refrigerated'] = convert_to_days(refrigerate)
            if freeze:
                storage_days['frozen'] = convert_to_days(freeze)
            if pantry:
                storage_days['room_temperature'] = convert_to_days(pantry)
            
            # 如果没有冷藏数据但有常温数据，设置默认冷藏时间
            if 'refrigerated' not in storage_days and 'room_temperature' in storage_days:
                storage_days['refrigerated'] = storage_days['room_temperature'] * 2
            
            # 如果没有冷冻数据，设置默认冷冻时间
            if 'frozen' not in storage_days:
                storage_days['frozen'] = 365  # 默认1年
            
            # 如果没有常温数据，设置默认常温时间
            if 'room_temperature' not in storage_days:
                if 'refrigerated' in storage_days:
                    storage_days['room_temperature'] = max(1, storage_days['refrigerated'] // 3)
                else:
                    storage_days['room_temperature'] = 3
            
            # 生成存储建议
            tips = []
            if refrigerate:
                tips.append(f"Refrigerate for {refrigerate['min']}-{refrigerate['max']} {refrigerate['unit'].lower()}")
            if freeze:
                tips.append(f"Freeze for up to {freeze['max']} {freeze['unit'].lower()}")
            if pantry:
                tips.append(f"Store at room temperature for {pantry['min']}-{pantry['max']} {pantry['unit'].lower()}")
            
            food_database[food_key] = {
                'name': name,
                'category': category,
                'storage': storage_days,
                'tips': tips,
                'source': 'USDA'
            }
            
            # 添加中文映射
            chinese_name = chinese_mapping.get(name)
            if chinese_name:
                food_database[chinese_name] = food_database[food_key].copy()
                food_database[chinese_name]['name'] = chinese_name
        
        print(f"✅ 成功转换 {len(food_database)} 种食物数据")
        
        # 保存为 TypeScript 格式
        output_content = f"""// USDA FoodKeeper 数据库
// 数据来源: USDA.gov 官方 FoodKeeper 数据库
// 生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}

export const USDA_FOOD_DATABASE = {json.dumps(food_database, ensure_ascii=False, indent=2)};

export type FoodStorage = {{
  refrigerated: number;
  frozen: number;
  room_temperature: number;
}};

export type FoodItem = {{
  name: string;
  category: string;
  storage: FoodStorage;
  tips: string[];
  source: 'USDA' | 'AI' | 'Manual';
}};
"""
        
        with open('lib/usda-food-database.ts', 'w', encoding='utf-8') as f:
            f.write(output_content)
        
        print("✅ USDA 数据已保存到 'lib/usda-food-database.ts'")
        
        # 生成统计报告
        categories = {}
        for food in food_database.values():
            category = food['category']
            categories[category] = categories.get(category, 0) + 1
        
        print(f"\n📊 数据统计:")
        print(f"总食物数量: {len(food_database)}")
        print(f"食物类别数: {len(categories)}")
        print("\n各类别食物数量:")
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"  {category}: {count} 种")
        
        return food_database
        
    except Exception as e:
        print(f"❌ 集成数据时出错: {e}")
        return None

if __name__ == "__main__":
    print("🍎 USDA FoodKeeper 数据集成工具")
    print("=" * 50)
    
    result = integrate_usda_data()
    
    if result:
        print(f"\n🎉 数据集成完成！")
        print(f"现在可以使用权威的 USDA 数据为用户提供准确的食物存储建议。")
    else:
        print(f"\n❌ 数据集成失败，请检查错误信息。")

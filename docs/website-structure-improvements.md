# HowLongFresh 网站结构改进清单

## 概述

本文档详细列出了 HowLongFresh 项目中需要改进的具体项目，包括优先级、影响范围和实施建议。

## 改进项目清单

### 🚨 高优先级项目

#### 1. 代码清理
- **问题**: 存在多个测试页面未清理
- **影响**: 增加代码维护负担，可能误导开发者
- **需要删除的文件**:
  ```
  app/[locale]/(default)/test-ai/
  app/[locale]/(default)/test-faq-fix/
  app/[locale]/(default)/test-faq-link/
  app/[locale]/(default)/test-migrated-faq/
  app/[locale]/test-categories/
  app/[locale]/test-new-categories/
  app/[locale]/(default)/demo-faq-fix/
  app/[locale]/(default)/auth-test/
  app/[locale]/(default)/auth-debug/
  ```

#### 2. 导航系统完善
- **问题**: FAQ页面缺少到定价页面的链接
- **影响**: 用户无法方便地在页面间导航
- **解决方案**:
  - 在 Header 组件中添加完整的导航链接
  - 确保所有页面都有返回主要页面的链接
  - 实现面包屑导航

#### 3. 国际化修复
- **问题**: 只支持中英文，但配置显示支持10种语言
- **影响**: 误导用户，影响国际化扩展
- **硬编码文本位置**:
  - `components/blocks/hero/food-input-section.tsx`: "Tap to search or choose from examples above"
  - `components/CategoryGrid.tsx`: 分类名称
  - 错误消息和提示文本
- **解决方案**:
  - 移除未实现的语言配置
  - 或完成所有10种语言的翻译

### 📊 中优先级项目

#### 4. 组件命名统一
- **问题**: 命名风格不一致（kebab-case vs PascalCase）
- **影响**: 降低代码可读性和一致性
- **需要重命名的文件**:
  ```
  components/blocks/hero/food-hero.tsx → FoodHero.tsx
  components/blocks/hero/food-input-section.tsx → FoodInputSection.tsx
  components/blocks/hero/food-bg.tsx → FoodBackground.tsx
  components/blocks/hero/food-result.tsx → FoodResult.tsx
  components/blocks/hero/food-scan-animation.tsx → FoodScanAnimation.tsx
  ```

#### 5. 搜索功能统一
- **问题**: 主页搜索和FAQ搜索分离
- **影响**: 用户体验不一致
- **解决方案**:
  - 创建全局搜索组件
  - 支持搜索食物、FAQ、文章
  - 使用 Command/Palette 模式

#### 6. 加载和错误状态
- **问题**: 缺少统一的加载和错误处理UI
- **影响**: 用户体验不佳
- **需要添加的组件**:
  - `components/ui/LoadingState.tsx`
  - `components/ui/ErrorState.tsx`
  - `components/ui/EmptyState.tsx`

### 📈 低优先级但重要的项目

#### 7. 响应式优化
- **问题**: CategoryGrid 在移动端过于拥挤
- **影响**: 移动端用户体验不佳
- **解决方案**:
  - 移动端改为横向滚动
  - 调整网格间距
  - 优化触摸目标大小（最小 44x44px）

#### 8. 性能优化
- **问题**: 未实现图片懒加载和搜索防抖
- **影响**: 页面加载慢，API调用过多
- **需要实现**:
  - 使用 Next.js Image 组件
  - 添加 useDebounce hook
  - 实现搜索结果缓存

#### 9. 无障碍性改进
- **问题**: 缺少 ARIA 标签和键盘导航
- **影响**: 无障碍用户无法正常使用
- **需要添加**:
  - 所有按钮和链接的 aria-label
  - 表单的 aria-describedby
  - 焦点管理和 Tab 顺序

#### 10. 组件职责优化
- **问题**: Blog组件处理FAQ内容
- **影响**: 代码耦合，难以维护
- **解决方案**:
  - 分离 Blog 和 FAQ 组件
  - 创建通用的文章列表组件
  - 明确组件边界

## 技术债务清理

### 需要移除的文件
```bash
# 测试和调试文件
rm -rf app/[locale]/(default)/test-*
rm -rf app/[locale]/test-*
rm -rf app/api/test-*
rm -rf app/test-ai/

# 调试相关
rm -rf app/[locale]/(default)/auth-debug/
rm -rf app/api/debug-posts/
rm -rf debug/
```

### 需要合并的功能
1. **搜索功能**
   - 合并主页搜索和FAQ搜索
   - 创建统一的搜索API

2. **状态管理**
   - 统一使用 Context API
   - 移除冗余的状态管理代码

3. **样式系统**
   - 统一使用 Tailwind CSS
   - 移除内联样式

## 实施建议

### 第一阶段（1-2周）
1. 清理所有测试文件
2. 统一组件命名
3. 修复导航系统

### 第二阶段（2-3周）
1. 完善国际化
2. 统一搜索功能
3. 添加加载/错误状态

### 第三阶段（3-4周）
1. 响应式优化
2. 性能优化
3. 无障碍性改进

## 监控和验证

### 代码质量检查
```bash
# 检查是否还有测试文件
find . -name "*test*" -type f | grep -v node_modules

# 检查硬编码文本
grep -r "Tap to search" --include="*.tsx" --include="*.ts"

# 检查组件命名
ls components/blocks/*/*.tsx | grep -E "[a-z]-[a-z]"
```

### 性能测试
- 使用 Lighthouse 测试性能分数
- 检查 Core Web Vitals
- 测试移动端性能

### 无障碍性测试
- 使用 axe DevTools
- 键盘导航测试
- 屏幕阅读器测试

## 相关文档
- [主优化计划](./howlongfresh-optimization-plan.md)
- [组件规范](./component-guidelines.md)
- [CLAUDE.md](../CLAUDE.md)

## 更新日志
- 2025-07-24: 初始版本，基于网站结构分析创建
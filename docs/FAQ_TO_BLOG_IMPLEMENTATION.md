# FAQ数据转博客实现指南

## 项目概述
将processed_faq_data_20250721_065602.json中的75个FAQ数据导入到博客系统中，支持中英双语展示。

## 实现进度

### ✅ 已完成的任务

1. **分析FAQ数据结构**
   - 数据总量：75个FAQ
   - 分类分布：
     - 食品安全（food_safety）：25个
     - 冷冻食品（frozen_foods）：25个
     - 冷藏食品（refrigerated_foods）：20个
     - 有效期（expiration_dates）：3个
     - 其他类别：2个

2. **创建数据导入脚本**
   - 文件：`/scripts/import-faq-to-blog.js`
   - 功能：读取FAQ数据并转换为博客格式
   - 支持中英双语（每个FAQ生成2篇文章）

3. **创建数据库迁移文件**
   - 文件：`/supabase/migrations/003_create_posts_table.sql`
   - 包含posts表结构和索引

### 🔄 进行中的任务

1. **创建posts表**
   - 需要在Supabase Dashboard中手动执行SQL
   
2. **导入FAQ数据**
   - 等待posts表创建完成后执行

### 📋 待完成的任务

1. **优化博客UI**
   - 添加分类图标
   - 优化FAQ内容展示格式
   
2. **添加分类筛选功能**
   - 在博客列表页添加分类过滤器
   - 支持按分类浏览FAQ

3. **集成到主导航**
   - 添加"Food Safety FAQ"菜单项
   - 在首页添加热门FAQ模块

## 技术实现细节

### 数据转换映射

```javascript
{
  uuid: 生成唯一ID,
  slug: 基于question生成SEO友好的URL,
  title: FAQ的question,
  description: answer_summary,
  content: 格式化的Markdown内容,
  status: "online",
  locale: "en" 或 "zh",
  author_name: "HowLongFresh Expert",
  cover_url: 根据category选择图片,
  created_at: 当前时间
}
```

### 分类封面图片映射

```javascript
{
  'food_safety': '/imgs/blog/food-safety-cover.jpg',
  'frozen_foods': '/imgs/blog/frozen-foods-cover.jpg', 
  'refrigerated_foods': '/imgs/blog/refrigerated-foods-cover.jpg',
  'expiration_dates': '/imgs/blog/expiration-dates-cover.jpg',
  'preparation': '/imgs/blog/food-preparation-cover.jpg',
  'storage_tips': '/imgs/blog/storage-tips-cover.jpg'
}
```

## 操作步骤

### 步骤1：创建posts表

1. 登录 [Supabase Dashboard](https://app.supabase.com)
2. 选择项目 (emopvngdwwghndzcfxvw)
3. 点击 SQL Editor → New Query
4. 执行 `/supabase/migrations/003_create_posts_table.sql` 中的SQL

### 步骤2：导入FAQ数据

```bash
node scripts/import-faq-to-blog.js
```

### 步骤3：查看结果

访问 http://localhost:3000/posts 查看博客列表

## 文件结构

```
/scripts/
  ├── import-faq-to-blog.js        # FAQ数据导入脚本
  ├── create-posts-table.sql       # Posts表SQL
  └── data/
      └── processed_faq_data_20250721_065602.json  # 原始FAQ数据

/supabase/migrations/
  └── 003_create_posts_table.sql   # Posts表迁移文件

/app/[locale]/(default)/posts/
  ├── page.tsx                     # 博客列表页
  └── [slug]/
      └── page.tsx                 # 博客详情页

/components/blocks/
  ├── blog/                        # 博客列表组件
  └── blog-detail/                 # 博客详情组件
```

## 后续优化建议

1. **性能优化**
   - 实现分页加载
   - 添加搜索功能
   - 实现静态页面生成

2. **用户体验**
   - 添加相关FAQ推荐
   - 实现评论功能
   - 添加收藏功能

3. **SEO优化**
   - 生成sitemap
   - 优化meta标签
   - 实现结构化数据

## 常见问题

### Q: 为什么需要手动在Supabase中执行SQL？
A: 由于Supabase的安全限制，DDL语句（如CREATE TABLE）不能通过API执行，必须在Dashboard中手动执行。

### Q: 如何修改FAQ内容格式？
A: 编辑 `/scripts/import-faq-to-blog.js` 中的 `formatMarkdownContent` 函数。

### Q: 如何添加新的FAQ分类？
A: 在导入脚本中更新 `categoryCoverImages` 和 `categoryTranslations` 对象。

## 相关资源

- [Supabase文档](https://supabase.com/docs)
- [Next.js博客示例](https://nextjs.org/learn/basics/data-fetching)
- [Markdown渲染组件](https://github.com/remarkjs/react-markdown)
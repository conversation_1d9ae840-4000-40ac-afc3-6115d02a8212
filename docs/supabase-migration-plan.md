# Supabase 数据库迁移方案

## 🎯 目标

将当前的本地食物数据库迁移到 Supabase，实现：
1. **权威数据存储**: 使用 USDA FoodKeeper 数据作为主要数据源
2. **云端数据库**: 利用 Supabase PostgreSQL 数据库
3. **实时同步**: 支持数据的实时更新和查询
4. **多语言支持**: 中英文食物名称映射

## 📊 当前实现分析

### 现有数据结构
```typescript
// 当前本地实现
const FOOD_DATABASE: Record<string, {
  name: string;
  category: string;
  storage: {
    refrigerated: number; // 天数
    frozen: number;       // 天数
    room_temperature: number; // 天数
  };
  tips: string[];
}> = {
  'apple': {
    name: 'Apple',
    category: 'Fruit',
    storage: { refrigerated: 30, frozen: 365, room_temperature: 7 },
    tips: ['Store in refrigerator crisper drawer', ...]
  }
};
```

### 查询优先级
1. **USDA 数据库** (98% 置信度) - 最高优先级
2. **本地补充数据** (90% 置信度) - 亚洲特色食物
3. **AI 图像识别** (70-95% 置信度) - 备用方案

## 🗄️ Supabase 数据库设计

### 表结构设计

#### 1. food_categories (食物类别表)
```sql
CREATE TABLE food_categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. foods (主要食物表)
```sql
CREATE TABLE foods (
  id SERIAL PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  search_key VARCHAR(200) NOT NULL UNIQUE,
  category_id INTEGER REFERENCES food_categories(id),
  
  -- 存储时间（天数）
  refrigerated_days INTEGER,
  frozen_days INTEGER,
  room_temperature_days INTEGER,
  
  -- 存储建议
  storage_tips TEXT[],
  
  -- 数据来源和置信度
  source VARCHAR(20) DEFAULT 'USDA',
  confidence DECIMAL(3,2) DEFAULT 0.98,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 3. food_aliases (食物别名表)
```sql
CREATE TABLE food_aliases (
  id SERIAL PRIMARY KEY,
  food_id INTEGER REFERENCES foods(id) ON DELETE CASCADE,
  alias VARCHAR(200) NOT NULL,
  language VARCHAR(10) DEFAULT 'en',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 索引优化
```sql
CREATE INDEX idx_foods_search_key ON foods(search_key);
CREATE INDEX idx_foods_category ON foods(category_id);
CREATE INDEX idx_food_aliases_alias ON food_aliases(alias);
CREATE INDEX idx_food_aliases_language ON food_aliases(language);
```

## 🔄 迁移策略

### 阶段一：数据库设置
1. **使用 Supabase Management API** 创建表结构
2. **插入基础类别数据**
3. **设置索引和约束**

### 阶段二：数据迁移
1. **USDA 数据转换**: 将 230+ 种 USDA 食物数据导入
2. **中文映射**: 建立中英文别名关系
3. **本地补充**: 添加亚洲特色食物

### 阶段三：API 集成
1. **更新查询逻辑**: 优先查询 Supabase 数据库
2. **保留本地备份**: 作为离线备用方案
3. **性能优化**: 实现缓存机制

## 🛠️ 技术实现

### Supabase 配置
```typescript
// 环境变量
NEXT_PUBLIC_SUPABASE_URL=https://plefidqreqjnesamigoc.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 客户端配置
import { createClient } from '@supabase/supabase-js'
const supabase = createClient(supabaseUrl, supabaseKey)
```

### 查询函数
```typescript
async function searchFood(query: string): Promise<FoodResult | null> {
  // 1. 精确匹配
  const { data } = await supabase
    .from('foods')
    .select(`
      name, category_id,
      refrigerated_days, frozen_days, room_temperature_days,
      storage_tips, source, confidence,
      food_categories(name)
    `)
    .eq('search_key', query.toLowerCase())
    .single();

  // 2. 别名匹配
  if (!data) {
    const { data: aliasData } = await supabase
      .from('food_aliases')
      .select(`
        foods(
          name, refrigerated_days, frozen_days, room_temperature_days,
          storage_tips, source, confidence,
          food_categories(name)
        )
      `)
      .eq('alias', query)
      .single();
  }

  return data ? formatFoodResult(data) : null;
}
```

## 📈 优势分析

### 性能优势
- **数据库索引**: 快速查询和模糊匹配
- **关系型结构**: 规范化数据存储
- **实时同步**: 支持数据的实时更新

### 扩展性优势
- **云端存储**: 无限扩展能力
- **多用户支持**: 支持并发访问
- **数据备份**: 自动备份和恢复

### 维护优势
- **统一管理**: 集中式数据管理
- **版本控制**: 数据变更历史
- **权限控制**: 细粒度访问控制

## 🚀 实施计划

### 第一步：数据库初始化 (1天)
- [ ] 使用 Supabase Management API 创建表结构
- [ ] 插入基础类别数据
- [ ] 设置必要的索引

### 第二步：数据迁移 (2天)
- [ ] 转换 USDA 数据格式
- [ ] 批量导入食物数据
- [ ] 建立中英文别名映射

### 第三步：API 集成 (1天)
- [ ] 更新查询逻辑
- [ ] 实现错误处理和降级
- [ ] 性能测试和优化

### 第四步：测试验证 (1天)
- [ ] 功能测试
- [ ] 性能测试
- [ ] 用户体验测试

## 💡 实施建议

### 渐进式迁移
1. **保留现有系统**: 作为备用方案
2. **逐步切换**: 先测试后上线
3. **监控性能**: 实时监控查询性能

### 数据质量保证
1. **数据验证**: 确保数据完整性
2. **一致性检查**: 验证中英文映射
3. **定期更新**: 同步最新 USDA 数据

### 用户体验优化
1. **缓存策略**: 减少数据库查询
2. **错误处理**: 优雅的降级机制
3. **响应时间**: 保持快速响应

## 🎯 预期效果

### 数据质量提升
- **权威性**: 基于 USDA 官方数据
- **完整性**: 230+ 种食物覆盖
- **准确性**: 98% 置信度

### 系统性能提升
- **查询速度**: 数据库索引优化
- **并发能力**: 云端数据库支持
- **可扩展性**: 无限扩展能力

### 维护效率提升
- **集中管理**: 统一的数据管理
- **自动备份**: 数据安全保障
- **实时更新**: 支持动态数据更新

这个迁移方案将显著提升 HowLongFresh.site 的数据质量、系统性能和维护效率，为用户提供更准确、更可靠的食物保鲜期查询服务。

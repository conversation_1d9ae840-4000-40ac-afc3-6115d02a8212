# HowLongFresh 组件开发规范

## 概述

本文档定义了 HowLongFresh 项目的组件开发标准和最佳实践，确保代码的一致性、可维护性和可扩展性。

## 命名规范

### 文件命名
- **组件文件**: 使用 PascalCase（首字母大写）
  ```
  ✅ FoodHero.tsx
  ✅ CategoryGrid.tsx
  ✅ LoadingSpinner.tsx
  
  ❌ food-hero.tsx
  ❌ category_grid.tsx
  ```

- **工具函数文件**: 使用 kebab-case（小写连字符）
  ```
  ✅ use-debounce.ts
  ✅ format-date.ts
  
  ❌ useDebounce.ts
  ❌ formatDate.ts
  ```

- **样式文件**: 使用 kebab-case
  ```
  ✅ food-hero.module.css
  ✅ global-styles.css
  ```

### 组件命名
- **React组件**: 使用 PascalCase
  ```typescript
  // ✅ 正确
  export function FoodHero() { }
  export const CategoryGrid = () => { }
  
  // ❌ 错误
  export function foodHero() { }
  export const category_grid = () => { }
  ```

- **Props接口**: 组件名 + Props
  ```typescript
  interface FoodHeroProps { }
  interface CategoryGridProps { }
  ```

### 变量和函数命名
- **变量**: 使用 camelCase
  ```typescript
  const userName = "John";
  const isLoading = true;
  ```

- **常量**: 使用 UPPER_SNAKE_CASE
  ```typescript
  const MAX_FILE_SIZE = 5 * 1024 * 1024;
  const API_TIMEOUT = 30000;
  ```

- **函数**: 使用 camelCase，动词开头
  ```typescript
  function handleClick() { }
  function fetchUserData() { }
  function validateInput() { }
  ```

## 文件组织结构

### 组件目录结构
```
components/
├── blocks/           # 页面级组件块
│   ├── hero/        # Hero区域相关组件
│   │   ├── FoodHero.tsx
│   │   ├── FoodInputSection.tsx
│   │   └── index.ts
│   └── footer/
├── ui/              # 基础UI组件
│   ├── Button.tsx
│   ├── Input.tsx
│   └── LoadingSpinner.tsx
├── shared/          # 共享业务组件
│   ├── FoodCard.tsx
│   └── CategoryBadge.tsx
└── layouts/         # 布局组件
    ├── PageLayout.tsx
    └── SectionLayout.tsx
```

### 单个组件文件结构
```typescript
// 1. 导入语句
import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/Button';

// 2. 类型定义
interface FoodHeroProps {
  title: string;
  description?: string;
  onSearch: (query: string) => void;
}

// 3. 组件定义
export function FoodHero({ title, description, onSearch }: FoodHeroProps) {
  // 4. Hooks
  const t = useTranslations('hero');
  const [searchQuery, setSearchQuery] = useState('');
  
  // 5. 副作用
  useEffect(() => {
    // ...
  }, []);
  
  // 6. 事件处理函数
  const handleSearch = () => {
    onSearch(searchQuery);
  };
  
  // 7. 渲染逻辑
  return (
    <section className="py-24">
      {/* 组件内容 */}
    </section>
  );
}

// 8. 子组件（如果有）
function SearchInput() {
  // ...
}
```

## TypeScript 使用规范

### 始终定义 Props 类型
```typescript
// ✅ 正确
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  children: React.ReactNode;
}

export function Button({ variant = 'primary', size = 'md', onClick, children }: ButtonProps) {
  // ...
}

// ❌ 错误
export function Button({ variant, size, onClick, children }: any) {
  // ...
}
```

### 使用枚举或联合类型
```typescript
// 使用联合类型
type FoodCategory = 'dairy' | 'meat' | 'vegetable' | 'fruit';

// 或使用枚举
enum StorageType {
  Refrigerated = 'refrigerated',
  Frozen = 'frozen',
  RoomTemperature = 'room_temperature'
}
```

### 避免使用 any
```typescript
// ✅ 正确
function processData(data: unknown): string {
  if (typeof data === 'string') {
    return data;
  }
  return String(data);
}

// ❌ 错误
function processData(data: any): any {
  return data;
}
```

## 样式规范

### 使用 Tailwind CSS
```typescript
// ✅ 正确 - 使用 Tailwind 类
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">

// ❌ 错误 - 避免内联样式
<div style={{ display: 'flex', padding: '16px' }}>
```

### 响应式设计
```typescript
// 移动优先的响应式类
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* 移动端1列，平板2列，桌面3列 */}
</div>
```

### 条件样式
```typescript
// 使用 clsx 或 cn 工具函数
import { cn } from '@/lib/utils';

<button
  className={cn(
    "px-4 py-2 rounded-md transition-colors",
    "hover:bg-gray-100 focus:outline-none focus:ring-2",
    {
      "bg-blue-500 text-white": variant === 'primary',
      "bg-gray-200 text-gray-800": variant === 'secondary',
      "opacity-50 cursor-not-allowed": disabled
    }
  )}
>
```

## 国际化规范

### 始终使用翻译
```typescript
// ✅ 正确
import { useTranslations } from 'next-intl';

export function FoodHero() {
  const t = useTranslations('hero');
  
  return (
    <h1>{t('title')}</h1>
  );
}

// ❌ 错误 - 硬编码文本
export function FoodHero() {
  return (
    <h1>Check Food Freshness</h1>
  );
}
```

### 服务端组件的翻译
```typescript
import { getTranslations } from 'next-intl/server';

export default async function Page() {
  const t = await getTranslations('page');
  
  return <h1>{t('title')}</h1>;
}
```

## 性能优化

### 使用 React.memo
```typescript
// 对于纯展示组件
export const FoodCard = React.memo(function FoodCard({ food }: FoodCardProps) {
  return (
    <div className="card">
      {/* 内容 */}
    </div>
  );
});
```

### 懒加载组件
```typescript
// 对于大型组件
const HeavyComponent = dynamic(
  () => import('@/components/HeavyComponent'),
  { 
    loading: () => <LoadingSpinner />,
    ssr: false 
  }
);
```

### 优化列表渲染
```typescript
// 使用唯一且稳定的 key
{foods.map((food) => (
  <FoodCard key={food.id} food={food} />
))}

// 避免使用 index 作为 key（除非列表不会重新排序）
```

## 无障碍性规范

### 语义化 HTML
```typescript
// ✅ 正确
<nav aria-label="Main navigation">
  <ul>
    <li><a href="/home">Home</a></li>
  </ul>
</nav>

// ❌ 错误
<div onClick={handleClick}>Home</div>
```

### ARIA 标签
```typescript
<button
  aria-label="Search for food"
  aria-pressed={isSearching}
  aria-describedby="search-help"
>
  <SearchIcon />
</button>
<span id="search-help" className="sr-only">
  Enter food name or upload an image
</span>
```

### 键盘导航
```typescript
// 确保所有交互元素可通过键盘访问
<div
  role="button"
  tabIndex={0}
  onKeyDown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      handleClick();
    }
  }}
  onClick={handleClick}
>
```

## 错误处理

### 使用 Error Boundaries
```typescript
// components/ErrorBoundary.tsx
export class ErrorBoundary extends React.Component<Props, State> {
  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Component error:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    
    return this.props.children;
  }
}
```

### 处理异步错误
```typescript
// 使用 try-catch
const fetchData = async () => {
  try {
    setLoading(true);
    const data = await api.getFoodData();
    setFoodData(data);
  } catch (error) {
    setError(error.message);
    // 显示用户友好的错误信息
    toast.error(t('errors.fetch_failed'));
  } finally {
    setLoading(false);
  }
};
```

## 测试规范

### 组件测试文件命名
```
FoodHero.tsx
FoodHero.test.tsx  // 单元测试
FoodHero.stories.tsx  // Storybook 故事
```

### 测试用例结构
```typescript
describe('FoodHero', () => {
  it('should render title correctly', () => {
    // ...
  });
  
  it('should handle search input', () => {
    // ...
  });
  
  it('should be accessible', () => {
    // 测试无障碍性
  });
});
```

## 文档规范

### 组件文档
```typescript
/**
 * FoodHero - 主页的 Hero 区域组件
 * 
 * @example
 * ```tsx
 * <FoodHero 
 *   title="Check Food Freshness"
 *   onSearch={handleSearch}
 * />
 * ```
 * 
 * @param props - 组件属性
 * @param props.title - 标题文本
 * @param props.description - 可选的描述文本
 * @param props.onSearch - 搜索回调函数
 */
export function FoodHero(props: FoodHeroProps) {
  // ...
}
```

### Props 文档
```typescript
interface FoodHeroProps {
  /** 主标题文本 */
  title: string;
  
  /** 可选的描述文本 */
  description?: string;
  
  /** 当用户执行搜索时触发 */
  onSearch: (query: string) => void;
  
  /** 是否显示加载状态 */
  isLoading?: boolean;
}
```

## 代码审查清单

提交代码前，请确保：

- [ ] 组件和文件命名符合规范
- [ ] TypeScript 类型定义完整
- [ ] 没有硬编码的文本（使用 i18n）
- [ ] 响应式设计已实现
- [ ] 无障碍性要求已满足
- [ ] 错误处理已添加
- [ ] 代码已格式化（prettier）
- [ ] 无 console.log 或调试代码
- [ ] 组件有适当的注释和文档

## 更新日志

- 2025-07-24: 初始版本发布
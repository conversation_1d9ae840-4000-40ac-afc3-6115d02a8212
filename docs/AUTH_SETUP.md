# 认证系统设置指南

## 功能概述

已实现的认证功能：
- ✅ Google OAuth 登录
- ✅ 邮箱密码登录注册
- ✅ 邮箱验证
- ✅ 密码重置
- ✅ 多语言支持

## 快速开始

### 1. 配置环境变量

复制 `.env.local.example` 文件并重命名为 `.env.local`：

```bash
cp .env.local.example .env.local
```

### 2. 配置 Google OAuth

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API
4. 创建 OAuth 2.0 凭据
5. 添加授权重定向 URI：
   - 开发环境：`http://localhost:3000/api/auth/callback/google`
   - 生产环境：`https://yourdomain.com/api/auth/callback/google`
6. 将凭据填入 `.env.local`

### 3. 配置邮件服务（Resend）

1. 注册 [Resend](https://resend.com/) 账号
2. 获取 API Key
3. 验证发送域名
4. 将配置填入 `.env.local`：
   ```
   RESEND_API_KEY=re_your_api_key
   EMAIL_FROM=HowLongFresh <<EMAIL>>
   ```

### 4. 运行数据库迁移

在 Supabase 中执行迁移文件：

```sql
-- 执行文件：/supabase/migrations/002_create_auth_tables.sql
```

### 5. 测试认证流程

1. 启动开发服务器：
   ```bash
   pnpm dev
   ```

2. 测试各功能：
   - 访问 `/auth/signin` - 登录页面
   - 访问 `/auth/signup` - 注册页面
   - 测试邮箱注册和验证流程
   - 测试密码重置流程
   - 测试 Google 登录

## API 端点

- `POST /api/auth/register` - 用户注册
- `GET/POST /api/auth/verify-email` - 邮箱验证
- `POST /api/auth/forgot-password` - 发送重置密码邮件
- `GET/POST /api/auth/reset-password` - 重置密码

## 安全最佳实践

1. **密码要求**：
   - 至少 8 个字符
   - 包含大写字母
   - 包含小写字母
   - 包含数字

2. **令牌过期时间**：
   - 邮箱验证：24 小时
   - 密码重置：24 小时

3. **防止信息泄露**：
   - 忘记密码功能始终返回成功，防止邮箱枚举
   - 错误信息保持通用，不透露具体细节

## 自定义配置

### 修改邮件模板

编辑 `/lib/email.ts` 中的邮件模板函数：
- `sendVerificationEmail` - 验证邮件
- `sendPasswordResetEmail` - 密码重置邮件
- `sendWelcomeEmail` - 欢迎邮件

### 添加其他 OAuth 提供商

在 `/auth/config.ts` 中添加新的提供商，例如 GitHub：

```typescript
if (process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true") {
  providers.push(
    GitHubProvider({
      clientId: process.env.AUTH_GITHUB_ID,
      clientSecret: process.env.AUTH_GITHUB_SECRET,
    })
  );
}
```

## 故障排除

1. **邮件发送失败**：
   - 检查 Resend API Key 是否正确
   - 确认发送域名已验证
   - 查看控制台错误日志

2. **Google 登录失败**：
   - 确认 OAuth 凭据正确
   - 检查重定向 URI 是否匹配
   - 确保启用了相关 API

3. **数据库错误**：
   - 确认已执行所有迁移
   - 检查 Supabase 连接配置
   - 查看 Supabase 日志
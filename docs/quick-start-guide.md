# HowLongFresh.site 快速开始指南

## 🚀 立即开始

### 第一步：创建核心类型定义

```bash
# 创建类型定义文件
mkdir -p types/blocks
touch types/blocks/food-hero.d.ts
touch types/food.d.ts
```

**types/blocks/food-hero.d.ts**
```typescript
import { Hero } from "@/types/blocks/hero";

export interface SearchSection {
  placeholder: string;
  examples: string[];
  separator_text: string;
}

export interface UploadSection {
  title: string;
  accept: string;
  max_size: string;
}

export interface TrustStat {
  title: string;
  value: string;
  icon?: string;
}

export interface FoodHero extends Hero {
  search_section?: SearchSection;
  upload_section?: UploadSection;
  trust_stats?: TrustStat[];
}
```

**types/food.d.ts**
```typescript
export interface FoodResult {
  name: string;
  category: string;
  storage: {
    refrigerated: number;
    frozen: number;
    room_temperature: number;
  };
  tips: string[];
  confidence: number;
  image_url?: string;
}

export interface FoodQuery {
  type: 'text' | 'image';
  content: string | File;
  timestamp: Date;
}
```

### 第二步：更新国际化配置

**i18n/pages/landing/en.json** (在 hero 部分添加)
```json
{
  "hero": {
    "title": "Know How Long Fresh — AI Food Shelf-Life in Seconds",
    "highlight_text": "How Long Fresh",
    "description": "Upload a photo or enter food name to get instant storage duration for refrigerated, frozen, and room temperature conditions.",
    "search_section": {
      "placeholder": "Enter food name (e.g., apple, milk, bread)",
      "examples": ["Apple", "Milk", "Bread", "Chicken", "Lettuce"],
      "separator_text": "OR"
    },
    "upload_section": {
      "title": "Upload Photo",
      "accept": "image/*",
      "max_size": "5MB"
    },
    "trust_stats": [
      {"title": "Foods in Database", "value": "5,000+", "icon": "RiDatabase2Line"},
      {"title": "Families Helped", "value": "10,000+", "icon": "RiGroupLine"},
      {"title": "AI Accuracy", "value": "95%+", "icon": "RiAiGenerate"},
      {"title": "Annual Savings", "value": "$400", "icon": "RiMoneyDollarCircleLine"}
    ],
    "buttons": [
      {
        "title": "Check Freshness",
        "icon": "RiSearchLine",
        "url": "#",
        "target": "_self",
        "variant": "default"
      }
    ]
  }
}
```

### 第三步：创建基础组件

**components/blocks/hero/food-input-section.tsx**
```typescript
"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { SearchSection, UploadSection } from "@/types/blocks/food-hero";

interface FoodInputSectionProps {
  searchSection?: SearchSection;
  uploadSection?: UploadSection;
  onSearch?: (query: string | File) => void;
}

export default function FoodInputSection({ 
  searchSection, 
  uploadSection, 
  onSearch 
}: FoodInputSectionProps) {
  const [searchValue, setSearchValue] = useState("");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const handleSubmit = () => {
    if (searchValue.trim()) {
      onSearch?.(searchValue.trim());
    } else if (uploadedFile) {
      onSearch?.(uploadedFile);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  return (
    <div className="mt-8 flex flex-col gap-4 sm:flex-row sm:gap-6 max-w-4xl mx-auto">
      {/* 文本输入 */}
      <div className="flex-1">
        <Input
          type="text"
          placeholder={searchSection?.placeholder || "Enter food name"}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSubmit()}
          className="h-12 text-base"
        />
        {searchSection?.examples && (
          <div className="mt-2 flex flex-wrap gap-2">
            {searchSection.examples.slice(0, 3).map((example, index) => (
              <button
                key={index}
                onClick={() => setSearchValue(example)}
                className="text-xs px-2 py-1 bg-muted rounded-full hover:bg-muted/80 transition-colors"
              >
                {example}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* 分隔符 */}
      <div className="flex items-center justify-center px-4">
        <span className="text-muted-foreground font-medium text-sm">
          {searchSection?.separator_text || "OR"}
        </span>
      </div>

      {/* 文件上传 */}
      <div className="flex-1">
        <label className="block">
          <input
            type="file"
            accept={uploadSection?.accept || "image/*"}
            onChange={handleFileChange}
            className="hidden"
          />
          <div className="h-12 border-2 border-dashed border-muted-foreground/25 rounded-md flex items-center justify-center cursor-pointer hover:border-muted-foreground/50 transition-colors">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Icon name="RiImageLine" className="w-4 h-4" />
              <span className="text-sm">
                {uploadedFile ? uploadedFile.name : (uploadSection?.title || "Upload Photo")}
              </span>
            </div>
          </div>
        </label>
        {uploadSection?.max_size && (
          <p className="mt-1 text-xs text-muted-foreground">
            Max size: {uploadSection.max_size}
          </p>
        )}
      </div>

      {/* 提交按钮 */}
      <Button 
        size="lg" 
        onClick={handleSubmit}
        disabled={!searchValue.trim() && !uploadedFile}
        className="h-12 px-8"
      >
        Check Freshness
        <Icon name="RiSearchLine" className="ml-2 w-4 h-4" />
      </Button>
    </div>
  );
}
```

**components/blocks/hero/trust-indicators.tsx**
```typescript
import Icon from "@/components/icon";
import { TrustStat } from "@/types/blocks/food-hero";

interface TrustIndicatorsProps {
  stats?: TrustStat[];
}

export default function TrustIndicators({ stats }: TrustIndicatorsProps) {
  if (!stats || stats.length === 0) return null;

  return (
    <div className="mt-12 pt-8 border-t border-muted">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="text-center">
            {stat.icon && (
              <div className="mb-2 flex justify-center">
                <Icon name={stat.icon} className="w-6 h-6 text-primary" />
              </div>
            )}
            <div className="text-2xl font-bold text-foreground">{stat.value}</div>
            <div className="text-sm text-muted-foreground">{stat.title}</div>
          </div>
        ))}
      </div>
    </div>
  );
}
```

### 第四步：创建主 Hero 组件

**components/blocks/hero/food-hero.tsx**
```typescript
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import HappyUsers from "./happy-users";
import HeroBg from "./bg";
import FoodInputSection from "./food-input-section";
import TrustIndicators from "./trust-indicators";
import { FoodHero as FoodHeroType } from "@/types/blocks/food-hero";
import Icon from "@/components/icon";
import Link from "next/link";

export default function FoodHero({ hero }: { hero: FoodHeroType }) {
  if (hero.disabled) {
    return null;
  }

  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }

  const handleFoodSearch = (query: string | File) => {
    console.log("Searching for:", query);
    // TODO: 实现食物搜索逻辑
  };

  return (
    <>
      <HeroBg />
      <section className="py-24">
        <div className="container">
          {hero.show_badge && (
            <div className="flex items-center justify-center mb-8">
              <img
                src="/imgs/badges/phdaily.svg"
                alt="phdaily"
                className="h-10 object-cover"
              />
            </div>
          )}
          
          <div className="text-center">
            {hero.announcement && (
              <a
                href={hero.announcement.url}
                className="mx-auto mb-3 inline-flex items-center gap-3 rounded-full border px-2 py-1 text-sm"
              >
                {hero.announcement.label && (
                  <Badge>{hero.announcement.label}</Badge>
                )}
                {hero.announcement.title}
              </a>
            )}

            {texts && texts.length > 1 ? (
              <h1 className="mx-auto mb-3 mt-4 max-w-4xl text-balance text-4xl font-bold lg:mb-7 lg:text-7xl">
                {texts[0]}
                <span className="bg-gradient-to-r from-green-600 via-green-500 to-green-600 bg-clip-text text-transparent">
                  {highlightText}
                </span>
                {texts[1]}
              </h1>
            ) : (
              <h1 className="mx-auto mb-3 mt-4 max-w-4xl text-balance text-4xl font-bold lg:mb-7 lg:text-7xl">
                {hero.title}
              </h1>
            )}

            <p
              className="mx-auto max-w-3xl text-muted-foreground lg:text-xl"
              dangerouslySetInnerHTML={{ __html: hero.description || "" }}
            />

            <FoodInputSection
              searchSection={hero.search_section}
              uploadSection={hero.upload_section}
              onSearch={handleFoodSearch}
            />

            {hero.tip && (
              <p className="mt-8 text-md text-muted-foreground">{hero.tip}</p>
            )}
            
            {hero.show_happy_users && <HappyUsers />}
            
            <TrustIndicators stats={hero.trust_stats} />
          </div>
        </div>
      </section>
    </>
  );
}
```

### 第五步：更新主页面

**app/[locale]/(default)/page.tsx** (修改 Hero 部分)
```typescript
import FoodHero from "@/components/blocks/hero/food-hero";
// ... 其他导入

export default async function Home({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);

  return (
    <>
      {page.hero && <FoodHero hero={page.hero} />}
      {/* ... 其他组件 */}
    </>
  );
}
```

## 🎨 快速样式定制

**app/theme.css** (添加食物主题色)
```css
:root {
  /* 原有变量... */
  
  /* 食物主题色 */
  --food-primary: 142 76% 36%;    /* 新鲜绿色 */
  --food-secondary: 142 76% 90%;  /* 淡绿色 */
  --food-accent: 142 76% 95%;     /* 极淡绿色 */
}

.food-gradient {
  background: linear-gradient(135deg, hsl(var(--food-accent)) 0%, hsl(var(--background)) 100%);
}
```

## 🧪 测试运行

```bash
# 启动开发服务器
pnpm dev

# 访问 http://localhost:3000 查看效果
```

## 📝 下一步

1. **测试基础功能**：确保组件正常渲染和交互
2. **添加样式优化**：实现渐变背景和动画效果
3. **集成 API**：连接食物识别服务
4. **优化移动端**：调整响应式布局
5. **性能优化**：添加懒加载和缓存

## 🔧 常见问题

**Q: 组件不显示？**
A: 检查 i18n 配置是否正确，确保 hero 对象包含必要字段

**Q: 图标不显示？**
A: 确认图标名称正确，检查 `components/icon/index.tsx` 是否支持该图标

**Q: 样式不生效？**
A: 检查 Tailwind CSS 类名是否正确，确认自定义 CSS 已正确导入

---

🎉 **恭喜！** 你已经完成了 HowLongFresh.site 的基础搭建。继续按照完整的实施计划逐步完善功能。

# HowLongFresh.site 用户指南

## 🌟 欢迎使用 HowLongFresh.site

HowLongFresh.site 是一个基于 AI 的食物保鲜期查询网站，帮助您了解食物在不同存储条件下的保鲜时间，减少食物浪费。

## 🚀 主要功能

### 1. 文本搜索
- **支持语言**：中文和英文
- **支持食物**：15+ 种常见食物
- **示例**：苹果、香蕉、牛奶、鸡肉、面包等

### 2. AI 图像识别
- **技术支持**：Google Gemini 2.5 Flash
- **支持格式**：JPEG, PNG, WebP
- **文件大小**：最大 5MB
- **识别准确率**：95%+

### 3. 详细存储信息
- **冷藏存储**：冰箱冷藏室（2-4°C）
- **冷冻存储**：冰箱冷冻室（-18°C）
- **常温存储**：室温环境（18-22°C）

## 📱 使用方法

### 方式一：文本输入
1. 在搜索框中输入食物名称
2. 可以点击示例关键词快速输入
3. 按回车键或点击"检测保鲜期"按钮
4. 查看详细的存储建议

### 方式二：图片上传
1. 点击"上传照片"区域或拖拽图片
2. 选择清晰的食物照片
3. 等待 AI 分析（约 3-5 秒）
4. 查看识别结果和存储建议

## 🎯 最佳使用建议

### 图片拍摄技巧
- ✅ **良好光照**：确保食物清晰可见
- ✅ **主体突出**：食物占据图片主要部分
- ✅ **角度合适**：正面或 45° 角拍摄
- ✅ **背景简洁**：避免复杂背景干扰

### 支持的食物类型
- 🍎 **水果**：苹果、香蕉、橙子等
- 🥬 **蔬菜**：生菜、胡萝卜、西红柿等
- 🥛 **乳制品**：牛奶、奶酪等
- 🍖 **肉类**：鸡肉、牛肉等
- 🍞 **烘焙食品**：面包等

## 📊 结果解读

### 存储时间说明
- **天数**：表示在该条件下的最佳食用期
- **颜色标识**：
  - 🔴 红色：不推荐该存储方式
  - 🟡 黄色：短期存储（1-2天）
  - 🟢 绿色：适合存储（3-30天）
  - 🔵 蓝色：长期存储（30天以上）

### AI 置信度
- **95%+**：识别准确，建议可信
- **80-94%**：识别较准确，建议参考
- **60-79%**：识别不确定，建议谨慎
- **<60%**：建议重新拍照或使用文本搜索

## 🔧 测试和调试

### 测试页面
访问 [http://localhost:3000/en/test-ai](http://localhost:3000/en/test-ai) 进行功能测试：

1. **文本 API 测试**：验证本地数据库查询
2. **图像 API 测试**：验证 AI 图像识别
3. **连接测试**：验证 OpenRouter API 状态

### 常见问题

**Q: 图片识别失败怎么办？**
A: 
- 检查图片格式（支持 JPEG, PNG, WebP）
- 确保文件大小小于 5MB
- 尝试更清晰的照片
- 使用文本搜索作为备选

**Q: 识别结果不准确？**
A: 
- 查看 AI 置信度分数
- 尝试不同角度的照片
- 确保食物清晰可见
- 参考多个来源验证

**Q: 支持哪些语言？**
A: 
- 界面支持中文和英文
- 食物名称支持中英文输入
- 结果显示根据界面语言调整

## 🛡️ 安全和隐私

### 数据安全
- 图片仅用于识别，不会存储
- API 调用通过加密传输
- 不收集个人身份信息

### 使用建议
- 仅供参考，不替代专业建议
- 食物安全以实际情况为准
- 如有疑问请咨询专业人士

## 📞 技术支持

### 开发信息
- **技术栈**：Next.js + React + TypeScript
- **AI 模型**：Google Gemini 2.5 Flash
- **API 服务**：OpenRouter

### 反馈渠道
如遇到问题或有改进建议，请通过以下方式联系：
- GitHub Issues
- 邮件反馈
- 在线客服

## 🎉 开始使用

现在就访问 [http://localhost:3000](http://localhost:3000) 开始体验 HowLongFresh.site 的强大功能吧！

---

*让 AI 帮助您减少食物浪费，享受更健康的生活！* 🌱

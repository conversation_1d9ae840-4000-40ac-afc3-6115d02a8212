# StillTasty 功能模块实现方案

基于对 StillTasty.com 的分析，以下是 8 个核心功能模块在 HowLongFresh 中的实现方案。

## 1. Browse Shelf-Life By Category (按分类浏览保质期)

### 功能描述
帮助用户通过分类快速探索食物，提升网站的 SEO 内链结构和用户体验。

### 实现方案

#### 前端实现
```typescript
// 在 Hero 下方添加分类导航卡片
<div className="category-grid">
  <CategoryCard 
    title="水果 / Fruits" 
    icon="🍎" 
    count={120}
    href="/category/fruits"
  />
  <CategoryCard 
    title="蔬菜 / Vegetables" 
    icon="🥬" 
    count={85}
    href="/category/vegetables"
  />
  // ... 更多分类
</div>
```

#### 后端实现
- **路由**: `/category/[slug]` 动态页面
- **数据源**: 基于现有 USDA 数据库按 category 字段分组
- **SEO优化**: 每个分类页面独立的 meta 标签和结构化数据

#### 技术栈
- Next.js 动态路由
- 静态生成 (SSG) 提升 SEO
- Tailwind CSS 响应式卡片布局

---

## 2. Keep It or Toss It? (保留还是丢弃?)

### 功能描述
一句话定位网站核心价值，让用户自然想起网站功能。

### 实现方案

#### UI/UX 改进
```typescript
// 修改主标题为更直接的问题形式
<h1 className="hero-title">
  Keep it or toss it? 
  <span className="subtitle">Upload a photo or type a food name to find out.</span>
</h1>
```

#### 营销策略
- **Slogan**: "Keep it or toss it? Upload a photo or type a food name to find out."
- **CTA按钮**: 从 "Check Freshness" 改为 "Keep or Toss?"
- **社交分享**: 添加预设文案 "Just checked if my food is still good on HowLongFresh!"

---

## 3. "Today's Tips" / Spotlight (每日小贴士)

### 功能描述
每日一次的小知识驱动用户回访，建立用户粘性。

### 实现方案

#### 数据结构
```typescript
interface DailyTip {
  id: string;
  title: string;
  content: string;
  category: 'storage' | 'safety' | 'nutrition' | 'waste-reduction';
  date: string;
  foodItem?: string; // 关联的食物
  imageUrl?: string;
}
```

#### 实现逻辑
```typescript
// 每日小贴士组件
const DailyTipCard = () => {
  const [tip, setTip] = useState<DailyTip | null>(null);
  
  useEffect(() => {
    // 基于日期生成固定的小贴士
    const today = new Date().toDateString();
    const tipIndex = hashCode(today) % TIPS_DATABASE.length;
    setTip(TIPS_DATABASE[tipIndex]);
  }, []);

  return (
    <div className="daily-tip-card">
      <h3>💡 今日保鲜小贴士</h3>
      <p>{tip?.content}</p>
      {tip?.foodItem && (
        <Link href={`/food/${tip.foodItem}`}>
          了解更多关于{tip.foodItem}的保存方法 →
        </Link>
      )}
    </div>
  );
};
```

#### 内容策略
- **轮换机制**: 基于日期哈希确保每日内容固定
- **内容来源**: AI 生成 + 人工审核
- **互动元素**: 用户可以收藏小贴士

---

## 4. Your Questions Answered (常见问题解答)

### 功能描述
解决用户疑问，减少客服压力，提升长尾 SEO 排名。

### 实现方案

#### 页面结构
```typescript
// /faq 页面布局
const FAQPage = () => {
  const faqCategories = [
    {
      title: "食物保存基础",
      questions: [
        {
          q: "冷冻食品可以保存多久？",
          a: "大多数冷冻食品可以安全保存3-12个月...",
          relatedFoods: ["chicken", "beef", "vegetables"]
        }
      ]
    }
  ];

  return (
    <div className="faq-container">
      <SearchBar placeholder="搜索问题..." />
      <CategoryTabs categories={faqCategories} />
      <QuestionList questions={filteredQuestions} />
    </div>
  );
};
```

#### SEO 优化
- **URL结构**: `/faq/how-long-can-i-freeze-turkey`
- **结构化数据**: FAQ Schema markup
- **内链策略**: 问题答案中链接到相关食物页面

---

## 5. Shelf Talk / Blog (长文档案)

### 功能描述
建立权威性，承载深度内容与外链，提升域名权重。

### 实现方案

#### 内容管理系统
```typescript
// 博客文章数据结构
interface BlogPost {
  slug: string;
  title: string;
  excerpt: string;
  content: string;
  category: 'storage-tips' | 'food-safety' | 'nutrition' | 'sustainability';
  publishDate: string;
  author: string;
  tags: string[];
  relatedFoods: string[];
  seoMetadata: {
    title: string;
    description: string;
    keywords: string[];
  };
}
```

#### 文章示例
1. **"Best Ways to Store Cooked Rice"** - 详细的米饭保存指南
2. **"Expiration Dates vs. Shelf Life"** - 科普保质期概念
3. **"AI 检测剩饭"** - 技术科普文章

#### 实现技术
- **CMS**: 使用 MDX 或 Sanity.io
- **SEO**: 自动生成 sitemap 和 RSS feed
- **社交分享**: Open Graph 和 Twitter Cards

---

## 6. 搜索功能增强

### 当前问题
用户搜索 "turkey" 时可能找不到结果，需要智能匹配和建议。

### 解决方案

#### 智能搜索算法
```typescript
const enhancedSearch = async (query: string) => {
  // 1. 精确匹配
  let results = await exactMatch(query);
  
  // 2. 模糊匹配
  if (results.length === 0) {
    results = await fuzzyMatch(query);
  }
  
  // 3. 同义词匹配
  if (results.length === 0) {
    const synonyms = await getSynonyms(query);
    results = await searchBySynonyms(synonyms);
  }
  
  // 4. AI 语义搜索
  if (results.length === 0) {
    results = await aiSemanticSearch(query);
  }
  
  return {
    results,
    suggestions: await getSearchSuggestions(query),
    didYouMean: await getSpellingSuggestions(query)
  };
};
```

#### 搜索建议组件
```typescript
const SearchSuggestions = ({ query, suggestions }) => (
  <div className="search-suggestions">
    <p>没有找到 "{query}" 的结果，您是否要查找：</p>
    <div className="suggestion-chips">
      {suggestions.map(suggestion => (
        <button 
          key={suggestion}
          onClick={() => searchFor(suggestion)}
          className="suggestion-chip"
        >
          {suggestion}
        </button>
      ))}
    </div>
  </div>
);
```

---

## 7. 用户互动功能

### 7.1 食物评分系统
```typescript
interface FoodRating {
  foodId: string;
  userId: string;
  rating: 1 | 2 | 3 | 4 | 5;
  comment?: string;
  helpfulVotes: number;
}

const RatingComponent = ({ foodId }) => {
  const [userRating, setUserRating] = useState(0);
  
  return (
    <div className="food-rating">
      <h4>这个信息对您有帮助吗？</h4>
      <StarRating value={userRating} onChange={setUserRating} />
      <button onClick={() => submitRating(foodId, userRating)}>
        提交评分
      </button>
    </div>
  );
};
```

### 7.2 用户贡献系统
```typescript
const ContributeSection = () => (
  <div className="contribute-section">
    <h3>帮助改进数据</h3>
    <p>发现信息有误？点击报告问题</p>
    <button onClick={openReportModal}>报告问题</button>
    <button onClick={openSuggestModal}>建议新食物</button>
  </div>
);
```

---

## 8. 移动端优化

### 响应式设计改进
```typescript
// 移动端优先的搜索界面
const MobileSearchInterface = () => (
  <div className="mobile-search">
    <div className="search-tabs">
      <Tab active>📝 输入食物名</Tab>
      <Tab>📷 拍照识别</Tab>
    </div>
    
    <div className="quick-categories">
      <CategoryChip>🍎 水果</CategoryChip>
      <CategoryChip>🥬 蔬菜</CategoryChip>
      <CategoryChip>🥩 肉类</CategoryChip>
      <CategoryChip>🥛 乳制品</CategoryChip>
    </div>
    
    <SearchInput 
      placeholder="例如：苹果、牛奶、剩饭"
      autoFocus
    />
  </div>
);
```

---

## 实施优先级

### Phase 1 (立即实施)
1. ✅ 修改主标题为 "Keep it or toss it?"
2. ✅ 添加分类浏览卡片
3. ✅ 增强搜索建议功能

### Phase 2 (2周内)
1. 🔄 实现每日小贴士功能
2. 🔄 创建 FAQ 页面
3. 🔄 添加用户评分系统

### Phase 3 (1个月内)
1. ⏳ 建立博客系统
2. ⏳ 完善移动端体验
3. ⏳ 添加用户贡献功能

---

## 技术架构总结

### 新增依赖
```json
{
  "fuse.js": "^6.6.2",        // 模糊搜索
  "@next/mdx": "^13.0.0",     // 博客系统
  "react-star-ratings": "^2.3.0", // 评分组件
  "date-fns": "^2.29.0"       // 日期处理
}
```

### 数据库扩展
```sql
-- 新增表结构
CREATE TABLE daily_tips (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255),
  content TEXT,
  category VARCHAR(50),
  food_item VARCHAR(100),
  created_at TIMESTAMP
);

CREATE TABLE user_ratings (
  id SERIAL PRIMARY KEY,
  food_id VARCHAR(100),
  user_id VARCHAR(100),
  rating INTEGER,
  comment TEXT,
  helpful_votes INTEGER DEFAULT 0,
  created_at TIMESTAMP
);
```

这个实现方案将显著提升 HowLongFresh 的用户体验和 SEO 表现，同时保持与现有架构的兼容性。

---

## 详细实现指南

### 1. 分类浏览页面实现

#### 文件结构
```
pages/
  category/
    [slug].tsx          // 动态分类页面
    index.tsx           // 分类总览页面
components/
  category/
    CategoryGrid.tsx    // 分类网格组件
    CategoryCard.tsx    // 单个分类卡片
    FoodList.tsx        // 食物列表组件
```

#### 核心代码示例
```typescript
// pages/category/[slug].tsx
export async function getStaticPaths() {
  const categories = await getCategoriesFromDatabase();
  const paths = categories.map(cat => ({
    params: { slug: cat.slug }
  }));

  return { paths, fallback: 'blocking' };
}

export async function getStaticProps({ params }) {
  const category = await getCategoryBySlug(params.slug);
  const foods = await getFoodsByCategory(params.slug);

  return {
    props: { category, foods },
    revalidate: 3600 // 1小时重新生成
  };
}
```

### 2. 搜索功能增强实现

#### 搜索算法优化
```typescript
// lib/enhanced-search.ts
import Fuse from 'fuse.js';

class EnhancedFoodSearch {
  private fuseInstance: Fuse<FoodItem>;
  private synonymMap: Map<string, string[]>;

  constructor(foodDatabase: FoodItem[]) {
    this.fuseInstance = new Fuse(foodDatabase, {
      keys: ['name', 'aliases', 'category'],
      threshold: 0.3,
      includeScore: true
    });

    this.synonymMap = new Map([
      ['turkey', ['火鸡', '土耳其鸡', 'thanksgiving turkey']],
      ['chicken', ['鸡肉', '鸡', 'poultry']],
      // ... 更多同义词映射
    ]);
  }

  async search(query: string): Promise<SearchResult> {
    // 1. 精确匹配
    const exactMatch = await this.exactSearch(query);
    if (exactMatch.length > 0) {
      return { results: exactMatch, type: 'exact' };
    }

    // 2. 模糊搜索
    const fuzzyResults = this.fuseInstance.search(query);
    if (fuzzyResults.length > 0) {
      return {
        results: fuzzyResults.map(r => r.item),
        type: 'fuzzy',
        confidence: fuzzyResults[0].score
      };
    }

    // 3. 同义词搜索
    const synonymResults = await this.searchBySynonyms(query);
    if (synonymResults.length > 0) {
      return { results: synonymResults, type: 'synonym' };
    }

    // 4. AI语义搜索
    return await this.aiSemanticSearch(query);
  }

  private async aiSemanticSearch(query: string): Promise<SearchResult> {
    // 调用AI模型进行语义理解
    const aiResponse = await callAIModel(`
      用户搜索: "${query}"
      请从以下食物数据库中找到最相关的食物，如果没有找到，请建议最相似的食物：
      ${JSON.stringify(this.foodDatabase.slice(0, 50))}
    `);

    return this.parseAISearchResponse(aiResponse);
  }
}
```

### 3. 每日小贴士系统

#### 内容管理
```typescript
// lib/daily-tips.ts
const DAILY_TIPS = [
  {
    id: 'tip-001',
    title: '香蕉保存小窍门',
    content: '将香蕉与苹果分开存放，苹果释放的乙烯气体会加速香蕉成熟。',
    category: 'storage',
    relatedFood: 'banana',
    season: 'all',
    difficulty: 'easy'
  },
  {
    id: 'tip-002',
    title: '剩米饭的黄金保存法',
    content: '剩米饭应在2小时内放入冰箱，可保存3-4天。重新加热时要确保温度达到74°C以上。',
    category: 'safety',
    relatedFood: 'rice',
    season: 'all',
    difficulty: 'medium'
  }
  // ... 更多小贴士
];

export function getTodaysTip(): DailyTip {
  const today = new Date();
  const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / 86400000);
  const tipIndex = dayOfYear % DAILY_TIPS.length;

  return DAILY_TIPS[tipIndex];
}
```

### 4. FAQ系统实现

#### 数据结构设计
```typescript
// types/faq.ts
interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'storage' | 'safety' | 'identification' | 'general';
  tags: string[];
  relatedFoods: string[];
  popularity: number; // 基于用户点击统计
  lastUpdated: string;
}

interface FAQCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  questions: FAQItem[];
}
```

#### 搜索和过滤
```typescript
// components/faq/FAQSearch.tsx
const FAQSearch = ({ faqs, onFilter }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredFAQs = useMemo(() => {
    return faqs.filter(faq => {
      const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [faqs, searchTerm, selectedCategory]);

  useEffect(() => {
    onFilter(filteredFAQs);
  }, [filteredFAQs, onFilter]);

  return (
    <div className="faq-search">
      <SearchInput
        value={searchTerm}
        onChange={setSearchTerm}
        placeholder="搜索常见问题..."
      />
      <CategoryFilter
        categories={FAQ_CATEGORIES}
        selected={selectedCategory}
        onChange={setSelectedCategory}
      />
    </div>
  );
};
```

### 5. 博客系统架构

#### MDX配置
```javascript
// next.config.js
const withMDX = require('@next/mdx')({
  extension: /\.mdx?$/,
  options: {
    remarkPlugins: [remarkGfm],
    rehypePlugins: [rehypeHighlight],
  },
});

module.exports = withMDX({
  pageExtensions: ['ts', 'tsx', 'js', 'jsx', 'md', 'mdx'],
});
```

#### 博客文章模板
```typescript
// components/blog/BlogPost.tsx
const BlogPost = ({ post, relatedPosts }) => {
  return (
    <article className="blog-post">
      <header className="post-header">
        <h1>{post.title}</h1>
        <div className="post-meta">
          <time>{formatDate(post.publishDate)}</time>
          <span className="category">{post.category}</span>
          <div className="tags">
            {post.tags.map(tag => (
              <Tag key={tag} href={`/blog/tag/${tag}`}>{tag}</Tag>
            ))}
          </div>
        </div>
      </header>

      <div className="post-content">
        <MDXContent components={mdxComponents} />
      </div>

      <footer className="post-footer">
        <RelatedFoods foods={post.relatedFoods} />
        <RelatedPosts posts={relatedPosts} />
        <ShareButtons url={post.url} title={post.title} />
      </footer>
    </article>
  );
};
```

### 6. 性能优化策略

#### 图片优化
```typescript
// components/OptimizedImage.tsx
import Image from 'next/image';

const OptimizedFoodImage = ({ src, alt, ...props }) => {
  return (
    <Image
      src={src}
      alt={alt}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      {...props}
    />
  );
};
```

#### 缓存策略
```typescript
// lib/cache.ts
import { Redis } from 'ioredis';

class CacheManager {
  private redis: Redis;

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
  }

  async getFoodData(foodId: string): Promise<FoodResult | null> {
    const cacheKey = `food:${foodId}`;
    const cached = await this.redis.get(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    const foodData = await fetchFoodFromDatabase(foodId);
    if (foodData) {
      await this.redis.setex(cacheKey, 3600, JSON.stringify(foodData)); // 1小时缓存
    }

    return foodData;
  }
}
```

### 7. SEO优化实现

#### 结构化数据
```typescript
// components/seo/StructuredData.tsx
const FoodStructuredData = ({ food }) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Recipe",
    "name": food.name,
    "description": `${food.name}的保存方法和保质期信息`,
    "nutrition": {
      "@type": "NutritionInformation",
      "calories": food.nutrition?.calories
    },
    "recipeInstructions": food.tips.map(tip => ({
      "@type": "HowToStep",
      "text": tip
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};
```

这个完整的实现方案涵盖了从前端组件到后端架构的所有细节，确保功能的可行性和可维护性。

# MCP (Model Context Protocol) 工具设置指南

## 已安装的MCP工具

### 1. Supabase MCP
```bash
npm install -g mcp-supabase
```

**功能**：
- 直接与Supabase数据库交互
- 执行查询和数据操作
- 管理表结构和数据

### 2. Playwright MCP
```bash
npm install -g @playwright/mcp
```

**功能**：
- 自动化浏览器操作
- 网页截图和测试
- 模拟用户交互

## 配置方法

### Supabase MCP配置

1. 创建配置文件 `~/.mcp/supabase-config.json`：
```json
{
  "supabaseUrl": "https://emopvngdwwghndzcfxvw.supabase.co",
  "supabaseKey": "your-service-role-key"
}
```

2. 启动MCP服务器：
```bash
mcp-supabase --config ~/.mcp/supabase-config.json
```

### Playwright MCP配置

1. 初始化Playwright：
```bash
npx playwright install
```

2. 启动Playwright MCP服务器：
```bash
playwright-mcp
```

## 使用示例

### Supabase操作
- 查询数据：直接执行SQL查询
- 插入数据：批量导入FAQ数据
- 管理表：创建、修改表结构

### Playwright操作
- 自动化测试：验证博客页面功能
- 截图：生成页面预览
- 用户流程测试：模拟用户浏览FAQ

## 集成到项目

### 1. 自动化数据导入
使用Supabase MCP直接执行数据库操作，无需手动在Dashboard中执行SQL。

### 2. 端到端测试
使用Playwright MCP自动化测试FAQ博客功能：
- 验证页面加载
- 测试分类筛选
- 检查响应式设计

### 3. 截图生成
为每个FAQ分类生成预览图片，用作封面图。

## 故障排除

### Supabase MCP连接问题
- 确保使用Service Role Key而非Anon Key
- 检查网络连接和代理设置

### Playwright MCP启动失败
- 确保已安装浏览器：`npx playwright install`
- 检查端口冲突

## 下一步

1. 配置MCP工具以提高开发效率
2. 使用Supabase MCP自动创建posts表
3. 使用Playwright MCP测试博客功能
4. 集成到CI/CD流程中
# StillTasty 处理后数据整合文档

## 概述

本文档记录了将 `processed_stilltasty_data_20250718_132503.json` 文件中的 StillTasty 数据整合到 HowLongFresh 系统的完整过程。

## 数据分析

### 数据文件信息
- **文件路径**: `/scripts/processed_data/processed_stilltasty_data_20250718_132503.json`
- **记录总数**: 2,014 条
- **数据来源**: StillTasty.com（已处理和清理）
- **数据质量**: 高（包含详细的存储信息和建议）

### 数据结构
```json
{
  "id": "stilltasty_12345",
  "name": "原始食物名称（包含详细描述）",
  "name_clean": "清理后的食物名称",
  "keywords": ["关键词1", "关键词2"],
  "category_id": "fruits",
  "category_name": "Fruits",
  "category_name_zh": "水果",
  "storage": {
    "room_temperature": {
      "days": 5,
      "text": "原始文本（可能包含HTML）",
      "recommended": true
    },
    "refrigerated": { ... },
    "frozen": { ... }
  },
  "tips": ["存储建议1", "存储建议2"],
  "source": {
    "name": "StillTasty.com",
    "url": "https://stilltasty.com/...",
    "confidence": "high"
  }
}
```

## 实施步骤

### 1. 数据库准备

#### 创建的迁移文件
- **文件**: `/supabase/migrations/005_add_stilltasty_support.sql`
- **主要更改**:
  1. 扩展 `source` 字段支持 'STILLTASTY'
  2. 添加新字段：
     - `external_id`: 存储原始 stilltasty_id
     - `source_url`: 数据来源URL
     - `name_variations`: 名称变体数组
     - `storage_text`: JSON格式的原始存储描述
  3. 创建分类映射表 `stilltasty_category_mapping`
  4. 添加数据清理函数：
     - `parse_storage_days()`: 解析存储天数文本
     - `clean_html_text()`: 清理HTML标签
     - `check_food_exists()`: 检查重复数据

### 2. 数据导入脚本

#### 主导入脚本
- **文件**: `/scripts/import-stilltasty-processed-data.js`
- **功能**:
  1. 批量读取和处理数据（每批100条）
  2. 清理HTML和标准化文本
  3. 智能解析存储天数：
     - "5-7 days" → 6天
     - "1 year" → 365天
     - "indefinitely" → 9999天
  4. 去重处理（基于 search_key 和 external_id）
  5. 插入关键词和别名
  6. 记录导入日志

#### 测试脚本
- **文件**: `/scripts/test-stilltasty-import.js`
- **用途**: 验证数据清理函数和数据格式
- **文件**: `/scripts/test-import-small-batch.js`
- **用途**: 小批量测试导入（前10条）

### 3. 数据清理规则

#### 存储天数标准化
- 范围值取中间值："5-7 days" → 6
- 年份转换为天数："1 year" → 365
- 月份转换为天数："6 months" → 180
- 周转换为天数："2 weeks" → 14
- 特殊值处理："indefinitely" → 9999

#### 文本清理
- 移除所有HTML标签和脚本
- 清理多余空白字符
- 解码HTML实体（&nbsp;、&amp;等）
- 保留纯文本存储建议

#### 名称处理
- 使用 `name_clean` 作为主要显示名称
- 原始 `name` 存入 `name_variations` 用于搜索
- 生成标准化的 `search_key`

### 4. 分类映射

StillTasty分类到系统分类的映射：
```sql
fruits → Produce
vegetables → Produce  
dairy → Dairy Products & Eggs
meat → Meat
poultry → Poultry
seafood → Seafood
beverages → Beverages
grains → Grains, Beans & Pasta
condiments → Condiments, Sauces & Canned Goods
baked → Baked Goods
frozen → Food Purchased Frozen
prepared → Deli & Prepared Foods
```

## 执行步骤

### 1. 运行数据库迁移
```bash
# 方式一：使用 Supabase CLI
supabase db push

# 方式二：手动执行 SQL
psql -h [数据库地址] -U [用户名] -d [数据库名] -f supabase/migrations/005_add_stilltasty_support.sql
```

### 2. 测试数据处理
```bash
# 运行测试脚本
node scripts/test-stilltasty-import.js

# 小批量测试导入
node scripts/test-import-small-batch.js
```

### 3. 执行完整导入
```bash
# 运行主导入脚本
node scripts/import-stilltasty-processed-data.js
```

## 预期结果

### 导入统计
- **总记录数**: 2,014
- **预计成功导入**: ~1,800-1,900（扣除重复）
- **数据源标记**: STILLTASTY
- **置信度**: 0.95

### 数据增强
1. **搜索能力提升**：通过 keywords 增强搜索准确性
2. **多语言支持**：category_name_zh 提供中文分类名
3. **详细存储建议**：保留原始存储描述文本
4. **数据溯源**：external_id 和 source_url 便于数据更新

### 与现有数据的整合
- USDA数据优先级高于StillTasty
- StillTasty数据作为补充，填补USDA数据空白
- 通过 source 字段区分数据来源
- 支持未来的数据源切换功能

## 注意事项

1. **数据去重**：基于 search_key 避免重复导入
2. **分类映射**：某些StillTasty分类可能需要手动调整
3. **性能考虑**：批量导入避免单条插入的性能问题
4. **错误处理**：记录失败的导入，便于后续处理

## 后续优化建议

1. **UI显示数据源**：在食物详情页显示数据来源
2. **数据更新机制**：定期从StillTasty更新数据
3. **用户反馈**：允许用户报告数据不准确
4. **AI增强**：使用AI验证和补充存储建议

## 相关文件

- 数据库迁移：`/supabase/migrations/005_add_stilltasty_support.sql`
- 导入脚本：`/scripts/import-stilltasty-processed-data.js`
- 测试脚本：`/scripts/test-stilltasty-import.js`
- 原始数据：`/scripts/processed_data/processed_stilltasty_data_20250718_132503.json`

## 更新历史

- 2025-07-25: 初始版本，完成数据整合方案设计和实施
# StillTasty 分类系统更新文档

## 📋 更新概述

基于StillTasty爬取的2014种食物数据，将首页的9宫格分类系统更新为10宫格分类系统，以更好地匹配实际数据分布。

## 🔄 主要变更

### 1. 分类数量调整
- **之前**: 9个分类
- **现在**: 10个分类
- **新增分类**: 调料(Condiments)、香料(Herbs & Spices)

### 2. 分类详细信息

| 分类ID | 中文名称 | 英文名称 | 数据量 | 占比 | 图标 |
|--------|----------|----------|--------|------|------|
| fruits | 水果 | Fruits | 222 | 11.0% | 🍎 |
| meat | 肉类 | Meat & Poultry | 350 | 17.4% | 🥩 |
| snacks | 零食 | Snacks & Sweets | 275 | 13.7% | 🍪 |
| grains | 谷物 | Grains & Bread | 229 | 11.4% | 🍞 |
| dairy | 奶制品 | Dairy & Eggs | 197 | 9.8% | 🥛 |
| seafood | 海鲜 | Seafood | 196 | 9.7% | 🐟 |
| condiments | 调料 | Condiments & Sauces | 182 | 9.0% | 🍯 |
| beverages | 饮料 | Beverages | 144 | 7.1% | 🧃 |
| spices | 香料 | Herbs & Spices | 134 | 6.7% | 🌿 |
| vegetables | 蔬菜 | Vegetables | 85 | 4.2% | 🥬 |

**总计**: 2014种食物

### 3. 布局调整
- **移动端**: 2列布局 (grid-cols-2)
- **平板端**: 3列布局 (md:grid-cols-3)
- **桌面端**: 5列布局 (lg:grid-cols-5)

## 📁 修改的文件

### 1. `lib/food-categories.ts`
- 添加了 `condiments` 和 `spices` 两个新分类
- 更新了分类映射 `CATEGORY_MAPPING`
- 调整了分类名称以匹配StillTasty数据

### 2. `components/CategoryGrid.tsx`
- 更新网格布局为响应式10宫格
- 修改注释从"9宫格"到"10宫格"

### 3. `app/[locale]/test-new-categories/page.tsx` (新增)
- 创建测试页面验证新分类系统
- 包含分类统计和详细信息展示

## 🎨 设计考虑

### 颜色方案
- **调料**: 橙色系 (text-orange-600, bg-orange-50)
- **香料**: 翠绿色系 (text-emerald-600, bg-emerald-50)
- 其他分类保持原有配色

### 图标选择
- **调料**: 🍯 (蜂蜜罐，代表调味品)
- **香料**: 🌿 (草本植物，代表香料和草药)

## 🔍 数据映射

### StillTasty → 新分类系统
```typescript
// 新增映射
'Condiments': 'condiments',
'Sauces': 'condiments',
'Dressings': 'condiments',
'Spices': 'spices',
'Herbs': 'spices',
'Herbs & Spices': 'spices',
'Seasonings': 'spices'
```

## 📊 数据分布分析

### 最大分类
1. **肉类** (350种, 17.4%) - 最大分类
2. **零食** (275种, 13.7%) - 第二大分类
3. **谷物** (229种, 11.4%) - 第三大分类

### 最小分类
1. **蔬菜** (85种, 4.2%) - 最小分类
2. **香料** (134种, 6.7%)
3. **饮料** (144种, 7.1%)

## 🚀 部署说明

### 测试步骤
1. 访问 `/test-new-categories` 查看新分类系统
2. 检查主页分类网格布局
3. 验证各分类页面链接正常工作

### 兼容性
- ✅ 向后兼容现有分类页面路由
- ✅ 保持现有数据库结构不变
- ✅ 支持响应式设计

## 📈 后续计划

### Phase 1: 数据集成 (下一步)
- [ ] 将StillTasty数据导入Supabase数据库
- [ ] 更新分类统计数据
- [ ] 测试分类页面数据展示

### Phase 2: 功能增强
- [ ] 添加分类筛选功能
- [ ] 优化移动端体验
- [ ] 添加分类搜索功能

### Phase 3: 数据优化
- [ ] 合并USDA和StillTasty数据
- [ ] 建立数据优先级机制
- [ ] 添加数据源标识

## 🔧 技术细节

### 响应式布局
```css
/* 移动端: 2列 */
grid-cols-2

/* 平板端: 3列 */
md:grid-cols-3

/* 桌面端: 5列 (2行显示10个分类) */
lg:grid-cols-5
```

### 分类卡片尺寸
- 保持原有卡片设计
- 自适应内容高度
- 悬停效果保持不变

---

**更新完成时间**: 2025-01-19
**影响范围**: 首页分类导航、分类页面路由
**测试状态**: ✅ 已测试通过

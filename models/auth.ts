import { getSupabaseClient } from "./db";
import { getIsoTimestr } from "@/lib/time";

export interface UserPassword {
  id?: number;
  user_uuid: string;
  password_hash: string;
  created_at?: string;
  updated_at?: string;
}

export interface EmailVerification {
  id?: number;
  token: string;
  email: string;
  user_uuid?: string;
  expires_at: string;
  verified_at?: string;
  created_at?: string;
}

export interface PasswordReset {
  id?: number;
  token: string;
  email: string;
  user_uuid?: string;
  expires_at: string;
  used_at?: string;
  created_at?: string;
}

// User Password functions
export async function createUserPassword(userPassword: UserPassword) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("user_passwords")
    .insert(userPassword)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function getUserPasswordByUuid(userUuid: string): Promise<UserPassword | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("user_passwords")
    .select("*")
    .eq("user_uuid", userUuid)
    .single();

  if (error) {
    return null;
  }

  return data;
}

export async function updateUserPassword(userUuid: string, passwordHash: string) {
  const supabase = getSupabaseClient();
  const updated_at = getIsoTimestr();
  
  const { data, error } = await supabase
    .from("user_passwords")
    .update({ password_hash: passwordHash, updated_at })
    .eq("user_uuid", userUuid)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

// Email Verification functions
export async function createEmailVerification(verification: EmailVerification) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("email_verifications")
    .insert(verification)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function getEmailVerificationByToken(token: string): Promise<EmailVerification | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("email_verifications")
    .select("*")
    .eq("token", token)
    .single();

  if (error) {
    return null;
  }

  return data;
}

export async function markEmailAsVerified(token: string) {
  const supabase = getSupabaseClient();
  const verified_at = getIsoTimestr();
  
  const { data, error } = await supabase
    .from("email_verifications")
    .update({ verified_at })
    .eq("token", token)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

// Password Reset functions
export async function createPasswordReset(reset: PasswordReset) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("password_resets")
    .insert(reset)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function getPasswordResetByToken(token: string): Promise<PasswordReset | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("password_resets")
    .select("*")
    .eq("token", token)
    .single();

  if (error) {
    return null;
  }

  return data;
}

export async function markPasswordResetAsUsed(token: string) {
  const supabase = getSupabaseClient();
  const used_at = getIsoTimestr();
  
  const { data, error } = await supabase
    .from("password_resets")
    .update({ used_at })
    .eq("token", token)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

// Update user email verification status
export async function updateUserEmailVerified(userUuid: string) {
  const supabase = getSupabaseClient();
  const email_verified_at = getIsoTimestr();
  
  const { data, error } = await supabase
    .from("users")
    .update({ 
      email_verified: true, 
      email_verified_at,
      updated_at: email_verified_at 
    })
    .eq("uuid", userUuid)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}
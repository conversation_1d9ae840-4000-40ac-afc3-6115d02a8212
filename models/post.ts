import { Post } from "@/types/post";
import { getSupabaseClient } from "./db";

export enum PostStatus {
  Created = "created",
  Deleted = "deleted",
  Online = "online",
  Offline = "offline",
}

export async function insertPost(post: Post) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.from("posts").insert(post);

  if (error) {
    throw error;
  }

  return data;
}

export async function updatePost(uuid: string, post: Partial<Post>) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("posts")
    .update(post)
    .eq("uuid", uuid);

  if (error) {
    throw error;
  }

  return data;
}

export async function findPostByUuid(uuid: string): Promise<Post | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("posts")
    .select("*")
    .eq("uuid", uuid)
    .limit(1)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}

export async function findPostBySlug(
  slug: string,
  locale: string
): Promise<Post | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("posts")
    .select("*")
    .eq("slug", slug)
    .eq("locale", locale)
    .limit(1)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}

export async function getAllPosts(
  page: number = 1,
  limit: number = 50
): Promise<Post[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("posts")
    .select("*")
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    return [];
  }

  return data;
}

export async function getPostsByLocale(
  locale: string,
  page: number = 1,
  limit: number = 50,
  category?: string
): Promise<Post[]> {
  const supabase = getSupabaseClient();
  let query = supabase
    .from("posts")
    .select("*")
    .eq("locale", locale)
    .eq("status", PostStatus.Online);
  
  // 添加分类过滤
  if (category) {
    query = query.eq("category", category);
  }
  
  const { data, error } = await query
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    return [];
  }

  return data;
}

export async function getPostCategories(locale: string): Promise<{name: string, count: number}[]> {
  const supabase = getSupabaseClient();
  
  // 使用原生SQL查询获取分类统计
  const { data, error } = await supabase
    .from("posts")
    .select("category")
    .eq("locale", locale)
    .eq("status", PostStatus.Online);

  if (error || !data) {
    return [];
  }

  // 手动聚合分类统计
  const categoryCount: Record<string, number> = {};
  data.forEach(post => {
    if (post.category) {
      categoryCount[post.category] = (categoryCount[post.category] || 0) + 1;
    }
  });

  // 转换为数组格式
  return Object.entries(categoryCount).map(([name, count]) => ({
    name,
    count
  }));
}
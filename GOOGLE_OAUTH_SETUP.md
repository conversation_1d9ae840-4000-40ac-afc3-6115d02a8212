# 🔧 Google OAuth 生产环境配置指南

## 问题描述
部署到线上后，Google登录重定向到 `localhost:3000`，导致 `ERR_CONNECTION_REFUSED` 错误。

## 解决步骤

### 1. 更新 Google Cloud Console 配置

1. **访问 Google Cloud Console**
   - 打开 [Google Cloud Console](https://console.cloud.google.com/)
   - 选择您的项目

2. **配置 OAuth 2.0 客户端**
   - 导航到：`APIs & Services` > `Credentials`
   - 找到您的 OAuth 2.0 客户端 ID
   - 点击编辑（铅笔图标）

3. **添加生产环境回调URL**
   在 "Authorized redirect URIs" 中添加：
   ```
   https://your-domain.com/api/auth/callback/google
   ```
   
   **示例**：
   - 如果您的域名是 `howlongfresh.com`
   - 添加：`https://howlongfresh.com/api/auth/callback/google`

4. **保存更改**

### 2. 更新环境变量

#### 方法A：直接修改 .env.local（临时测试）
```bash
# 将 localhost 改为您的实际域名
AUTH_URL = "https://your-domain.com"
NEXTAUTH_URL = "https://your-domain.com"
```

#### 方法B：创建生产环境配置（推荐）
1. 复制 `.env.production.example` 为 `.env.production`
2. 填入您的实际域名和配置
3. 确保部署平台使用正确的环境变量

### 3. 部署平台配置

#### Vercel 部署
1. 在 Vercel Dashboard 中打开您的项目
2. 进入 `Settings` > `Environment Variables`
3. 添加以下变量：
   ```
   AUTH_URL = https://your-vercel-app.vercel.app
   NEXTAUTH_URL = https://your-vercel-app.vercel.app
   AUTH_GOOGLE_ID = your-google-client-id
   AUTH_GOOGLE_SECRET = your-google-client-secret
   ```

#### Netlify 部署
1. 在 Netlify Dashboard 中打开您的项目
2. 进入 `Site settings` > `Environment variables`
3. 添加相同的环境变量

#### 其他平台
确保在您的部署平台中设置正确的环境变量。

### 4. 验证配置

1. **检查回调URL格式**：
   ```
   https://your-domain.com/api/auth/callback/google
   ```

2. **测试登录流程**：
   - 访问您的生产网站
   - 点击 "Sign in with Google"
   - 应该重定向到您的域名而不是 localhost

### 5. 常见问题排查

#### 问题1：仍然重定向到 localhost
- **原因**：环境变量未正确设置
- **解决**：检查部署平台的环境变量配置

#### 问题2：Google OAuth 错误
- **原因**：Google Console 中未添加正确的回调URL
- **解决**：确保添加了 `https://your-domain.com/api/auth/callback/google`

#### 问题3：NEXTAUTH_SECRET 错误
- **原因**：生产环境需要设置 NEXTAUTH_SECRET
- **解决**：生成一个随机字符串作为 NEXTAUTH_SECRET

```bash
# 生成随机密钥
openssl rand -base64 32
```

### 6. 完整的环境变量示例

```bash
# 生产环境 (.env.production)
AUTH_URL="https://howlongfresh.com"
NEXTAUTH_URL="https://howlongfresh.com"
AUTH_GOOGLE_ID="653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET="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"
NEXTAUTH_SECRET="your-production-secret-key"
```

## 🎯 快速修复步骤

1. **立即修复**：在 Google Cloud Console 添加生产环境回调URL
2. **更新环境变量**：在部署平台设置正确的 AUTH_URL
3. **重新部署**：确保新的环境变量生效
4. **测试**：验证 Google 登录功能

完成这些步骤后，Google OAuth 应该能正常工作！

#!/usr/bin/env node

/**
 * 最终验证脚本 - 确认端口3000配置正确
 */

const http = require('http');

console.log('🎯 最终验证：端口3000配置\n');

async function verifyConfiguration() {
  console.log('📋 验证清单：');
  console.log('─'.repeat(50));
  
  // 1. 检查服务器运行状态
  try {
    const response = await new Promise((resolve, reject) => {
      const req = http.get('http://localhost:3000', (res) => {
        resolve({ status: res.statusCode });
      });
      req.on('error', reject);
      req.setTimeout(3000, () => {
        req.destroy();
        reject(new Error('连接超时'));
      });
    });
    
    if (response.status === 200) {
      console.log('✅ 服务器运行在端口3000');
    } else {
      console.log(`❌ 服务器响应异常: ${response.status}`);
      return;
    }
  } catch (error) {
    console.log('❌ 服务器未运行在端口3000');
    console.log('请运行: npm run dev');
    return;
  }
  
  // 2. 检查OAuth providers端点
  try {
    const response = await new Promise((resolve, reject) => {
      const req = http.get('http://localhost:3000/api/auth/providers', (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({ status: res.statusCode, data });
        });
      });
      req.on('error', reject);
      req.setTimeout(5000, () => {
        req.destroy();
        reject(new Error('请求超时'));
      });
    });
    
    if (response.status === 200) {
      const providers = JSON.parse(response.data);
      if (providers.google) {
        console.log('✅ Google OAuth Provider 已配置');
        console.log(`   回调URL: ${providers.google.callbackUrl}`);
      } else {
        console.log('❌ Google OAuth Provider 未找到');
      }
    } else {
      console.log(`❌ OAuth providers端点异常: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ 检查OAuth providers失败: ${error.message}`);
  }
  
  // 3. 显示Google Console配置信息
  console.log('\n🔧 Google Cloud Console 配置：');
  console.log('─'.repeat(50));
  console.log('需要在Google Cloud Console中添加的回调URL：');
  console.log('📍 http://localhost:3000/api/auth/callback/google');
  
  console.log('\n📝 配置步骤：');
  console.log('─'.repeat(50));
  console.log('1. 访问: https://console.cloud.google.com/');
  console.log('2. 选择您的项目');
  console.log('3. 导航到: APIs & Services → Credentials');
  console.log('4. 编辑OAuth 2.0客户端ID: 653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96');
  console.log('5. 在"Authorized redirect URIs"中添加:');
  console.log('   http://localhost:3000/api/auth/callback/google');
  console.log('6. 保存更改');
  
  console.log('\n🧪 测试步骤：');
  console.log('─'.repeat(50));
  console.log('1. 完成Google Console配置后');
  console.log('2. 访问: http://localhost:3000');
  console.log('3. 点击Google登录按钮');
  console.log('4. 应该能正常跳转到Google授权页面');
  
  console.log('\n✅ 配置摘要：');
  console.log('─'.repeat(50));
  console.log('• 应用端口: 3000 (固定)');
  console.log('• 回调URL: http://localhost:3000/api/auth/callback/google');
  console.log('• NextAuth配置: 正常');
  console.log('• 环境变量: 已更新');
  
  console.log('\n🎉 准备就绪！');
  console.log('完成Google Console配置后即可正常使用Google OAuth登录。');
}

verifyConfiguration();

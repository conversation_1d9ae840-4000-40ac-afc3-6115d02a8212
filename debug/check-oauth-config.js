#!/usr/bin/env node

/**
 * Google OAuth 配置诊断脚本
 * 用于检查和诊断Google OAuth配置问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Google OAuth 配置诊断\n');

// 读取环境变量
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env.local 文件不存在');
    return {};
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const match = line.match(/^([^#][^=]*)\s*=\s*"?([^"]*)"?$/);
    if (match) {
      env[match[1].trim()] = match[2].trim();
    }
  });

  return env;
}

// 检查配置
function checkConfiguration() {
  const env = loadEnvFile();
  
  console.log('📋 当前配置状态：');
  console.log('─'.repeat(50));
  
  // 检查基本配置
  const authUrl = env.AUTH_URL || '未设置';
  const googleId = env.AUTH_GOOGLE_ID || '未设置';
  const googleSecret = env.AUTH_GOOGLE_SECRET || '未设置';
  const googleEnabled = env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED || '未设置';
  
  console.log(`🌐 AUTH_URL: ${authUrl}`);
  console.log(`🔑 AUTH_GOOGLE_ID: ${googleId.length > 20 ? googleId.substring(0, 30) + '...' : googleId}`);
  console.log(`🔐 AUTH_GOOGLE_SECRET: ${googleSecret.length > 10 ? googleSecret.substring(0, 15) + '...' : googleSecret}`);
  console.log(`✅ GOOGLE_ENABLED: ${googleEnabled}`);
  
  console.log('\n🎯 预期的回调URL：');
  console.log('─'.repeat(50));
  
  if (authUrl && authUrl !== '未设置') {
    const callbackUrl = `${authUrl}/api/auth/callback/google`;
    console.log(`📍 ${callbackUrl}`);
  } else {
    console.log('❌ 无法确定回调URL，AUTH_URL未设置');
  }
  
  console.log('\n🔧 配置检查结果：');
  console.log('─'.repeat(50));
  
  let hasErrors = false;
  
  // 检查必需的配置
  if (!env.AUTH_URL) {
    console.log('❌ AUTH_URL 未设置');
    hasErrors = true;
  } else if (!env.AUTH_URL.startsWith('http')) {
    console.log('❌ AUTH_URL 格式不正确，应该以 http:// 或 https:// 开头');
    hasErrors = true;
  } else {
    console.log('✅ AUTH_URL 配置正确');
  }
  
  if (!env.AUTH_GOOGLE_ID) {
    console.log('❌ AUTH_GOOGLE_ID 未设置');
    hasErrors = true;
  } else if (!env.AUTH_GOOGLE_ID.includes('.apps.googleusercontent.com')) {
    console.log('❌ AUTH_GOOGLE_ID 格式不正确');
    hasErrors = true;
  } else {
    console.log('✅ AUTH_GOOGLE_ID 配置正确');
  }
  
  if (!env.AUTH_GOOGLE_SECRET) {
    console.log('❌ AUTH_GOOGLE_SECRET 未设置');
    hasErrors = true;
  } else if (env.AUTH_GOOGLE_SECRET.length < 10) {
    console.log('❌ AUTH_GOOGLE_SECRET 可能不正确（长度太短）');
    hasErrors = true;
  } else {
    console.log('✅ AUTH_GOOGLE_SECRET 配置正确');
  }
  
  if (env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED !== 'true') {
    console.log('❌ NEXT_PUBLIC_AUTH_GOOGLE_ENABLED 未启用');
    hasErrors = true;
  } else {
    console.log('✅ Google OAuth 已启用');
  }
  
  console.log('\n📝 修复建议：');
  console.log('─'.repeat(50));
  
  if (hasErrors) {
    console.log('1. 检查并修复上述配置错误');
    console.log('2. 确保在 Google Cloud Console 中添加正确的回调URL');
    if (env.AUTH_URL) {
      console.log(`   回调URL应该是: ${env.AUTH_URL}/api/auth/callback/google`);
    }
    console.log('3. 重启开发服务器');
  } else {
    console.log('✅ 配置看起来正确！');
    console.log('如果仍有问题，请检查：');
    console.log('1. Google Cloud Console 中的回调URL是否正确');
    console.log('2. 是否已启用相关的 Google API');
    console.log('3. 浏览器缓存是否已清除');
  }
  
  console.log('\n🔗 有用的链接：');
  console.log('─'.repeat(50));
  console.log('• Google Cloud Console: https://console.cloud.google.com/');
  console.log('• NextAuth.js 文档: https://authjs.dev/getting-started/providers/google');
  console.log('• 修复指南: ./debug/google-oauth-fix.md');
}

// 运行诊断
checkConfiguration();

# Google OAuth 错误修复指南

## 问题描述
错误：`redirect_uri_mismatch` - Google OAuth重定向URI不匹配

## 当前配置状态
- 应用运行端口：`http://localhost:3000`
- Google Client ID：`653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96.apps.googleusercontent.com`
- 需要的回调URL：`http://localhost:3000/api/auth/callback/google`

## 修复步骤

### 1. 更新Google Cloud Console配置

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 选择您的项目
3. 导航到 "APIs & Services" > "Credentials"
4. 找到OAuth 2.0客户端ID：`653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96`
5. 点击编辑
6. 在"Authorized redirect URIs"中添加：
   ```
   http://localhost:3000/api/auth/callback/google
   ```
7. 保存更改

### 2. 验证环境变量配置

确保以下环境变量正确设置：
```bash
AUTH_URL = "http://localhost:3000"
AUTH_GOOGLE_ID = "653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
```

### 3. 重启开发服务器

更新配置后，重启Next.js开发服务器：
```bash
# 停止当前服务器 (Ctrl+C)
# 然后重新启动
npm run dev
```

### 4. 测试OAuth流程

1. 访问 `http://localhost:3000`
2. 点击Google登录按钮
3. 应该能正常跳转到Google授权页面

## 常见问题排查

### 如果仍然出现redirect_uri_mismatch错误：

1. **检查端口号**：确保Google Console中的URI与实际运行端口一致
2. **清除浏览器缓存**：清除浏览器缓存和Cookie
3. **等待生效**：Google配置更改可能需要几分钟生效
4. **检查协议**：确保使用http而不是https（本地开发）

### 如果出现其他错误：

1. **检查控制台日志**：查看浏览器开发者工具的控制台
2. **检查服务器日志**：查看Next.js终端输出
3. **验证API启用**：确保Google+ API或Google Identity API已启用

## 验证修复成功

修复成功后，您应该看到：
1. 能够正常跳转到Google授权页面
2. 授权后能够成功返回应用
3. 用户信息正确显示
4. 控制台没有OAuth相关错误

## 备用解决方案

如果上述方法不起作用，可以尝试：

1. **使用固定端口**：
   在`package.json`中修改dev脚本：
   ```json
   "dev": "cross-env NODE_NO_WARNINGS=1 next dev -p 3000 --turbopack"
   ```

2. **添加多个回调URL**：
   在Google Console中同时添加：
   ```
   http://localhost:3000/api/auth/callback/google
   http://localhost:3001/api/auth/callback/google
   http://localhost:3002/api/auth/callback/google
   ```

3. **检查NextAuth配置**：
   确保`auth/config.ts`中的Google Provider配置正确

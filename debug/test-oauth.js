#!/usr/bin/env node

/**
 * Google OAuth 测试脚本
 * 用于测试OAuth配置是否正确
 */

const http = require('http');
const https = require('https');

console.log('🧪 Google OAuth 测试\n');

// 测试NextAuth端点
async function testNextAuthEndpoints() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('📡 测试NextAuth端点：');
  console.log('─'.repeat(50));
  
  const endpoints = [
    '/api/auth/providers',
    '/api/auth/csrf',
    '/api/auth/session'
  ];
  
  for (const endpoint of endpoints) {
    try {
      const url = `${baseUrl}${endpoint}`;
      console.log(`🔍 测试: ${endpoint}`);

      // 使用简单的HTTP请求而不是fetch
      const response = await new Promise((resolve, reject) => {
        const req = http.get(url, (res) => {
          let data = '';
          res.on('data', chunk => data += chunk);
          res.on('end', () => {
            resolve({
              status: res.statusCode,
              data: data
            });
          });
        });
        req.on('error', reject);
        req.setTimeout(5000, () => {
          req.destroy();
          reject(new Error('请求超时'));
        });
      });

      if (response.status === 200) {
        console.log(`✅ ${endpoint} - 状态: ${response.status}`);

        if (endpoint === '/api/auth/providers') {
          try {
            const data = JSON.parse(response.data);
            const hasGoogle = data.google ? '✅' : '❌';
            console.log(`   Google Provider: ${hasGoogle}`);
          } catch (e) {
            console.log(`   无法解析响应数据`);
          }
        }
      } else {
        console.log(`❌ ${endpoint} - 状态: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - 错误: ${error.message}`);
    }
  }
}

// 测试Google OAuth URL
function testGoogleOAuthUrl() {
  console.log('\n🔗 Google OAuth URL：');
  console.log('─'.repeat(50));
  
  const baseUrl = 'http://localhost:3000';
  const googleAuthUrl = `${baseUrl}/api/auth/signin/google`;
  const callbackUrl = `${baseUrl}/api/auth/callback/google`;
  
  console.log(`🚀 登录URL: ${googleAuthUrl}`);
  console.log(`📍 回调URL: ${callbackUrl}`);
  
  console.log('\n📋 Google Console配置检查清单：');
  console.log('─'.repeat(50));
  console.log('□ 已在Google Cloud Console中创建OAuth 2.0客户端');
  console.log('□ 已添加授权重定向URI：');
  console.log(`   ${callbackUrl}`);
  console.log('□ 已启用Google+ API或Google Identity API');
  console.log('□ 客户端ID和密钥已正确配置在.env.local中');
}

// 主函数
async function main() {
  try {
    await testNextAuthEndpoints();
    testGoogleOAuthUrl();
    
    console.log('\n🎯 下一步操作：');
    console.log('─'.repeat(50));
    console.log('1. 确保Google Cloud Console配置正确');
    console.log('2. 访问 http://localhost:3002 测试登录');
    console.log('3. 如果仍有问题，检查浏览器控制台错误');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.log('\n💡 可能的原因：');
    console.log('- 开发服务器未运行');
    console.log('- 端口3002被占用');
    console.log('- NextAuth配置错误');
  }
}

// 检查服务器是否运行
function checkServer() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000', (res) => {
      resolve(true);
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.setTimeout(3000, () => {
      req.destroy();
      resolve(false);
    });
  });
}

// 运行测试
checkServer().then(isRunning => {
  if (isRunning) {
    main();
  } else {
    console.log('❌ 开发服务器未在端口3000上运行');
    console.log('请先运行: npm run dev');
  }
});

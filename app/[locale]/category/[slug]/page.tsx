import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { getCategoryBySlug, getAllCategorySlugs, FOOD_CATEGORIES } from '@/lib/food-categories';
import { getFoodsByCategory } from '@/lib/supabase-food-service';
import FoodCardList from '@/components/FoodCardList';
import CategoryBreadcrumb from '@/components/CategoryBreadcrumb';
import CategorySortSelector from '@/components/CategorySortSelector';
import CategorySearchBar from '@/components/CategorySearchBar';
import Pagination from '@/components/Pagination';

interface CategoryPageProps {
  params: Promise<{
    locale: string;
    slug: string;
  }>;
  searchParams: Promise<{
    page?: string;
    sort?: string;
    search?: string;
  }>;
}

// 生成静态路径
export async function generateStaticParams() {
  const slugs = getAllCategorySlugs();
  const locales = ['en', 'zh']; // 支持的语言
  
  const paths = [];
  for (const locale of locales) {
    for (const slug of slugs) {
      paths.push({ locale, slug });
    }
  }
  
  return paths;
}

// 生成页面元数据
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const category = getCategoryBySlug(resolvedParams.slug);

  if (!category) {
    return {
      title: 'Category Not Found',
      description: 'The requested food category was not found.'
    };
  }

  const isZh = resolvedParams.locale === 'zh';
  const title = isZh ? category.name.zh : category.name.en;
  const description = isZh ? category.description.zh : category.description.en;

  return {
    title: `${title} | HowLongFresh`,
    description: description,
    keywords: [
      title,
      '保鲜',
      '保质期',
      '储存',
      '食物安全',
      isZh ? '保存方法' : 'storage tips'
    ],
    openGraph: {
      title: `${title} - 食物保鲜指南`,
      description: description,
      type: 'website',
      locale: resolvedParams.locale,
    },
    alternates: {
      canonical: `/category/${resolvedParams.slug}`,
      languages: {
        'zh': `/zh/category/${resolvedParams.slug}`,
        'en': `/en/category/${resolvedParams.slug}`,
      },
    },
  };
}

export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const t = await getTranslations('categories');

  const category = getCategoryBySlug(resolvedParams.slug);

  if (!category) {
    notFound();
  }

  // 获取分页参数
  const page = parseInt(resolvedSearchParams.page || '1', 10);
  const pageSize = 24;
  const sortBy = resolvedSearchParams.sort || 'name';
  const searchQuery = resolvedSearchParams.search || '';

  // 获取该分类下的食物数据
  const { foods, total, hasMore } = await getFoodsByCategory(
    resolvedParams.slug,
    page,
    pageSize,
    sortBy,
    searchQuery
  );

  const isZh = resolvedParams.locale === 'zh';
  const categoryName = isZh ? category.name.zh : category.name.en;
  const categoryDescription = isZh ? category.description.zh : category.description.en;

  return (
    <div className="min-h-screen bg-background">
      {/* 面包屑导航 */}
      <CategoryBreadcrumb
        category={category}
        locale={resolvedParams.locale}
      />

      {/* 页面头部 */}
      <div className="bg-card border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4 mb-4">
            <span className="text-5xl">{category.emoji}</span>
            <div>
              <h1 className={`text-3xl font-bold ${category.color}`}>
                {categoryName}
              </h1>
              <p className="text-gray-600 mt-2">
                {categoryDescription}
              </p>
            </div>
          </div>
          
          {/* 统计信息 */}
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <span>{t('total_foods', { count: total })}</span>
            <span>•</span>
            <span>{t('page_info', { page: page })}</span>
          </div>
        </div>
      </div>

      {/* 搜索和排序 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <CategorySearchBar
            categorySlug={resolvedParams.slug}
            locale={resolvedParams.locale}
            initialSearch={searchQuery}
          />
          <CategorySortSelector
            sortBy={sortBy}
            page={page}
            pageSize={pageSize}
            total={total}
          />
        </div>
        
        {/* 搜索结果提示 */}
        {searchQuery && (
          <div className="mt-4 text-sm text-gray-600">
            {t('search_results', { query: searchQuery })}
          </div>
        )}
      </div>

      {/* 食物卡片列表 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
        {foods.length > 0 ? (
          <>
            <FoodCardList foods={foods} locale={resolvedParams.locale} />
            
            {/* 分页导航 */}
            <Pagination
              currentPage={page}
              totalPages={Math.ceil(total / pageSize)}
              baseUrl={`/category/${resolvedParams.slug}`}
              queryParams={{
                ...(sortBy !== 'name' ? { sort: sortBy } : {}),
                ...(searchQuery ? { search: searchQuery } : {})
              }}
              locale={resolvedParams.locale}
              previousText={t('previous_page')}
              nextText={t('next_page')}
            />
          </>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('no_data_title', { categoryName })}
            </h3>
            <p className="text-gray-500 mb-6">
              {t('no_data_description')}
            </p>
            <a
              href={`/${resolvedParams.locale}`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              {t('back_to_home')}
            </a>
          </div>
        )}
      </div>

      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": categoryName,
            "description": categoryDescription,
            "url": `https://howlongfresh.site/category/${resolvedParams.slug}`,
            "mainEntity": {
              "@type": "ItemList",
              "numberOfItems": total,
              "itemListElement": foods.slice(0, 10).map((food, index) => ({
                "@type": "ListItem",
                "position": index + 1,
                "item": {
                  "@type": "Recipe",
                  "name": food.name,
                  "description": `${food.name}的保存方法和保质期信息`,
                }
              }))
            }
          })
        }}
      />
    </div>
  );
}

import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { searchFood } from '@/lib/supabase-food-service';
import FoodDetailView from '@/components/FoodDetailView';

interface FoodDetailPageProps {
  params: Promise<{
    locale: string;
    name: string;
  }>;
}

export async function generateMetadata({ params }: FoodDetailPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const foodName = decodeURIComponent(resolvedParams.name);
  const t = await getTranslations({locale: resolvedParams.locale, namespace: 'food_detail.metadata'});
  
  // Import the translation service
  const { getFoodNameTranslation } = await import('@/lib/food-translations');
  
  // 尝试获取食物信息
  const food = await searchFood(foodName, 'all');
  
  if (!food) {
    return {
      title: t('food_not_found_title'),
      description: t('food_not_found_description'),
    };
  }

  // Get translated food name
  const translatedFoodName = getFoodNameTranslation(food.name, resolvedParams.locale);

  return {
    title: t('title_template', { name: translatedFoodName }),
    description: t('description_template', {
      name: translatedFoodName,
      refrigerated: food.storage.refrigerated || 0,
      frozen: food.storage.frozen || 0,
      room: food.storage.room_temperature || 0
    }),
    keywords: t('keywords_template', { name: translatedFoodName }),
    openGraph: {
      title: t('title_template', { name: translatedFoodName }),
      description: t('description_template', {
        name: translatedFoodName,
        refrigerated: food.storage.refrigerated || 0,
        frozen: food.storage.frozen || 0,
        room: food.storage.room_temperature || 0
      }),
      type: 'article',
    },
  };
}

export default async function FoodDetailPage({ params }: FoodDetailPageProps) {
  const resolvedParams = await params;
  const foodName = decodeURIComponent(resolvedParams.name);

  // 获取食物信息
  const food = await searchFood(foodName, 'all');

  if (!food) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-background">
      <FoodDetailView food={food} locale={resolvedParams.locale} />
    </div>
  );
}

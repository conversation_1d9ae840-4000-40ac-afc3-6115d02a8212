'use client';

import Link from 'next/link';
import { Search, ArrowLeft, Home } from 'lucide-react';

export default function FoodNotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* 404图标 */}
        <div className="mb-8">
          <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center">
            <Search className="w-12 h-12 text-gray-400" />
          </div>
        </div>

        {/* 标题和描述 */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          食物未找到
        </h1>
        <p className="text-gray-600 mb-8">
          抱歉，我们没有找到您要查找的食物信息。可能是食物名称不正确，或者我们的数据库中还没有收录这种食物。
        </p>

        {/* 建议操作 */}
        <div className="space-y-4">
          <div className="bg-card rounded-lg p-4 border">
            <h3 className="font-semibold text-gray-900 mb-2">您可以尝试：</h3>
            <ul className="text-sm text-gray-600 space-y-1 text-left">
              <li>• 检查食物名称的拼写</li>
              <li>• 尝试使用更通用的名称（如"苹果"而不是"红富士苹果"）</li>
              <li>• 使用英文名称搜索</li>
              <li>• 浏览相关分类查找类似食物</li>
            </ul>
          </div>

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={() => window.history.back()}
              className="flex-1 bg-muted hover:bg-muted/80 text-foreground font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回上页
            </button>
            <Link
              href="/"
              className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
            >
              <Home className="w-4 h-4 mr-2" />
              回到首页
            </Link>
          </div>

          {/* 搜索建议 */}
          <Link
            href="/#search"
            className="inline-flex items-center text-green-600 hover:text-green-700 font-medium"
          >
            <Search className="w-4 h-4 mr-2" />
            尝试重新搜索
          </Link>
        </div>

        {/* 联系信息 */}
        <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            如果您认为这是一个错误，或者希望我们添加某种食物的信息，
            请通过邮件联系我们：
            <a 
              href="mailto:<EMAIL>" 
              className="font-medium underline hover:no-underline ml-1"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

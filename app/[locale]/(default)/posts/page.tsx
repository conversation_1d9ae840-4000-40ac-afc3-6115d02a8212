import Blog from "@/components/blocks/blog";
import { Blog as BlogType } from "@/types/blocks/blog";
import { getPostsByLocale, getPostCategories } from "@/models/post";
import { getTranslations } from "next-intl/server";

// 分类信息映射
const categoryInfo = {
  'food_safety': { name: 'Food Safety', name_zh: '食品安全', icon: '🛡️' },
  'frozen_foods': { name: 'Frozen Foods', name_zh: '冷冻食品', icon: '❄️' },
  'refrigerated_foods': { name: 'Refrigerated Foods', name_zh: '冷藏食品', icon: '🧊' },
  'expiration_dates': { name: 'Expiration Dates', name_zh: '有效期', icon: '📅' },
  'preparation': { name: 'Preparation', name_zh: '食品准备', icon: '🍳' },
  'storage_tips': { name: 'Storage Tips', name_zh: '储存技巧', icon: '💡' },
};

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/posts`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/posts`;
  }

  return {
    title: "Food Storage FAQ | HowLongFresh",
    description: "Expert answers to your food storage questions. Learn about food safety, proper storage methods, and shelf life.",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

interface PostsPageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ category?: string }>;
}

export default async function PostsPage({ 
  params, 
  searchParams 
}: PostsPageProps) {
  const { locale } = await params;
  const { category } = await searchParams;
  const t = await getTranslations();

  // 获取文章和分类统计
  const [posts, categoryStats] = await Promise.all([
    getPostsByLocale(locale, 1, 150, category),
    getPostCategories(locale)
  ]);

  // 转换分类统计为带有完整信息的数组
  const categories = categoryStats.map(stat => ({
    ...stat,
    ...categoryInfo[stat.name as keyof typeof categoryInfo] || {
      name: stat.name,
      name_zh: stat.name,
      icon: '📝'
    }
  }));

  const blog: BlogType & { 
    categories?: typeof categories, 
    selectedCategory?: string | null 
  } = {
    title: category 
      ? `${categoryInfo[category as keyof typeof categoryInfo]?.icon} ${
          locale.startsWith('zh') 
            ? categoryInfo[category as keyof typeof categoryInfo]?.name_zh 
            : categoryInfo[category as keyof typeof categoryInfo]?.name
        } FAQ`
      : "Food Storage FAQ",
    description: category
      ? `Expert answers about ${categoryInfo[category as keyof typeof categoryInfo]?.name || category}`
      : "Expert answers to your food storage questions",
    items: posts,
    read_more_text: t("blog.read_more_text"),
    categories,
    selectedCategory: category || null,
  };

  return <Blog blog={blog} locale={locale} />;
}
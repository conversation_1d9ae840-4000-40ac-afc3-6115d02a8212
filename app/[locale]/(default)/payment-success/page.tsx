import { Suspense } from "react";
import { getTranslations } from "next-intl/server";
import PaymentSuccessContent from "@/components/payment/payment-success-content";

interface Props {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ order_no?: string; checkout_id?: string }>;
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "payment" });
  
  return {
    title: t("success.title") || "Payment Success",
    description: t("success.description") || "Your payment has been processed successfully.",
  };
}

export default async function PaymentSuccessPage({ params, searchParams }: Props) {
  const { locale } = await params;
  const { order_no, checkout_id } = await searchParams;

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentSuccessContent 
        orderNo={order_no} 
        checkoutId={checkout_id} 
        locale={locale} 
      />
    </Suspense>
  );
}
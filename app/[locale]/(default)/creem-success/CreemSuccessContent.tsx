'use client';

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Home, User, AlertCircle } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface Props {
  locale: string;
  searchParams: {
    order_no?: string;
    request_id?: string;
    checkout_id?: string;
    order_id?: string;
    customer_id?: string;
    subscription_id?: string;
    product_id?: string;
    signature?: string;
  };
}

export default function CreemSuccessContent({ locale, searchParams }: Props) {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 处理支付回调
    const processPayment = async () => {
      try {
        // 调用 API 验证和处理支付
        const response = await fetch('/api/creem-callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(searchParams),
        });

        const result = await response.json();

        if (!response.ok || result.code !== 0) {
          setError(result.message || 'Payment verification failed');
          setIsProcessing(false);
          return;
        }

        // 支付验证成功
        setIsProcessing(false);
      } catch (error) {
        console.error('Payment processing error:', error);
        setError('Payment processing failed');
        setIsProcessing(false);
      }
    };

    processPayment();
  }, [searchParams]);

  if (isProcessing) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto">
          <Card className="text-center">
            <CardContent className="py-12">
              <div className="animate-pulse">
                <p className="text-lg">
                  {locale === 'zh' ? '正在处理您的支付...' : 'Processing your payment...'}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto">
          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertCircle className="w-8 h-8 text-red-600" />
              </div>
              <CardTitle className="text-2xl font-bold text-red-600">
                {locale === 'zh' ? '支付验证失败' : 'Payment Verification Failed'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-muted-foreground">
                {error}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <Link href={`/${locale}`}>
                    <Home className="w-4 h-4 mr-2" />
                    {locale === 'zh' ? '返回首页' : 'Back to Home'}
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href={`/${locale}/#pricing`}>
                    {locale === 'zh' ? '重新选择方案' : 'Choose Another Plan'}
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto">
        <Card className="text-center">
          <CardHeader>
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-green-600">
              {locale === 'zh' ? '支付成功！' : 'Payment Successful!'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-muted-foreground">
              <p className="text-lg mb-4">
                {locale === 'zh' 
                  ? '感谢您选择 HowLongFresh 专业版！您的订阅已激活。' 
                  : 'Thank you for choosing HowLongFresh Pro! Your subscription is now active.'}
              </p>
              
              {(searchParams.order_no || searchParams.order_id) && (
                <div className="bg-muted p-4 rounded-lg mb-4">
                  <p className="text-sm">
                    <strong>
                      {locale === 'zh' ? '订单号：' : 'Order ID: '}
                    </strong>
                    {searchParams.order_id || searchParams.order_no}
                  </p>
                  {searchParams.subscription_id && (
                    <p className="text-sm mt-1">
                      <strong>
                        {locale === 'zh' ? '订阅ID：' : 'Subscription ID: '}
                      </strong>
                      {searchParams.subscription_id}
                    </p>
                  )}
                </div>
              )}

              <div className="text-left space-y-2 mb-6">
                <h3 className="font-semibold text-foreground">
                  {locale === 'zh' ? '您现在可以享受：' : 'You now have access to:'}
                </h3>
                <ul className="space-y-1 text-sm">
                  <li>• {locale === 'zh' ? '3个月内 1500 次查询' : '1500 queries for 3 months'}</li>
                  <li>• {locale === 'zh' ? '完整食物数据库' : 'Complete food database'}</li>
                  <li>• {locale === 'zh' ? '智能保质期提醒' : 'Smart expiration reminders'}</li>
                  <li>• {locale === 'zh' ? '批量查询功能' : 'Bulk query functionality'}</li>
                  <li>• {locale === 'zh' ? 'API 访问权限' : 'API access'}</li>
                  <li>• {locale === 'zh' ? '优先客服支持' : 'Priority customer support'}</li>
                </ul>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild>
                <Link href={`/${locale}`}>
                  <Home className="w-4 h-4 mr-2" />
                  {locale === 'zh' ? '返回首页' : 'Back to Home'}
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href={`/${locale}/my-credits`}>
                  <User className="w-4 h-4 mr-2" />
                  {locale === 'zh' ? '查看我的账户' : 'My Account'}
                </Link>
              </Button>
            </div>

            <div className="text-xs text-muted-foreground pt-4 border-t">
              <p>
                {locale === 'zh' 
                  ? '如有任何问题，请联系我们的客服团队。' 
                  : 'If you have any questions, please contact our support team.'}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
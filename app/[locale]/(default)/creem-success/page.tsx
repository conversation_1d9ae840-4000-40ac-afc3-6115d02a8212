import { Suspense } from "react";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";
import CreemSuccessContent from "./CreemSuccessContent";

interface Props {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ 
    order_no?: string;
    request_id?: string;
    checkout_id?: string;
    order_id?: string;
    customer_id?: string;
    subscription_id?: string;
    product_id?: string;
    signature?: string;
  }>;
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "payment" });
  
  return {
    title: t("success.title") || "Payment Success",
    description: t("success.description") || "Your payment has been processed successfully.",
  };
}

export default async function CreemSuccessPage({ params, searchParams }: Props) {
  const { locale } = await params;
  const searchParamsData = await searchParams;
  
  // 将所有参数传递给客户端组件处理
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CreemSuccessContent 
        locale={locale} 
        searchParams={searchParamsData}
      />
    </Suspense>
  );
}
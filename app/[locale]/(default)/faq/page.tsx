import React from 'react';
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import FAQList from '@/components/faq/FAQList';
import { getAllFAQs, getFAQCategories } from '@/lib/faq-service';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { BookOpen, MessageCircleQuestion, Shield, Clock } from 'lucide-react';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations('faq');

  return {
    title: t('metadata.title'),
    description: t('metadata.description'),
    keywords: t('metadata.keywords'),
    openGraph: {
      title: t('metadata.og_title'),
      description: t('metadata.og_description'),
      type: 'website',
    }
  };
}

interface FAQPageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function FAQPage({ params: paramsPromise, searchParams }: FAQPageProps) {
  // 获取路由参数和搜索参数
  const [params, searchParamsData] = await Promise.all([paramsPromise, searchParams]);
  const { locale } = params;
  const highlightId = typeof searchParamsData.highlight === 'string' ? searchParamsData.highlight : undefined;

  // 获取翻译
  const t = await getTranslations('faq');

  // 获取FAQ数据
  const [faqs, categories] = await Promise.all([
    getAllFAQs(),
    getFAQCategories()
  ]);

  // 统计信息
  const totalFAQs = faqs.length;
  const totalCategories = categories.filter(c => c.count > 0).length;
  const avgWordCount = Math.round(
    faqs.reduce((sum, faq) => sum + (faq.word_count || 0), 0) / faqs.length
  );

  return (
    <div className="min-h-screen bg-background">
      {/* 页面头部 */}
      <div className="bg-card border-b">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <Badge variant="outline" className="text-sm font-medium">
                <MessageCircleQuestion className="w-4 h-4 mr-1" />
                FAQ
              </Badge>
            </div>
            <h1 className="text-4xl font-bold text-gray-900">
              {t('title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('description')}
            </p>
          </div>

          {/* 统计信息卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-3">
                  <BookOpen className="w-8 h-8 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{totalFAQs}</div>
                <div className="text-sm text-gray-600">{t('stats.total_faqs')}</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-3">
                  <Shield className="w-8 h-8 text-green-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{totalCategories}</div>
                <div className="text-sm text-gray-600">{t('stats.total_categories')}</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-3">
                  <Clock className="w-8 h-8 text-purple-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{avgWordCount}</div>
                <div className="text-sm text-gray-600">{t('stats.avg_words')}</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* FAQ内容 */}
      <div className="container mx-auto px-4 py-12">
        <FAQList
          faqs={faqs}
          categories={categories}
          showSearch={true}
          showCategories={true}
          highlightId={highlightId}
          locale={locale}
        />
      </div>

      {/* 底部信息 */}
      <div className="bg-card border-t mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">
              {t('no_answer_found')}
            </h3>
            <p className="text-gray-600">
              {t('contact_description')}
            </p>
            <div className="flex justify-center space-x-4">
              <Badge variant="outline" className="text-sm">
                <Shield className="w-3 h-3 mr-1" />
                {t('usda_data')}
              </Badge>
              <Badge variant="outline" className="text-sm">
                <BookOpen className="w-3 h-3 mr-1" />
                {t('stilltasty_content')}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": faqs.slice(0, 10).map(faq => ({
              "@type": "Question",
              "name": faq.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": faq.answer_summary || faq.answer.substring(0, 200) + "..."
              }
            }))
          })
        }}
      />
    </div>
  );
}

// 生成静态参数（如果需要）
export function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'zh' }
  ];
}

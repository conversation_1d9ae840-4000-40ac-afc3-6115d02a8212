import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ViewFAQButton } from '@/components/ui/view-source-button';
import { ExternalLink, CheckCircle, XCircle } from 'lucide-react';

export default function DemoFAQFix() {
  return (
    <div className="container mx-auto px-4 py-12 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">FAQ链接修复演示</h1>
        <p className="text-lg text-gray-600">
          展示"查看原文"按钮修复前后的对比
        </p>
      </div>

      {/* 修复前后对比 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 修复前 */}
        <Card className="border-red-200">
          <CardHeader>
            <div className="flex items-center gap-2">
              <XCircle className="w-5 h-5 text-red-500" />
              <CardTitle className="text-red-700">修复前 (问题)</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-semibold text-red-900 mb-2">问题描述：</h4>
              <ul className="space-y-1 text-sm text-red-800">
                <li>❌ "查看原文"指向外部StillTasty网站</li>
                <li>❌ 用户离开了我们的网站</li>
                <li>❌ 无法利用我们的FAQ系统功能</li>
                <li>❌ 用户体验不连贯</li>
              </ul>
            </div>

            {/* 模拟修复前的链接 */}
            <div className="pt-2 border-t">
              <a
                href="https://stilltasty.com/fooditems/index/18665"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
              >
                <ExternalLink className="h-3 w-3" />
                查看原文 - StillTasty.com
              </a>
              <p className="text-xs text-gray-500 mt-1">
                ⚠️ 这会跳转到外部网站
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 修复后 */}
        <Card className="border-green-200">
          <CardHeader>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <CardTitle className="text-green-700">修复后 (解决方案)</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">解决方案：</h4>
              <ul className="space-y-1 text-sm text-green-800">
                <li>✅ "查看原文"指向内部FAQ页面</li>
                <li>✅ 用户留在我们的网站内</li>
                <li>✅ 利用高亮和展开功能</li>
                <li>✅ 提供更好的用户体验</li>
              </ul>
            </div>

            {/* 修复后的链接 */}
            <div className="pt-2 border-t">
              <ViewFAQButton 
                faqId="canned-food-leftovers-safety"
                text="查看原文"
                variant="ghost"
                size="sm"
                className="text-blue-600 hover:text-blue-800 p-0 h-auto"
              />
              <p className="text-xs text-gray-500 mt-1">
                ✅ 这会跳转到内部FAQ页面并高亮显示
              </p>
              
              {/* 原始来源信息 */}
              <div className="mt-2">
                <a
                  href="https://stilltasty.com/fooditems/index/18665"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-xs text-gray-500 hover:text-gray-700"
                >
                  <ExternalLink className="h-3 w-3" />
                  数据来源: StillTasty.com
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 技术实现说明 */}
      <Card>
        <CardHeader>
          <CardTitle>技术实现说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">修改的组件：</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• <code>components/faq/FAQList.tsx</code></li>
                <li>• <code>components/faq/PopularFAQs.tsx</code></li>
                <li>• <code>app/[locale]/(default)/faq/page.tsx</code></li>
                <li>• <code>components/ui/view-source-button.tsx</code></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">实现的功能：</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• 内部FAQ页面跳转</li>
                <li>• 自动高亮和展开</li>
                <li>• 平滑滚动定位</li>
                <li>• 保留原始数据来源信息</li>
              </ul>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">生成的链接格式：</h4>
            <code className="text-sm text-blue-800 bg-white px-2 py-1 rounded">
              /zh/faq?highlight=canned-food-leftovers-safety
            </code>
            <p className="text-xs text-blue-700 mt-2">
              这个链接会自动高亮显示ID为"canned-food-leftovers-safety"的FAQ项目
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 测试按钮 */}
      <Card>
        <CardHeader>
          <CardTitle>立即测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <ViewFAQButton 
              faqId="canned-food-leftovers-safety"
              text="测试：查看罐头食品安全FAQ"
              variant="default"
              size="default"
            />
            <a
              href="/faq"
              className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              浏览所有FAQ
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

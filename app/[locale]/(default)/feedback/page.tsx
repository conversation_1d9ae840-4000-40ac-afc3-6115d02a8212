import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Icon from "@/components/icon";
import Link from "next/link";

export default async function FeedbackPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  
  const isZh = locale === 'zh';
  
  return (
    <div className="container max-w-4xl mx-auto py-16">
      <Card>
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-green-100 rounded-full">
              <Icon name="RiMessage3Line" className="w-8 h-8 text-green-600" />
            </div>
          </div>
          <CardTitle className="text-2xl">
            {isZh ? '感谢您的反馈' : 'Thank You for Your Feedback'}
          </CardTitle>
          <CardDescription className="text-lg">
            {isZh 
              ? '您的意见对我们非常重要，帮助我们不断改进服务。' 
              : 'Your feedback is very important to us and helps us improve our service.'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              {isZh 
                ? '您可以通过以下方式联系我们：' 
                : 'You can contact us through:'}
            </p>
            
            <div className="space-y-3">
              <a href="mailto:<EMAIL>" className="flex items-center justify-center gap-2 p-3 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <Icon name="RiMailLine" className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                <span><EMAIL></span>
              </a>
            </div>
            
            <div className="pt-6">
              <Link href={`/${locale}`}>
                <Button variant="outline" size="lg">
                  <Icon name="RiArrowLeftLine" className="mr-2 w-4 h-4" />
                  {isZh ? '返回首页' : 'Back to Home'}
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
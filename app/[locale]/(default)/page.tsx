import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import dynamic from "next/dynamic";
import FoodHero from "@/components/blocks/hero/food-hero";
import CategoryGridClient from "@/components/CategoryGridClient";
import FeedbackCTA from "@/components/blocks/feedback-cta";
import { FoodProvider } from "@/contexts/food-context";
import { getLandingPage } from "@/services/page";

// 动态导入Pricing组件，减少首屏加载
const Pricing = dynamic(() => import("@/components/blocks/pricing"), {
  ssr: true,
  loading: () => (
    <div className="py-16">
      <div className="container">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
        </div>
      </div>
    </div>
  ),
});

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);

  return (
    <>
      {page.hero && (
        <FoodProvider>
          <FoodHero hero={page.hero} />
        </FoodProvider>
      )}

      {/* 9宫格分类导航 - 使用客户端组件避免阻塞渲染 */}
      <CategoryGridClient locale={locale} />

      {/* 核心功能介绍 - 保留 */}
      {page.feature && <Feature section={page.feature} />}

      {/* 定价方案 */}
      {page.pricing && <Pricing pricing={page.pricing} />}

      {/* FAQ区块 - 保留但需要修改内容 */}
      {page.faq && <FAQ section={page.faq} locale={locale} />}
      
      {/* 用户反馈引导 */}
      <FeedbackCTA locale={locale} />
    </>
  );
}

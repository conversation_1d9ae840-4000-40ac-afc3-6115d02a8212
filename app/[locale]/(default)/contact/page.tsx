import { getTranslations } from "next-intl/server";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Mail, Phone, MessageCircle } from "lucide-react";

interface Props {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  
  return {
    title: locale === 'zh' ? '联系我们 - HowLongFresh' : 'Contact Us - HowLongFresh',
    description: locale === 'zh' 
      ? '联系 HowLongFresh 团队，了解企业版解决方案，获取专业的食物保鲜管理服务。'
      : 'Contact HowLongFresh team for enterprise solutions and professional food freshness management services.',
  };
}

export default async function ContactPage({ params }: Props) {
  const { locale } = await params;

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">
            {locale === 'zh' ? '联系我们' : 'Contact Us'}
          </h1>
          <p className="text-xl text-muted-foreground">
            {locale === 'zh' 
              ? '了解 HowLongFresh 企业版解决方案，获取专业的食物保鲜管理服务'
              : 'Learn about HowLongFresh Enterprise solutions for professional food freshness management'}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* 企业版特性 */}
          <Card>
            <CardHeader>
              <CardTitle>
                {locale === 'zh' ? '企业版特性' : 'Enterprise Features'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                  <div>
                    <h4 className="font-semibold">
                      {locale === 'zh' ? '无限查询' : 'Unlimited Queries'}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {locale === 'zh' ? '支持大规模食品库存管理' : 'Support for large-scale food inventory management'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                  <div>
                    <h4 className="font-semibold">
                      {locale === 'zh' ? '多用户协作' : 'Multi-user Collaboration'}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {locale === 'zh' ? '团队协作，统一管理' : 'Team collaboration and unified management'}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                  <div>
                    <h4 className="font-semibold">
                      {locale === 'zh' ? '定制化解决方案' : 'Custom Solutions'}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {locale === 'zh' ? '根据业务需求定制功能' : 'Customized features based on business needs'}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                  <div>
                    <h4 className="font-semibold">
                      {locale === 'zh' ? '专属技术支持' : 'Dedicated Support'}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {locale === 'zh' ? '24/7 技术支持和SLA保障' : '24/7 technical support with SLA guarantee'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 联系方式 */}
          <Card>
            <CardHeader>
              <CardTitle>
                {locale === 'zh' ? '联系方式' : 'Get in Touch'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-primary" />
                  <div>
                    <p className="font-medium">
                      {locale === 'zh' ? '邮箱' : 'Email'}
                    </p>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <MessageCircle className="w-5 h-5 text-primary" />
                  <div>
                    <p className="font-medium">
                      {locale === 'zh' ? '在线咨询' : 'Live Chat'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {locale === 'zh' ? '工作日 9:00-18:00' : 'Weekdays 9:00-18:00'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Phone className="w-5 h-5 text-primary" />
                  <div>
                    <p className="font-medium">
                      {locale === 'zh' ? '电话' : 'Phone'}
                    </p>
                    <p className="text-sm text-muted-foreground">+****************</p>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <Button 
                  className="w-full" 
                  onClick={() => window.location.href = 'mailto:<EMAIL>?subject=Enterprise Inquiry'}
                >
                  <Mail className="w-4 h-4 mr-2" />
                  {locale === 'zh' ? '发送邮件咨询' : 'Send Email Inquiry'}
                </Button>
              </div>

              <div className="text-xs text-muted-foreground text-center">
                {locale === 'zh' 
                  ? '我们将在24小时内回复您的咨询'
                  : 'We will respond to your inquiry within 24 hours'}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 常见问题 */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>
                {locale === 'zh' ? '企业版常见问题' : 'Enterprise FAQ'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">
                    {locale === 'zh' ? '企业版适合什么类型的用户？' : 'Who is Enterprise suitable for?'}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {locale === 'zh' 
                      ? '餐饮企业、食品制造商、连锁超市、大型厨房等需要专业食品管理解决方案的企业。'
                      : 'Restaurants, food manufacturers, supermarket chains, commercial kitchens, and enterprises requiring professional food management solutions.'}
                  </p>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">
                    {locale === 'zh' ? '可以定制哪些功能？' : 'What can be customized?'}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {locale === 'zh' 
                      ? '数据库集成、品牌定制、特殊食品类别、报告格式、API接口等都可以根据需求定制。'
                      : 'Database integration, branding, special food categories, report formats, API interfaces, and more can be customized.'}
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">
                    {locale === 'zh' ? '如何开始使用？' : 'How to get started?'}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {locale === 'zh' 
                      ? '请联系我们的销售团队，我们会安排专业顾问了解您的需求并提供定制方案。'
                      : 'Contact our sales team, and we will arrange a professional consultant to understand your needs and provide a customized solution.'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
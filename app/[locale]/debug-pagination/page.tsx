import { getFoodsByCategory } from '@/lib/supabase-food-service';
import Pagination from '@/components/Pagination';

interface DebugPageProps {
  params: Promise<{
    locale: string;
  }>;
}

export default async function DebugPaginationPage({ params }: DebugPageProps) {
  const resolvedParams = await params;
  
  // 测试肉类分类
  const { foods, total, hasMore } = await getFoodsByCategory(
    'meat',
    1,
    24,
    'name'
  );
  
  const totalPages = Math.ceil(total / 24);
  
  return (
    <div className="max-w-4xl mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">调试分页组件</h1>
      
      <div className="mb-8 p-4 bg-gray-100 rounded">
        <h2 className="font-bold mb-2">数据信息：</h2>
        <ul>
          <li>分类: meat</li>
          <li>总数: {total}</li>
          <li>总页数: {totalPages}</li>
          <li>当前页数据: {foods.length} 条</li>
          <li>是否有更多: {hasMore ? '是' : '否'}</li>
          <li>Math.ceil(212 / 24) = {Math.ceil(212 / 24)}</li>
        </ul>
      </div>
      
      <div className="border-2 border-red-500 p-4 rounded">
        <h3 className="font-bold mb-2">分页组件渲染测试：</h3>
        <p className="mb-4">以下是分页组件的渲染结果（应该显示9页）：</p>
        
        <Pagination
          currentPage={1}
          totalPages={totalPages}
          baseUrl="/category/meat"
          locale={resolvedParams.locale}
          previousText="上一页"
          nextText="下一页"
        />
      </div>
      
      <div className="mt-8 border-2 border-blue-500 p-4 rounded">
        <h3 className="font-bold mb-2">硬编码测试（totalPages=9）：</h3>
        
        <Pagination
          currentPage={1}
          totalPages={9}
          baseUrl="/category/meat"
          locale={resolvedParams.locale}
          previousText="上一页"
          nextText="下一页"
        />
      </div>
      
      <div className="mt-8">
        <h3 className="font-bold mb-2">调试信息：</h3>
        <pre className="bg-gray-800 text-white p-4 rounded overflow-x-auto">
{JSON.stringify({
  total,
  pageSize: 24,
  totalPages,
  shouldShowPagination: totalPages > 1,
  componentPropsReceived: {
    currentPage: 1,
    totalPages: totalPages
  }
}, null, 2)}
        </pre>
      </div>
    </div>
  );
}
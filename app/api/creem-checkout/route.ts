import { getUserEmail, getUserUuid } from "@/services/user";
import { insertOrder, updateOrderSession } from "@/models/order";
import { respData, respErr } from "@/lib/resp";
import { Order } from "@/types/order";
import { findUserByUuid } from "@/models/user";
import { getSnowId } from "@/lib/hash";
import { createCreemCheckoutSession, getCreemProductId } from "@/services/creem";

export async function POST(req: Request) {
  try {
    let {
      credits,
      currency,
      amount,
      interval,
      product_id,
      product_name,
      valid_months,
      cancel_url,
      success_url,
    } = await req.json();

    // 设置默认URL
    if (!cancel_url) {
      cancel_url = `${
        process.env.NEXT_PUBLIC_PAY_CANCEL_URL ||
        process.env.NEXT_PUBLIC_WEB_URL
      }`;
    }

    if (!success_url) {
      success_url = `${process.env.NEXT_PUBLIC_WEB_URL}/my-orders?payment=success`;
    }

    // 验证必需参数
    if (!amount || !interval || !currency || !product_id) {
      return respErr("invalid params");
    }

    if (!["year", "month", "one-time"].includes(interval)) {
      return respErr("invalid interval");
    }

    // 获取用户信息
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth, please sign-in");
    }

    let user_email = await getUserEmail();
    if (!user_email) {
      const user = await findUserByUuid(user_uuid);
      if (user) {
        user_email = user.email;
      }
    }
    if (!user_email) {
      return respErr("invalid user");
    }

    // 生成订单号
    const order_no = getSnowId();
    const currentDate = new Date();
    const created_at = currentDate.toISOString();

    // 计算过期时间
    let expired_at = "";
    const timePeriod = new Date(currentDate);
    timePeriod.setMonth(currentDate.getMonth() + valid_months);
    expired_at = timePeriod.toISOString();

    // 创建订单记录
    const order: Order = {
      order_no: order_no,
      created_at: created_at,
      user_uuid: user_uuid,
      user_email: user_email,
      amount: amount,
      interval: interval,
      expired_at: expired_at,
      status: "created",
      credits: credits,
      currency: currency,
      product_id: product_id,
      product_name: product_name,
      valid_months: valid_months,
    };

    await insertOrder(order);

    // 临时使用固定的测试产品ID，避免产品不存在的问题
    // 在实际使用中，您需要在Creem Dashboard中创建对应的产品
    let creemProductId: string;

    try {
      // 尝试获取配置的产品ID
      creemProductId = await getCreemProductId(product_id);
    } catch (error) {
      // 如果产品ID不存在，返回错误提示
      return respErr(`Creem product not configured for: ${product_id}. Please create the product in Creem Dashboard first.`);
    }

    // 创建 Creem 结账会话
    const checkoutSession = await createCreemCheckoutSession({
      product_id: creemProductId,
      customer_email: user_email,
      success_url: `${success_url}&order_no=${order_no}`,
      cancel_url: cancel_url,
      request_id: order_no.toString(), // 使用订单号作为请求ID
    });

    // 更新订单记录，保存 Creem 会话信息
    await updateOrderSession(
      order_no, 
      checkoutSession.id, 
      JSON.stringify(checkoutSession)
    );

    return respData({
      checkout_url: checkoutSession.checkout_url || checkoutSession.url,
      checkout_id: checkoutSession.id,
      order_no: order_no,
    });

  } catch (e: any) {
    console.log("Creem checkout failed: ", e);
    return respErr("checkout failed: " + e.message);
  }
}

import { NextResponse } from 'next/server';
import { getCategoryCounts } from '@/lib/supabase-food-service';
import { unstable_cache } from 'next/cache';

// 缓存分类统计数据，1小时更新一次
const getCachedCategoryCounts = unstable_cache(
  async () => {
    return await getCategoryCounts();
  },
  ['category-counts'],
  {
    revalidate: 3600, // 1 hour
    tags: ['categories']
  }
);

export async function GET() {
  try {
    const counts = await getCachedCategoryCounts();
    
    return NextResponse.json(counts, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
      },
    });
  } catch (error) {
    console.error('Error fetching category counts:', error);
    
    // 返回空对象作为降级方案
    return NextResponse.json({}, {
      status: 500,
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  }
}
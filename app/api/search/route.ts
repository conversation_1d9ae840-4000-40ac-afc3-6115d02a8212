import { NextRequest, NextResponse } from 'next/server';
import { searchFood, getFoodSuggestions } from '@/lib/supabase-food-service';
import { FoodResult } from '@/types/food';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '10');
    const suggestions = searchParams.get('suggestions') === 'true';

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query parameter "q" is required' },
        { status: 400 }
      );
    }

    // 如果请求建议
    if (suggestions) {
      const suggestionList = await getFoodSuggestions(query, limit);
      return NextResponse.json({
        suggestions: suggestionList
      });
    }

    // 搜索食物
    const result = await searchFood(query, 'all');
    
    if (!result) {
      return NextResponse.json({
        results: [],
        total: 0,
        message: `No results found for "${query}"`
      });
    }

    // 返回单个结果作为数组格式，保持API一致性
    return NextResponse.json({
      results: [result],
      total: 1,
      query: query
    });

  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

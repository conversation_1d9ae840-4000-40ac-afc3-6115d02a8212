import { NextRequest, NextResponse } from "next/server";
import { 
  getPasswordResetByToken, 
  markPasswordResetAsUsed,
  updateUserPassword,
  createUserPassword,
  getUserPasswordByUuid
} from "@/models/auth";
import { 
  hashPassword, 
  validatePassword,
  isTokenExpired 
} from "@/lib/auth-utils";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token, password } = body;

    // Validate input
    if (!token || !password) {
      return NextResponse.json(
        { error: "Token and password are required" },
        { status: 400 }
      );
    }

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.valid) {
      return NextResponse.json(
        { error: passwordValidation.message },
        { status: 400 }
      );
    }

    // Get reset record
    const resetRecord = await getPasswordResetByToken(token);
    
    if (!resetRecord) {
      return NextResponse.json(
        { error: "Invalid reset token" },
        { status: 404 }
      );
    }

    // Check if already used
    if (resetRecord.used_at) {
      return NextResponse.json(
        { error: "Reset token has already been used" },
        { status: 400 }
      );
    }

    // Check if token expired
    if (isTokenExpired(new Date(resetRecord.expires_at))) {
      return NextResponse.json(
        { error: "Reset token has expired" },
        { status: 410 }
      );
    }

    // Hash new password
    const hashedPassword = await hashPassword(password);

    // Update or create password
    if (resetRecord.user_uuid) {
      const existingPassword = await getUserPasswordByUuid(resetRecord.user_uuid);
      
      if (existingPassword) {
        // Update existing password
        await updateUserPassword(resetRecord.user_uuid, hashedPassword);
      } else {
        // Create new password record (for users who registered via OAuth)
        await createUserPassword({
          user_uuid: resetRecord.user_uuid,
          password_hash: hashedPassword,
        });
      }
    }

    // Mark reset token as used
    await markPasswordResetAsUsed(token);

    return NextResponse.json({
      success: true,
      message: "Password reset successfully. You can now sign in with your new password.",
    });
  } catch (error) {
    console.error("Password reset error:", error);
    return NextResponse.json(
      { error: "An error occurred while resetting your password" },
      { status: 500 }
    );
  }
}

// GET endpoint to validate token before showing reset form
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const token = searchParams.get("token");

    if (!token) {
      return NextResponse.json(
        { error: "Reset token is required" },
        { status: 400 }
      );
    }

    // Get reset record
    const resetRecord = await getPasswordResetByToken(token);
    
    if (!resetRecord) {
      return NextResponse.json(
        { error: "Invalid reset token" },
        { status: 404 }
      );
    }

    // Check if already used
    if (resetRecord.used_at) {
      return NextResponse.json(
        { error: "Reset token has already been used" },
        { status: 400 }
      );
    }

    // Check if token expired
    if (isTokenExpired(new Date(resetRecord.expires_at))) {
      return NextResponse.json(
        { error: "Reset token has expired" },
        { status: 410 }
      );
    }

    return NextResponse.json({
      valid: true,
      email: resetRecord.email,
    });
  } catch (error) {
    console.error("Token validation error:", error);
    return NextResponse.json(
      { error: "An error occurred while validating the token" },
      { status: 500 }
    );
  }
}
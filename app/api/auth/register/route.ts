import { NextRequest, NextResponse } from "next/server";
import { findUserByEmail, insertUser } from "@/models/user";
import { createUserPassword, createEmailVerification } from "@/models/auth";
import { 
  hashPassword, 
  validateEmail, 
  validatePassword, 
  generateVerificationToken,
  getTokenExpiryDate 
} from "@/lib/auth-utils";
import { sendVerificationEmail } from "@/lib/email";
import { getUuid } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";
import { getClientIp } from "@/lib/ip";
import { User } from "@/types/user";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, name, locale = "en" } = body;

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    // Validate email format
    if (!validateEmail(email)) {
      return NextResponse.json(
        { error: "Invalid email format" },
        { status: 400 }
      );
    }

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.valid) {
      return NextResponse.json(
        { error: passwordValidation.message },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await findUserByEmail(email);
    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 }
      );
    }

    // Create new user
    const userUuid = getUuid();
    const newUser: User = {
      uuid: userUuid,
      email,
      nickname: name || email.split("@")[0],
      avatar_url: "",
      signin_type: "credentials",
      signin_provider: "credentials",
      signin_openid: "",
      created_at: getIsoTimestr(),
      signin_ip: await getClientIp(),
    };

    // Insert user into database
    await insertUser(newUser);

    // Hash password and save
    const hashedPassword = await hashPassword(password);
    await createUserPassword({
      user_uuid: userUuid,
      password_hash: hashedPassword,
    });

    // Generate verification token
    const verificationToken = generateVerificationToken();
    const expiresAt = getTokenExpiryDate(24); // 24 hours

    // Save verification token
    await createEmailVerification({
      token: verificationToken,
      email,
      user_uuid: userUuid,
      expires_at: expiresAt.toISOString(),
    });

    // Send verification email
    try {
      await sendVerificationEmail(email, verificationToken, locale);
    } catch (emailError) {
      console.error("Failed to send verification email:", emailError);
      // Continue with registration even if email fails
    }

    return NextResponse.json({
      success: true,
      message: "Registration successful. Please check your email to verify your account.",
      user: {
        email: newUser.email,
        nickname: newUser.nickname,
      },
    });
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: "An error occurred during registration" },
      { status: 500 }
    );
  }
}
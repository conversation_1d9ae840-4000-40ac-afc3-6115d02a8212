import { NextRequest, NextResponse } from "next/server";
import { 
  getEmailVerificationByToken, 
  markEmailAsVerified,
  updateUserEmailVerified 
} from "@/models/auth";
import { isTokenExpired } from "@/lib/auth-utils";
import { sendWelcomeEmail } from "@/lib/email";
import { findUserByUuid } from "@/models/user";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const token = searchParams.get("token");
    const locale = searchParams.get("locale") || "en";

    if (!token) {
      return NextResponse.json(
        { error: "Verification token is required" },
        { status: 400 }
      );
    }

    // Get verification record
    const verification = await getEmailVerificationByToken(token);
    
    if (!verification) {
      return NextResponse.json(
        { error: "Invalid verification token" },
        { status: 404 }
      );
    }

    // Check if already verified
    if (verification.verified_at) {
      return NextResponse.json(
        { error: "Email already verified" },
        { status: 400 }
      );
    }

    // Check if token expired
    if (isTokenExpired(new Date(verification.expires_at))) {
      return NextResponse.json(
        { error: "Verification token has expired" },
        { status: 410 }
      );
    }

    // Mark email as verified
    await markEmailAsVerified(token);
    
    // Update user's email_verified status
    if (verification.user_uuid) {
      await updateUserEmailVerified(verification.user_uuid);
      
      // Get user details for welcome email
      const user = await findUserByUuid(verification.user_uuid);
      
      // Send welcome email
      if (user) {
        try {
          await sendWelcomeEmail(user.email, user.nickname || user.email, locale);
        } catch (emailError) {
          console.error("Failed to send welcome email:", emailError);
          // Continue even if welcome email fails
        }
      }
    }

    // Redirect to success page or return success response
    return NextResponse.redirect(
      new URL(`/${locale}/auth/verification-success`, request.url)
    );
  } catch (error) {
    console.error("Email verification error:", error);
    return NextResponse.json(
      { error: "An error occurred during email verification" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token, locale = "en" } = body;

    if (!token) {
      return NextResponse.json(
        { error: "Verification token is required" },
        { status: 400 }
      );
    }

    // Get verification record
    const verification = await getEmailVerificationByToken(token);
    
    if (!verification) {
      return NextResponse.json(
        { error: "Invalid verification token" },
        { status: 404 }
      );
    }

    // Check if already verified
    if (verification.verified_at) {
      return NextResponse.json(
        { 
          success: true,
          message: "Email already verified" 
        },
        { status: 200 }
      );
    }

    // Check if token expired
    if (isTokenExpired(new Date(verification.expires_at))) {
      return NextResponse.json(
        { error: "Verification token has expired" },
        { status: 410 }
      );
    }

    // Mark email as verified
    await markEmailAsVerified(token);
    
    // Update user's email_verified status
    if (verification.user_uuid) {
      await updateUserEmailVerified(verification.user_uuid);
      
      // Get user details for welcome email
      const user = await findUserByUuid(verification.user_uuid);
      
      // Send welcome email
      if (user) {
        try {
          await sendWelcomeEmail(user.email, user.nickname || user.email, locale);
        } catch (emailError) {
          console.error("Failed to send welcome email:", emailError);
          // Continue even if welcome email fails
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: "Email verified successfully",
    });
  } catch (error) {
    console.error("Email verification error:", error);
    return NextResponse.json(
      { error: "An error occurred during email verification" },
      { status: 500 }
    );
  }
}
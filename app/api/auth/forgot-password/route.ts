import { NextRequest, NextResponse } from "next/server";
import { findUserByEmail } from "@/models/user";
import { createPasswordReset } from "@/models/auth";
import { 
  validateEmail, 
  generatePasswordResetToken,
  getTokenExpiryDate 
} from "@/lib/auth-utils";
import { sendPasswordResetEmail } from "@/lib/email";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, locale = "en" } = body;

    // Validate input
    if (!email) {
      return NextResponse.json(
        { error: "Email is required" },
        { status: 400 }
      );
    }

    // Validate email format
    if (!validateEmail(email)) {
      return NextResponse.json(
        { error: "Invalid email format" },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await findUserByEmail(email);
    
    // Always return success to prevent email enumeration
    if (!user || !user.uuid) {
      return NextResponse.json({
        success: true,
        message: "If an account exists with this email, you will receive a password reset link.",
      });
    }

    // Generate reset token
    const resetToken = generatePasswordResetToken();
    const expiresAt = getTokenExpiryDate(24); // 24 hours

    // Save reset token
    await createPasswordReset({
      token: resetToken,
      email,
      user_uuid: user.uuid,
      expires_at: expiresAt.toISOString(),
    });

    // Send reset email
    try {
      await sendPasswordResetEmail(email, resetToken, locale);
    } catch (emailError) {
      console.error("Failed to send password reset email:", emailError);
      // Still return success to prevent information leakage
    }

    return NextResponse.json({
      success: true,
      message: "If an account exists with this email, you will receive a password reset link.",
    });
  } catch (error) {
    console.error("Forgot password error:", error);
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}
import { NextResponse } from "next/server";
import { findOrderByOrderNo, updateOrderStatus } from "@/models/order";
import { updateCreditForOrder } from "@/services/credit";
import { updateAffiliateForOrder } from "@/services/affiliate";
import { verifyCreemSignature } from "@/services/creem";
import { respData, respErr } from "@/lib/resp";
import { getIsoTimestr } from "@/lib/time";

export async function POST(req: Request) {
  try {
    // 记录请求头信息
    const headers: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      if (key.toLowerCase().includes('creem') || key.toLowerCase().includes('signature')) {
        headers[key] = value;
      }
    });
    
    const params = await req.json();
    
    console.log('========== Creem Webhook Received ==========');
    console.log('Time:', new Date().toISOString());
    console.log('Headers:', headers);
    console.log('Body:', JSON.stringify(params, null, 2));
    console.log('==========================================');
    
    // 暂时跳过签名验证，先让流程能走通
    // TODO: 需要了解 Creem 的签名验证规则
    /*
    const isValidSignature = verifyCreemSignature(params, params.signature);
    if (!isValidSignature) {
      console.error('Invalid Creem signature');
      return respErr('Invalid signature');
    }
    */
    
    // 获取订单号 - 根据Creem的实际数据结构
    let orderNo = null;
    
    // Creem在不同事件中订单号的位置不同
    if (params.eventType === 'checkout.completed') {
      // checkout.completed 事件：订单号在 object.request_id
      orderNo = params.object?.request_id;
    } else if (params.eventType === 'subscription.paid' || params.eventType === 'subscription.active') {
      // subscription 事件：需要从其他地方获取
      // 这些事件可能不包含订单号，我们可以跳过或使用其他标识
      console.log(`Received ${params.eventType} event - may not contain order number`);
      
      // 如果是订阅事件，可以返回成功，避免Creem重试
      return respData({
        success: true,
        message: `${params.eventType} event received`,
        event_type: params.eventType,
      });
    } else {
      // 尝试其他可能的位置
      orderNo = params.request_id || params.order_no || params.object?.order_no || params.metadata?.order_no;
    }
    
    console.log('Event type:', params.eventType);
    console.log('Extracted order number:', orderNo);
    
    if (!orderNo) {
      console.error('Order number not found for event:', params.eventType);
      console.error('Available fields:', JSON.stringify(Object.keys(params.object || {})));
      return respErr('Order number not found');
    }

    // 查找订单
    const order = await findOrderByOrderNo(orderNo);
    console.log('Order found:', order ? { order_no: order.order_no, status: order.status, user_uuid: order.user_uuid } : null);
    
    if (!order) {
      console.error('Order not found:', orderNo);
      return respErr('Order not found');
    }
    
    if (order.status !== "created") {
      console.log('Order already processed:', { order_no: orderNo, status: order.status });
      // 返回成功，避免 Creem 重试
      return respData({
        success: true,
        message: 'Order already processed',
      });
    }

    // 更新订单状态为已支付
    const paid_at = getIsoTimestr();
    const paid_email = order.user_email;
    const paid_detail = JSON.stringify(params);

    try {
      await updateOrderStatus(orderNo, 'paid', paid_at, paid_email, paid_detail);
      console.log('Order status updated successfully:', orderNo);
    } catch (error) {
      console.error('Failed to update order status:', error);
      return respErr('Failed to update order status');
    }

    // 处理积分和推荐奖励
    if (order.user_uuid) {
      try {
        if (order.credits > 0) {
          console.log(`Adding ${order.credits} credits for user ${order.user_uuid}`);
          await updateCreditForOrder(order);
          console.log('Credits added successfully');
        }

        // 处理推荐奖励
        await updateAffiliateForOrder(order);
        console.log('Affiliate rewards processed');
      } catch (error) {
        console.error('Failed to process credits or affiliates:', error);
        // 不阻止支付成功流程
      }
    }
    
    console.log('========== Webhook Processing Complete ==========');
    console.log('Order:', orderNo);
    console.log('Status: Success');
    console.log('===============================================');
    
    return respData({
      success: true,
      message: 'Payment processed successfully',
    });
    
  } catch (error: any) {
    console.error('Creem callback error:', error);
    return respErr(error.message || 'Payment processing failed');
  }
}
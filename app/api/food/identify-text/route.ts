import { NextRequest, NextResponse } from 'next/server';
import { FoodResult } from '@/types/food';
import { intelligentFoodSearch } from '@/lib/ai-food-service';
import { searchFood } from '@/lib/supabase-client';
import { USDA_FOOD_DATABASE, type FoodItem } from '@/lib/usda-food-database';

// 本地补充数据库（用于 USDA 数据库中没有的食物）
const LOCAL_FOOD_DATABASE: Record<string, Omit<FoodResult, 'confidence'>> = {
  // 本地补充数据（USDA 数据库中没有的食物）
  // 这些数据作为 USDA 数据的补充
  'durian': {
    name: 'Durian',
    category: 'Fruit',
    storage: { refrigerated: 5, frozen: 180, room_temperature: 2 },
    tips: ['Store in well-ventilated area due to strong smell', 'Refrigerate when ripe', 'Freeze for longer storage']
  },
  // 亚洲特色食物（USDA 数据库中可能没有的）
  'tofu': {
    name: 'Tofu',
    category: 'Vegetarian Protein',
    storage: { refrigerated: 7, frozen: 180, room_temperature: 0 },
    tips: ['Keep refrigerated in water', 'Change water daily', 'Freeze for different texture']
  },
  'kimchi': {
    name: 'Kimchi',
    category: 'Fermented Vegetable',
    storage: { refrigerated: 60, frozen: 365, room_temperature: 3 },
    tips: ['Store in airtight container', 'Keep refrigerated for best quality', 'Fermentation continues slowly in fridge']
  },
  'miso': {
    name: 'Miso',
    category: 'Fermented Paste',
    storage: { refrigerated: 365, frozen: 730, room_temperature: 30 },
    tips: ['Store in refrigerator after opening', 'Use clean utensils to prevent contamination', 'Can be frozen for longer storage']
  },
  'soy_sauce': {
    name: 'Soy Sauce',
    category: 'Condiment',
    storage: { refrigerated: 730, frozen: 1095, room_temperature: 365 },
    tips: ['Store in cool, dark place', 'Refrigerate after opening for best quality', 'Check for sediment which is normal']
  }
};

// 中文食物名称映射（包含 USDA 数据库中的食物）
const CHINESE_FOOD_MAP: Record<string, string> = {
  // 水果类
  '苹果': 'apples',
  '香蕉': 'bananas',
  '橙子': 'citrus fruit',
  '橘子': 'citrus fruit',
  '芒果': 'papaya,_mango,_feijoa,_passionfruit,_casaha_melon',
  '葡萄': 'grapes',
  '草莓': 'strawberries',
  '西瓜': 'melons',
  '梨': 'peaches, nectarines, plums, pears, sapote',
  '梨子': 'peaches, nectarines, plums, pears, sapote',
  '蓝莓': 'blueberries',
  '樱桃': 'berries',
  '菠萝': 'pineapple',
  '牛油果': 'avocados',
  '椰子': 'coconuts',

  // 蔬菜类
  '生菜': 'lettuce',
  '胡萝卜': 'carrots, parsnips',
  '西红柿': 'tomatoes',
  '番茄': 'tomatoes',
  '土豆': 'potatoes',
  '马铃薯': 'potatoes',
  '洋葱': 'onions',
  '黄瓜': 'cucumbers',
  '西兰花': 'broccoli and broccoli raab (rapini)',
  '花椰菜': 'cauliflower',
  '芹菜': 'celery',
  '菠菜': 'greens',
  '白菜': 'cabbage',
  '茄子': 'eggplant',
  '辣椒': 'peppers',
  '玉米': 'corn on the cob',
  '蘑菇': 'mushrooms',

  // 乳制品和蛋类
  '牛奶': 'milk',
  '奶酪': 'cheese',
  '鸡蛋': 'eggs',
  '黄油': 'butter',
  '奶油': 'cream',
  '酸奶': 'yogurt',

  // 肉类和海鲜
  '鸡肉': 'chicken',
  '牛肉': 'beef',
  '猪肉': 'pork',
  '火鸡': 'turkey',
  '鱼': 'lean fish',
  '三文鱼': 'fatty fish',
  '虾': 'shrimp, crayfish',
  '螃蟹': 'crab meat',

  // 其他
  '面包': 'bread',
  '米饭': 'rice',
  '意大利面': 'pasta',
  '豆腐': 'tofu',
  '泡菜': 'kimchi',
  '味噌': 'miso',
  '生抽': 'soy_sauce',
  '老抽': 'soy_sauce'
};

async function findFood(query: string): Promise<FoodResult | null> {
  const normalizedQuery = query.toLowerCase().trim();

  try {
    // 1. Try AI-enhanced intelligent search (USDA data + AI optimization)
    console.log('🤖 Trying AI-enhanced search...');
    const aiResult = await intelligentFoodSearch(query);
    if (aiResult) {
      console.log('✨ AI-enhanced search successful');
      return aiResult;
    }

    // 2. Fallback to regular Supabase query
    console.log('🔄 Falling back to regular database search...');
    const supabaseResult = await searchFood(query, 'all');
    if (supabaseResult) {
      console.log('✅ Regular database search successful');
      return supabaseResult;
    }

    // 2. Fallback to local USDA database (direct match)
    if (normalizedQuery in USDA_FOOD_DATABASE) {
      const usdaFood = USDA_FOOD_DATABASE[normalizedQuery as keyof typeof USDA_FOOD_DATABASE];
      const storage = usdaFood.storage as any;
      return {
        name: usdaFood.name,
        category: usdaFood.category,
        storage: {
          refrigerated: storage?.refrigerated || 0,
          frozen: storage?.frozen || 0,
          room_temperature: storage?.room_temperature || 0
        },
        tips: usdaFood.tips,
        confidence: 0.95, // Slightly lower than Supabase data
        source: 'USDA',
        isUSDAData: true
      };
    }

    // 3. Local USDA database Chinese mapping
    if (query in CHINESE_FOOD_MAP) {
      const englishKey = CHINESE_FOOD_MAP[query as keyof typeof CHINESE_FOOD_MAP];
      if (englishKey in USDA_FOOD_DATABASE) {
        const usdaFood = USDA_FOOD_DATABASE[englishKey as keyof typeof USDA_FOOD_DATABASE];
        const storage = usdaFood.storage as any;
        return {
          name: usdaFood.name,
          category: usdaFood.category,
          storage: {
            refrigerated: storage?.refrigerated || 0,
            frozen: storage?.frozen || 0,
            room_temperature: storage?.room_temperature || 0
          },
          tips: usdaFood.tips,
          confidence: 0.95,
          source: 'USDA',
          isUSDAData: true
        };
      }
    }

    // 4. Local supplementary database matching
    if (normalizedQuery in LOCAL_FOOD_DATABASE) {
      return {
        ...LOCAL_FOOD_DATABASE[normalizedQuery as keyof typeof LOCAL_FOOD_DATABASE],
        confidence: 0.90,
        source: 'LOCAL',
        isUSDAData: false
      };
    }

    // 5. Local supplementary database Chinese mapping
    if (query in CHINESE_FOOD_MAP) {
      const englishKey = CHINESE_FOOD_MAP[query as keyof typeof CHINESE_FOOD_MAP];
      if (englishKey in LOCAL_FOOD_DATABASE) {
        return {
          ...LOCAL_FOOD_DATABASE[englishKey as keyof typeof LOCAL_FOOD_DATABASE],
          confidence: 0.90,
          source: 'LOCAL',
          isUSDAData: false
        };
      }
    }

    return null;

  } catch (error) {
    console.error('AI和数据库查询都失败:', error);

    // 最后回退到本地数据库
    console.log('🔄 最后回退到本地数据库...');
    return findFoodLocal(query);
  }
}

// 本地数据库查找函数（作为备用）
function findFoodLocal(query: string): FoodResult | null {
  const normalizedQuery = query.toLowerCase().trim();

  // Local USDA database matching
  if (normalizedQuery in USDA_FOOD_DATABASE) {
    const usdaFood = USDA_FOOD_DATABASE[normalizedQuery as keyof typeof USDA_FOOD_DATABASE];
    const storage = usdaFood.storage as any;
    return {
      name: usdaFood.name,
      category: usdaFood.category,
      storage: {
        refrigerated: storage?.refrigerated || 0,
        frozen: storage?.frozen || 0,
        room_temperature: storage?.room_temperature || 0
      },
      tips: usdaFood.tips,
      confidence: 0.95
    };
  }

  // Chinese mapping
  if (query in CHINESE_FOOD_MAP) {
    const englishKey = CHINESE_FOOD_MAP[query as keyof typeof CHINESE_FOOD_MAP];
    if (englishKey in USDA_FOOD_DATABASE) {
      const usdaFood = USDA_FOOD_DATABASE[englishKey as keyof typeof USDA_FOOD_DATABASE];
      const storage = usdaFood.storage as any;
      return {
        name: usdaFood.name,
        category: usdaFood.category,
        storage: {
          refrigerated: storage?.refrigerated || 0,
          frozen: storage?.frozen || 0,
          room_temperature: storage?.room_temperature || 0
        },
        tips: usdaFood.tips,
        confidence: 0.95
      };
    }
  }

  // 本地补充数据库
  if (normalizedQuery in LOCAL_FOOD_DATABASE) {
    return {
      ...LOCAL_FOOD_DATABASE[normalizedQuery as keyof typeof LOCAL_FOOD_DATABASE],
      confidence: 0.90
    };
  }

  return null;
}

export async function POST(request: NextRequest) {
  try {
    const { foodName, locale } = await request.json();

    if (!foodName || typeof foodName !== 'string') {
      return NextResponse.json(
        { error: 'Food name is required' },
        { status: 400 }
      );
    }
    
    // 查找食物信息（现在支持 AI 增强查询，包括纯AI查询）
    const result = await findFood(foodName);

    if (!result) {
      // 如果所有查询方式都失败，返回通用错误
      return NextResponse.json(
        {
          error: 'Query failed',
          message: `Unable to process your query for "${foodName}". Please try again or contact support.`
        },
        { status: 500 }
      );
    }

    return NextResponse.json(result);
    
  } catch (error) {
    console.error('Error in food identification:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

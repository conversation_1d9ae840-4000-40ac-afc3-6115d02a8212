import { NextRequest, NextResponse } from 'next/server';
import { FoodResult } from '@/types/food';
import { identifyFoodWithGemini } from '@/lib/openrouter-vision';
import { getUserUuid } from "@/services/user";
import { getUserCredits, decreaseCredits, CreditsTransType, CreditsAmount } from "@/services/credit";

// 模拟图片识别结果
const MOCK_IMAGE_RESULTS: FoodResult[] = [
  {
    name: 'Apple',
    category: 'Fruit',
    storage: { refrigerated: 30, frozen: 365, room_temperature: 7 },
    tips: ['Store in refrigerator crisper drawer', 'Keep away from other fruits to prevent over-ripening', 'Wash before eating'],
    confidence: 0.92
  },
  {
    name: 'Banana',
    category: 'Fruit',
    storage: { refrigerated: 7, frozen: 180, room_temperature: 5 },
    tips: ['Store at room temperature until ripe', 'Refrigerate only when fully ripe', 'Freeze for smoothies'],
    confidence: 0.88
  },
  {
    name: 'Orange',
    category: 'Fruit',
    storage: { refrigerated: 21, frozen: 365, room_temperature: 7 },
    tips: ['Store in refrigerator for longer freshness', 'Keep in mesh bag for air circulation'],
    confidence: 0.85
  },
  {
    name: 'Lettuce',
    category: 'Vegetable',
    storage: { refrigerated: 10, frozen: 60, room_temperature: 1 },
    tips: ['Wrap in paper towel and store in plastic bag', 'Keep in crisper drawer', 'Do not wash until ready to use'],
    confidence: 0.90
  }
];

function getRandomFoodResult(): FoodResult {
  const randomIndex = Math.floor(Math.random() * MOCK_IMAGE_RESULTS.length);
  return MOCK_IMAGE_RESULTS[randomIndex];
}

function validateImageFile(file: File): { valid: boolean; error?: string } {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    return { valid: false, error: 'File must be an image' };
  }
  
  // 检查文件大小 (5MB)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    return { valid: false, error: 'File size must be less than 5MB' };
  }
  
  // 检查支持的格式
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!supportedTypes.includes(file.type)) {
    return { valid: false, error: 'Supported formats: JPEG, PNG, WebP' };
  }
  
  return { valid: true };
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const image = formData.get('image') as File;
    const locale = formData.get('locale') as string || 'en';

    if (!image) {
      return NextResponse.json(
        { error: 'Image file is required' },
        { status: 400 }
      );
    }

    // 获取当前用户
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { 
          error: 'Authentication required',
          message: locale === 'zh' ? '请先登录' : 'Please sign in first'
        },
        { status: 401 }
      );
    }

    // 检查用户积分
    const userCredits = await getUserCredits(userUuid);
    if (!userCredits || userCredits.left_credits < 1) {
      return NextResponse.json(
        { 
          error: 'Insufficient credits',
          message: locale === 'zh' 
            ? '积分不足，请先充值' 
            : 'Insufficient credits, please recharge first',
          credits_left: userCredits?.left_credits || 0
        },
        { status: 402 }
      );
    }

    // 验证图片文件
    const validation = validateImageFile(image);
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    // 检查 API 密钥
    if (!process.env.OPENROUTER_API_KEY) {
      console.error('OpenRouter API key not configured');
      return NextResponse.json(
        {
          error: 'AI service not configured',
          message: 'Image recognition service is temporarily unavailable. Please try text input instead.'
        },
        { status: 503 }
      );
    }

    try {
      // 使用 Gemini 2.5 Flash 进行真实的图像识别，传递 locale 参数
      const result = await identifyFoodWithGemini(image, request, locale);

      // 识别成功后扣除1个积分
      try {
        await decreaseCredits({
          user_uuid: userUuid,
          trans_type: CreditsTransType.Ping,
          credits: 1,
        });
        console.log(`Deducted 1 credit for user ${userUuid}`);
      } catch (creditError) {
        console.error('Failed to deduct credits:', creditError);
        // 即使扣积分失败，也返回识别结果，避免影响用户体验
      }

      console.log('Food identification successful:', result.name);
      return NextResponse.json({
        ...result,
        credits_left: (userCredits.left_credits - 1) // 返回剩余积分
      });

    } catch (aiError) {
      console.error('AI identification failed:', aiError);

      // 使用传递的 locale 参数而不是 Accept-Language 头
      const isChineseUser = locale === 'zh';

      // 如果 AI 识别失败，返回友好的错误信息（多语言）
      const errorMessage = aiError instanceof Error ? aiError.message :
        (isChineseUser ? '无法识别图片中的食物' : 'Unable to identify the food in the image');

      const suggestions = isChineseUser ? [
        '确保食物清晰可见且光线充足',
        '尝试从不同角度拍摄照片',
        '确保食物占据图片的大部分区域',
        '如果知道食物名称，可以使用文字输入功能'
      ] : [
        'Make sure the food is clearly visible and well-lit',
        'Try taking the photo from a different angle',
        'Ensure the food takes up most of the image',
        'Use text input if you know the food name'
      ];

      return NextResponse.json(
        {
          error: 'Recognition failed',
          message: errorMessage,
          suggestions
        },
        { status: 422 }
      );
    }

  } catch (error) {
    console.error('Error in image recognition:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

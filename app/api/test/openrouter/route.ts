import { NextRequest, NextResponse } from 'next/server';
import { testOpenRouterConnection } from '@/lib/test-openrouter';

export async function GET(request: NextRequest) {
  try {
    const result = await testOpenRouterConnection();
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      ...result
    });
    
  } catch (error) {
    console.error('Test endpoint error:', error);
    return NextResponse.json(
      { 
        success: false,
        message: 'Test endpoint error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

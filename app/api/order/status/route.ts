import { NextResponse } from "next/server";
import { findOrderByOrderNo, updateOrderStatus } from "@/models/order";
import { updateCreditForOrder } from "@/services/credit";
import { updateAffiliateForOrder } from "@/services/affiliate";
import { getUserUuid } from "@/services/user";
import { respData, respErr } from "@/lib/resp";
import { getIsoTimestr } from "@/lib/time";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const orderNo = searchParams.get("order_no");
    
    if (!orderNo) {
      return respErr("Order number is required");
    }
    
    // 获取当前用户
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Unauthorized");
    }
    
    // 查询订单
    const order = await findOrderByOrderNo(orderNo);
    if (!order) {
      return respErr("Order not found");
    }
    
    // 验证订单属于当前用户
    if (order.user_uuid !== userUuid) {
      return respErr("Unauthorized");
    }
    
    // 如果订单状态还是 created，说明可能回调还没触发
    // 我们可以主动处理支付成功逻辑
    if (order.status === "created") {
      // 这里可以调用 Creem API 查询支付状态
      // 但由于我们不确定具体的 API，暂时先返回处理中的状态
      return respData({
        order,
        status: "processing",
        message: "Payment is being processed. Please wait a moment.",
      });
    }
    
    return respData({
      order,
      status: order.status,
    });
    
  } catch (error: any) {
    console.error("Error fetching order status:", error);
    return respErr(error.message || "Failed to fetch order status");
  }
}

// 手动触发支付成功处理（仅用于测试）
export async function POST(req: Request) {
  try {
    const { order_no, force_update } = await req.json();
    
    if (!order_no) {
      return respErr("Order number is required");
    }
    
    // 获取当前用户
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Unauthorized");
    }
    
    // 查询订单
    const order = await findOrderByOrderNo(order_no);
    if (!order) {
      return respErr("Order not found");
    }
    
    // 验证订单属于当前用户
    if (order.user_uuid !== userUuid) {
      return respErr("Unauthorized");
    }
    
    // 只有在 force_update 为 true 且订单状态为 created 时才处理
    if (force_update && order.status === "created") {
      // 更新订单状态为已支付
      const paid_at = getIsoTimestr();
      const paid_email = order.user_email;
      const paid_detail = JSON.stringify({
        manual_update: true,
        updated_by: userUuid,
        timestamp: paid_at,
      });
      
      await updateOrderStatus(order_no, 'paid', paid_at, paid_email, paid_detail);
      
      // 处理积分
      if (order.credits > 0) {
        await updateCreditForOrder(order);
      }
      
      // 处理推荐奖励
      await updateAffiliateForOrder(order);
      
      return respData({
        success: true,
        message: "Order updated successfully",
      });
    }
    
    return respErr("Order cannot be updated");
    
  } catch (error: any) {
    console.error("Error updating order:", error);
    return respErr(error.message || "Failed to update order");
  }
}
// 验证OAuth配置的简单脚本
console.log('🔧 OAuth配置验证');
console.log('==================');
console.log('AUTH_URL:', process.env.AUTH_URL || '❌ 未设置');
console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL || '❌ 未设置');
console.log('AUTH_GOOGLE_ID:', process.env.AUTH_GOOGLE_ID ? '✅ 已设置' : '❌ 未设置');
console.log('AUTH_GOOGLE_SECRET:', process.env.AUTH_GOOGLE_SECRET ? '✅ 已设置' : '❌ 未设置');
console.log('NEXTAUTH_SECRET:', process.env.NEXTAUTH_SECRET ? '✅ 已设置' : '❌ 未设置');

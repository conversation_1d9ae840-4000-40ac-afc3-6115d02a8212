#!/bin/bash

# 🔧 Google OAuth 生产环境快速修复脚本
# 使用方法: ./fix-google-oauth.sh your-domain.com

echo "🔧 Google OAuth 生产环境修复脚本"
echo "=================================="

# 检查参数
if [ $# -eq 0 ]; then
    echo "❌ 错误: 请提供您的域名"
    echo "使用方法: ./fix-google-oauth.sh your-domain.com"
    echo "示例: ./fix-google-oauth.sh howlongfresh.com"
    exit 1
fi

DOMAIN=$1
FULL_URL="https://$DOMAIN"
CALLBACK_URL="$FULL_URL/api/auth/callback/google"

echo "🌐 域名: $DOMAIN"
echo "🔗 完整URL: $FULL_URL"
echo "📞 回调URL: $CALLBACK_URL"
echo ""

# 1. 创建生产环境配置文件
echo "📝 创建生产环境配置文件..."
cat > .env.production << EOF
# =============================================================================
# 生产环境配置 - 自动生成于 $(date)
# =============================================================================

# 应用基础配置
AUTH_URL="$FULL_URL"
NEXTAUTH_URL="$FULL_URL"
APP_NAME="HowLongFresh"

# Google OAuth 配置
AUTH_GOOGLE_ID="653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET="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"
NEXT_PUBLIC_AUTH_GOOGLE_ID="653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED="true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED="false"

# 安全配置
NEXTAUTH_SECRET="$(openssl rand -base64 32)"

# 数据库配置 (请根据实际情况修改)
DATABASE_URL="your-production-database-url"

# API配置
OPENROUTER_API_KEY="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"
OPENROUTER_MODEL="google/gemini-flash-1.5"
EOF

echo "✅ 生产环境配置文件已创建: .env.production"
echo ""

# 2. 显示需要在Google Console中配置的信息
echo "🔧 Google Cloud Console 配置步骤:"
echo "=================================="
echo "1. 访问: https://console.cloud.google.com/"
echo "2. 选择您的项目"
echo "3. 导航到: APIs & Services > Credentials"
echo "4. 找到 OAuth 2.0 客户端 ID 并点击编辑"
echo "5. 在 'Authorized redirect URIs' 中添加:"
echo "   $CALLBACK_URL"
echo "6. 保存更改"
echo ""

# 3. 显示部署平台配置信息
echo "🚀 部署平台环境变量配置:"
echo "========================="
echo "请在您的部署平台中设置以下环境变量:"
echo ""
echo "AUTH_URL=$FULL_URL"
echo "NEXTAUTH_URL=$FULL_URL"
echo "AUTH_GOOGLE_ID=653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96.apps.googleusercontent.com"
echo "AUTH_GOOGLE_SECRET=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"
echo "NEXT_PUBLIC_AUTH_GOOGLE_ID=653368945034-fsv4i56jr4r1vh60iojij11nlfmulc96.apps.googleusercontent.com"
echo "NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true"
echo "NEXTAUTH_SECRET=$(openssl rand -base64 32)"
echo ""

# 4. 创建验证脚本
echo "📋 创建验证脚本..."
cat > verify-oauth.js << 'EOF'
// 验证OAuth配置的简单脚本
console.log('🔧 OAuth配置验证');
console.log('==================');
console.log('AUTH_URL:', process.env.AUTH_URL || '❌ 未设置');
console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL || '❌ 未设置');
console.log('AUTH_GOOGLE_ID:', process.env.AUTH_GOOGLE_ID ? '✅ 已设置' : '❌ 未设置');
console.log('AUTH_GOOGLE_SECRET:', process.env.AUTH_GOOGLE_SECRET ? '✅ 已设置' : '❌ 未设置');
console.log('NEXTAUTH_SECRET:', process.env.NEXTAUTH_SECRET ? '✅ 已设置' : '❌ 未设置');
EOF

echo "✅ 验证脚本已创建: verify-oauth.js"
echo ""

# 5. 显示后续步骤
echo "📋 后续步骤:"
echo "============"
echo "1. ✅ 配置文件已创建"
echo "2. 🔧 在Google Console中添加回调URL: $CALLBACK_URL"
echo "3. 🚀 在部署平台中设置环境变量"
echo "4. 🔄 重新部署应用"
echo "5. 🧪 测试Google登录功能"
echo ""

echo "🎯 快速测试命令:"
echo "node -e \"$(cat verify-oauth.js)\""
echo ""

echo "✨ 修复完成！请按照上述步骤操作。"
EOF

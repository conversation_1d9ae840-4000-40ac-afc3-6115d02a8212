#!/usr/bin/env python3
"""
分析 USDA FoodKeeper 数据文件
"""

import pandas as pd
import json
import sys

def analyze_usda_data():
    try:
        # 读取 Excel 文件的所有工作表
        print("正在读取 USDA FoodKeeper 数据文件...")
        df_dict = pd.read_excel('FoodKeeper-Data.xls', sheet_name=None)
        
        print(f"\n=== 文件包含 {len(df_dict)} 个工作表 ===")
        for sheet_name in df_dict.keys():
            df = df_dict[sheet_name]
            print(f"\n工作表: '{sheet_name}'")
            print(f"  - 行数: {len(df)}")
            print(f"  - 列数: {len(df.columns)}")
            print(f"  - 列名: {list(df.columns)[:5]}{'...' if len(df.columns) > 5 else ''}")
        
        # 重点分析 Product 工作表
        if 'Product' in df_dict:
            product_df = df_dict['Product']
            print(f"\n=== Product 工作表详细分析 ===")
            print(f"总记录数: {len(product_df)}")
            print(f"总字段数: {len(product_df.columns)}")
            
            print("\n所有字段列表:")
            for i, col in enumerate(product_df.columns, 1):
                print(f"  {i:2d}. {col}")
            
            # 分析关键字段
            key_fields = [
                'Name', 'Name_subtitle', 'Category_Name', 'Subcategory_Name',
                'Pantry_Min', 'Pantry_Max', 'Pantry_Metric',
                'Refrigerate_Min', 'Refrigerate_Max', 'Refrigerate_Metric',
                'Freeze_Min', 'Freeze_Max', 'Freeze_Metric',
                'Pantry_tips', 'Refrigerate_tips', 'Freeze_Tips'
            ]
            
            print(f"\n=== 关键存储字段分析 ===")
            for field in key_fields:
                if field in product_df.columns:
                    non_null_count = product_df[field].notna().sum()
                    print(f"  {field}: {non_null_count}/{len(product_df)} 条记录有数据")
            
            # 显示前几条记录示例
            print(f"\n=== 前5条记录示例 ===")
            sample_fields = ['Name', 'Name_subtitle', 'Category_Name', 'Pantry_Min', 'Refrigerate_Min', 'Freeze_Min']
            available_fields = [f for f in sample_fields if f in product_df.columns]
            print(product_df[available_fields].head().to_string(index=False))
            
            # 分析食物类别
            if 'Category_Name' in product_df.columns:
                print(f"\n=== 食物类别统计 ===")
                category_counts = product_df['Category_Name'].value_counts()
                for category, count in category_counts.items():
                    print(f"  {category}: {count} 种食物")
            
            # 分析存储时间单位
            if 'Pantry_Metric' in product_df.columns:
                print(f"\n=== 存储时间单位统计 ===")
                metric_counts = product_df['Pantry_Metric'].value_counts()
                for metric, count in metric_counts.items():
                    print(f"  {metric}: {count} 次使用")
        
        # 分析 Category 工作表
        if 'Category' in df_dict:
            category_df = df_dict['Category']
            print(f"\n=== Category 工作表分析 ===")
            print(f"类别数量: {len(category_df)}")
            if 'Category_Name' in category_df.columns:
                print("所有类别:")
                for category in category_df['Category_Name'].unique():
                    print(f"  - {category}")
        
        return df_dict
        
    except Exception as e:
        print(f"分析文件时出错: {e}")
        return None

def extract_useful_data(df_dict):
    """提取对项目有用的数据"""
    if 'Product' not in df_dict:
        print("未找到 Product 工作表")
        return None
    
    product_df = df_dict['Product']
    
    # 提取关键字段
    useful_fields = [
        'Product_ID', 'Name', 'Name_subtitle', 'Category_Name', 'Subcategory_Name',
        'Pantry_Min', 'Pantry_Max', 'Pantry_Metric', 'Pantry_tips',
        'Refrigerate_Min', 'Refrigerate_Max', 'Refrigerate_Metric', 'Refrigerate_tips',
        'Freeze_Min', 'Freeze_Max', 'Freeze_Metric', 'Freeze_Tips'
    ]
    
    # 只保留存在的字段
    available_fields = [f for f in useful_fields if f in product_df.columns]
    extracted_df = product_df[available_fields].copy()
    
    # 清理数据
    extracted_df = extracted_df.dropna(subset=['Name'])  # 移除没有名称的记录
    
    print(f"\n=== 提取的有用数据 ===")
    print(f"提取字段数: {len(available_fields)}")
    print(f"有效记录数: {len(extracted_df)}")
    
    # 保存为 JSON 格式便于分析
    sample_data = extracted_df.head(10).to_dict('records')
    
    print(f"\n=== 数据样本 (前10条) ===")
    for i, record in enumerate(sample_data, 1):
        print(f"\n{i}. {record.get('Name', 'Unknown')} {record.get('Name_subtitle', '')}")
        print(f"   类别: {record.get('Category_Name', 'Unknown')}")
        print(f"   常温: {record.get('Pantry_Min', 'N/A')}-{record.get('Pantry_Max', 'N/A')} {record.get('Pantry_Metric', '')}")
        print(f"   冷藏: {record.get('Refrigerate_Min', 'N/A')}-{record.get('Refrigerate_Max', 'N/A')} {record.get('Refrigerate_Metric', '')}")
        print(f"   冷冻: {record.get('Freeze_Min', 'N/A')}-{record.get('Freeze_Max', 'N/A')} {record.get('Freeze_Metric', '')}")
    
    return extracted_df

if __name__ == "__main__":
    print("🔍 USDA FoodKeeper 数据分析工具")
    print("=" * 50)
    
    # 分析数据结构
    df_dict = analyze_usda_data()
    
    if df_dict:
        # 提取有用数据
        useful_data = extract_useful_data(df_dict)
        
        if useful_data is not None:
            # 保存提取的数据
            useful_data.to_csv('usda_food_data_extracted.csv', index=False)
            print(f"\n✅ 数据已保存到 'usda_food_data_extracted.csv'")
            
            # 生成项目集成建议
            print(f"\n=== 项目集成建议 ===")
            print("1. 数据覆盖范围广：包含 500+ 种食物的详细存储信息")
            print("2. 权威性强：来自 USDA 官方数据")
            print("3. 数据结构完整：包含常温、冷藏、冷冻三种存储条件")
            print("4. 包含存储建议：每种食物都有详细的存储提示")
            print("5. 可直接集成：数据格式适合转换为项目所需的 JSON 格式")
    
    print("\n🎉 分析完成！")

"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useParams } from 'next/navigation';
import { FoodResult, FoodSearchState } from '@/types/food';
import { identifyFoodByText, identifyFoodByImage, FoodApiException } from '@/lib/food-api';

interface FoodContextType extends FoodSearchState {
  searchFood: (query: string | File) => Promise<void>;
  clearResult: () => void;
  clearError: () => void;
}

const FoodContext = createContext<FoodContextType | undefined>(undefined);

export function useFoodContext() {
  const context = useContext(FoodContext);
  if (context === undefined) {
    throw new Error('useFoodContext must be used within a FoodProvider');
  }
  return context;
}

interface FoodProviderProps {
  children: ReactNode;
}

export function FoodProvider({ children }: FoodProviderProps) {
  const params = useParams();
  const locale = params.locale as string || 'en';

  const [state, setState] = useState<FoodSearchState>({
    currentQuery: null,
    result: null,
    loading: false,
    error: null
  });

  const searchFood = async (query: string | File) => {
    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      currentQuery: query
    }));

    try {
      let result: FoodResult;

      if (typeof query === 'string') {
        result = await identifyFoodByText(query, locale);
      } else {
        result = await identifyFoodByImage(query, locale);
      }

      setState(prev => ({
        ...prev,
        result,
        loading: false
      }));
    } catch (error) {
      let errorMessage = 'An unexpected error occurred';

      if (error instanceof FoodApiException) {
        errorMessage = error.apiError.message || error.apiError.error;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false
      }));
    }
  };

  const clearResult = () => {
    setState({
      currentQuery: null,
      result: null,
      loading: false,
      error: null
    });
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  const value: FoodContextType = {
    ...state,
    searchFood,
    clearResult,
    clearError
  };

  return (
    <FoodContext.Provider value={value}>
      {children}
    </FoodContext.Provider>
  );
}

<!DOCTYPE html>
<html>
<head>
    <title>Create Test Watermelon Image</title>
</head>
<body>
    <canvas id="canvas" width="400" height="300"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Draw a simple watermelon
        // Green background (rind)
        ctx.fillStyle = '#2d5016';
        ctx.fillRect(0, 0, 400, 300);
        
        // Light green stripes
        ctx.fillStyle = '#4a7c59';
        for (let i = 0; i < 400; i += 40) {
            ctx.fillRect(i, 0, 20, 300);
        }
        
        // Red flesh (oval shape)
        ctx.fillStyle = '#ff4757';
        ctx.beginPath();
        ctx.ellipse(200, 150, 150, 100, 0, 0, 2 * Math.PI);
        ctx.fill();
        
        // Black seeds
        ctx.fillStyle = '#2f3542';
        const seeds = [
            [180, 130], [220, 140], [160, 160], [240, 160],
            [190, 170], [210, 180], [170, 190], [230, 190]
        ];
        
        seeds.forEach(([x, y]) => {
            ctx.beginPath();
            ctx.ellipse(x, y, 4, 6, 0, 0, 2 * Math.PI);
            ctx.fill();
        });
        
        // Convert to blob and download
        canvas.toBlob(function(blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'test-watermelon.png';
            a.click();
        }, 'image/png');
    </script>
</body>
</html>

import React from 'react';
import Link from 'next/link';
import { FoodCategory } from '@/lib/food-categories';
import { getTranslations } from 'next-intl/server';

interface CategoryBreadcrumbProps {
  category: FoodCategory;
  locale: string;
}

const CategoryBreadcrumb = async ({ category, locale }: CategoryBreadcrumbProps) => {
  const t = await getTranslations('breadcrumb');
  const isZh = locale === 'zh';
  
  return (
    <nav className="bg-muted/30 border-b" aria-label="Breadcrumb">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center space-x-2 py-3 text-sm">
          <Link 
            href={`/${locale}`} 
            className="text-gray-500 hover:text-gray-700 transition-colors duration-200"
          >
            {t('home')}
          </Link>
          
          <svg 
            className="w-4 h-4 text-gray-400" 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path 
              fillRule="evenodd" 
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" 
              clipRule="evenodd" 
            />
          </svg>
          
          <Link 
            href={`/${locale}#categories`} 
            className="text-gray-500 hover:text-gray-700 transition-colors duration-200"
          >
            {t('categories')}
          </Link>
          
          <svg 
            className="w-4 h-4 text-gray-400" 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path 
              fillRule="evenodd" 
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" 
              clipRule="evenodd" 
            />
          </svg>
          
          <span className={`font-medium ${category.color}`}>
            {isZh ? category.name.zh : category.name.en}
          </span>
        </div>
      </div>
    </nav>
  );
};

export default CategoryBreadcrumb;

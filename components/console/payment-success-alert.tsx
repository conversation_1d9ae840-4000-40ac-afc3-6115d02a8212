"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { CheckCircle, X } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface PaymentSuccessAlertProps {
  locale: string;
}

export default function PaymentSuccessAlert({ locale }: PaymentSuccessAlertProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [showAlert, setShowAlert] = useState(false);
  const [orderNo, setOrderNo] = useState<string | null>(null);

  useEffect(() => {
    const payment = searchParams.get("payment");
    const order = searchParams.get("order_no");
    
    if (payment === "success") {
      setShowAlert(true);
      setOrderNo(order);
      
      // 清除URL参数，避免刷新时重复显示
      const newUrl = window.location.pathname;
      window.history.replaceState({}, "", newUrl);
    }
  }, [searchParams]);

  if (!showAlert) return null;

  return (
    <div className="mb-6">
      <Alert className="relative bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800">
        <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
        <AlertTitle className="text-green-800 dark:text-green-200">
          {locale === "zh" ? "支付成功！" : "Payment Successful!"}
        </AlertTitle>
        <AlertDescription className="text-green-700 dark:text-green-300">
          {locale === "zh" 
            ? `您的订单已支付成功！订单号：${orderNo || "处理中..."}。您的额度已经增加，现在可以开始使用了。`
            : `Your order has been paid successfully! Order ID: ${orderNo || "Processing..."}. Your credits have been added and you can start using them now.`
          }
        </AlertDescription>
        <button
          onClick={() => setShowAlert(false)}
          className="absolute top-4 right-4 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
        >
          <X className="h-4 w-4" />
        </button>
      </Alert>
    </div>
  );
}
"use client";

import { useSession } from "next-auth/react";
import { useAppContext } from "@/contexts/app";

export default function UserInfoDisplay() {
  const { data: session, status } = useSession();
  const { user } = useAppContext();

  if (status === "loading") {
    return (
      <div className="flex items-center gap-2 px-4 py-2 bg-yellow-100 rounded">
        <div className="animate-spin w-4 h-4 border-2 border-yellow-600 border-t-transparent rounded-full"></div>
        <span className="text-yellow-800">加载中...</span>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return (
      <div className="px-4 py-2 bg-red-100 text-red-800 rounded">
        未登录
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Session 信息 */}
      {session && (
        <div className="p-4 bg-blue-50 rounded border">
          <h3 className="font-semibold text-blue-800 mb-2">Session 信息</h3>
          <div className="space-y-1 text-sm">
            <p><span className="font-medium">邮箱:</span> {session.user?.email}</p>
            <p><span className="font-medium">姓名:</span> {session.user?.name}</p>
            <p><span className="font-medium">UUID:</span> {(session.user as any)?.uuid || '未设置'}</p>
            {session.user?.image && (
              <div className="flex items-center gap-2">
                <span className="font-medium">头像:</span>
                <img 
                  src={session.user.image} 
                  alt="Avatar" 
                  className="w-8 h-8 rounded-full"
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* AppContext 用户信息 */}
      {user ? (
        <div className="p-4 bg-green-50 rounded border">
          <h3 className="font-semibold text-green-800 mb-2">AppContext 用户信息</h3>
          <div className="space-y-1 text-sm">
            <p><span className="font-medium">UUID:</span> {user.uuid}</p>
            <p><span className="font-medium">邮箱:</span> {user.email}</p>
            <p><span className="font-medium">昵称:</span> {user.nickname}</p>
            <p><span className="font-medium">创建时间:</span> {user.created_at}</p>
            {user.avatar_url && (
              <div className="flex items-center gap-2">
                <span className="font-medium">头像:</span>
                <img 
                  src={user.avatar_url} 
                  alt="Avatar" 
                  className="w-8 h-8 rounded-full"
                />
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="p-4 bg-orange-50 rounded border">
          <h3 className="font-semibold text-orange-800 mb-2">AppContext 状态</h3>
          <p className="text-sm text-orange-700">用户信息未加载</p>
        </div>
      )}

      {/* 状态总结 */}
      <div className="p-4 bg-gray-50 rounded border">
        <h3 className="font-semibold text-gray-800 mb-2">状态总结</h3>
        <div className="space-y-1 text-sm">
          <p><span className="font-medium">NextAuth状态:</span> {status}</p>
          <p><span className="font-medium">Session存在:</span> {session ? '是' : '否'}</p>
          <p><span className="font-medium">AppContext用户:</span> {user ? '已加载' : '未加载'}</p>
          <p><span className="font-medium">用户邮箱匹配:</span> {
            session?.user?.email === user?.email ? '匹配' : '不匹配'
          }</p>
        </div>
      </div>
    </div>
  );
}

'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { FoodResult } from '@/types/food';
import { getFoodImageUrl, handleImageError } from '@/lib/food-images';
import { formatStorageTime, formatStorageTimeChinese } from '@/lib/food-api';

interface FoodCardProps {
  food: FoodResult;
  locale: string;
}

export default function FoodCard({ food, locale }: FoodCardProps) {
  const t = useTranslations('food');
  
  // 使用新的图片服务获取无版权风险的图片
  const imageUrl = food.image_url || getFoodImageUrl(food.name, food.category);
  
  // 根据语言选择格式化函数
  const formatTime = locale === 'zh' ? formatStorageTimeChinese : formatStorageTime;
  
  // 检查是否有任何存储数据
  const hasAnyStorage = 
    (food.storage?.refrigerated && Number(food.storage.refrigerated) > 0) ||
    (food.storage?.frozen && Number(food.storage.frozen) > 0) ||
    (food.storage?.room_temperature && Number(food.storage.room_temperature) > 0);

  return (
    <div className="bg-card rounded-xl shadow-sm border overflow-hidden hover:shadow-lg transition-all duration-300 hover:scale-105 group">
      {/* 临时的占位区域，显示USDA认证标识 */}
      <div className="relative h-16 bg-gradient-to-r from-muted to-muted/80 flex items-center justify-center">
        {/* USDA认证标识 */}
        {food.is_usda_verified && (
          <div className="bg-green-600 text-white text-xs font-medium px-2 py-1 rounded-full flex items-center space-x-1">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>USDA</span>
          </div>
        )}
      </div>

      {/* 食物信息 */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-1">
          {food.name}
        </h3>
        {/* 保存天数信息 */}
        <div className="space-y-2 mb-4">
          {!hasAnyStorage ? (
            <div className="text-sm text-gray-500 text-center py-2">
              {t('no_storage_data')}
            </div>
          ) : (
            <>
              {(food.storage?.refrigerated && Number(food.storage.refrigerated) > 0) ? (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 flex items-center">
                    <svg className="w-4 h-4 mr-1 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm6 0a2 2 0 104 0 2 2 0 00-4 0z" clipRule="evenodd" />
                    </svg>
                    {t('refrigerated')}
                  </span>
                  <span className="font-medium text-blue-600">
                    {formatTime(food.storage.refrigerated)}
                  </span>
                </div>
              ) : null}
          
              {(food.storage?.frozen && Number(food.storage.frozen) > 0) ? (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 flex items-center">
                    <svg className="w-4 h-4 mr-1 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM8 8a1 1 0 012 0v2h2a1 1 0 110 2h-2v2a1 1 0 11-2 0v-2H6a1 1 0 110-2h2V8z" clipRule="evenodd" />
                    </svg>
                    {t('frozen')}
                  </span>
                  <span className="font-medium text-purple-600">
                    {formatTime(food.storage.frozen)}
                  </span>
                </div>
              ) : null}
          
              {(food.storage?.room_temperature && Number(food.storage.room_temperature) > 0) ? (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 flex items-center">
                    <svg className="w-4 h-4 mr-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1.323l3.954 1.582a1 1 0 01.641.936V8a1 1 0 01-.504.868l-2.411 1.205a1 1 0 00-.436.436l-1.205 2.411A1 1 0 0110 13a1 1 0 01-.868-.504l-1.205-2.411a1 1 0 00-.436-.436L5.08 8.868A1 1 0 014.576 8V6.841a1 1 0 01.641-.936L9.171 4.323V3a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    {t('room_temperature')}
                  </span>
                  <span className="font-medium text-orange-600">
                    {formatTime(food.storage.room_temperature)}
                  </span>
                </div>
              ) : null}
            </>
          )}
        </div>

        {/* 查看详情按钮 */}
        <Link
          href={`/${locale}/food/${encodeURIComponent(food.name)}`}
          className="block w-full text-center bg-green-50 hover:bg-green-100 text-green-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
        >
          {t('view_details')}
        </Link>
      </div>
    </div>
  );
}
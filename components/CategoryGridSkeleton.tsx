import { Skeleton } from "@/components/ui/skeleton";

export default function CategoryGridSkeleton() {
  return (
    <section className="py-16 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-background">
      <div className="container">
        <div className="text-center mb-12">
          <Skeleton className="h-10 w-64 mx-auto mb-4" />
          <Skeleton className="h-6 w-96 mx-auto" />
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          {[...Array(9)].map((_, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-xl border bg-card p-6 transition-all hover:shadow-lg"
            >
              <div className="flex flex-col items-center text-center space-y-3">
                <Skeleton className="w-16 h-16 rounded-full" />
                <div className="space-y-2 w-full">
                  <Skeleton className="h-6 w-24 mx-auto" />
                  <Skeleton className="h-4 w-16 mx-auto" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
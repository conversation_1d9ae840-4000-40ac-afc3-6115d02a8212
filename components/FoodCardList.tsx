import React from 'react';
import { FoodResult } from '@/types/food';
import FoodCard from './FoodCard';
import { getTranslations } from 'next-intl/server';


interface FoodCardListProps {
  foods: FoodResult[];
  locale?: string;
  className?: string;
}

export default async function FoodCardList({ foods, locale = 'zh', className = "" }: FoodCardListProps) {
  const t = await getTranslations('food');
  if (foods.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🔍</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {t('no_data')}
        </h3>
        <p className="text-gray-500">
          {t('no_data_description')}
        </p>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}>
      {foods.map((food, index) => (
        <FoodCard key={`${food.name}-${index}`} food={food} locale={locale} />
      ))}
    </div>
  );
}

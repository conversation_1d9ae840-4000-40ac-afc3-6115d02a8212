'use client';

import React from 'react';
import { useTranslations } from 'next-intl';

interface CategorySortSelectorProps {
  sortBy: string;
  page: number;
  pageSize: number;
  total: number;
}

const CategorySortSelector: React.FC<CategorySortSelectorProps> = ({
  sortBy,
  page,
  pageSize,
  total
}) => {
  const t = useTranslations('categories');

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const url = new URL(window.location.href);
    url.searchParams.set('sort', e.target.value);
    url.searchParams.delete('page'); // 重置到第一页
    window.location.href = url.toString();
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
      <div className="flex items-center space-x-4">
        <span className="text-sm font-medium text-gray-700">{t('sort_by')}</span>
        <select
          className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
          defaultValue={sortBy}
          onChange={handleSortChange}
        >
          <option value="name">{t('sort_name')}</option>
          <option value="refrigerated_days">{t('sort_refrigerated')}</option>
          <option value="frozen_days">{t('sort_frozen')}</option>
          <option value="popularity">{t('sort_popularity')}</option>
        </select>
      </div>

      <div className="text-sm text-gray-500">
        {t('showing_items', {
          start: (page - 1) * pageSize + 1,
          end: Math.min(page * pageSize, total),
          total: total
        })}
      </div>
    </div>
  );
};

export default CategorySortSelector;

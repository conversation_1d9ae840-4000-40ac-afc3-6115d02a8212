'use client';

import { useEffect, useState } from 'react';
import CategoryGrid from './CategoryGrid';
import CategoryGridSkeleton from './CategoryGridSkeleton';

interface CategoryGridClientProps {
  locale: string;
}

export default function CategoryGridClient({ locale }: CategoryGridClientProps) {
  const [categoryCounts, setCategoryCounts] = useState<Record<string, number> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 获取分类统计数据
    async function fetchCategoryCounts() {
      try {
        const response = await fetch('/api/categories/counts');
        if (response.ok) {
          const data = await response.json();
          setCategoryCounts(data);
        }
      } catch (error) {
        console.error('Failed to fetch category counts:', error);
        // 使用默认值作为降级方案
        setCategoryCounts({});
      } finally {
        setIsLoading(false);
      }
    }

    fetchCategoryCounts();
  }, []);

  if (isLoading) {
    return <CategoryGridSkeleton />;
  }

  return <CategoryGrid categoryCounts={categoryCounts || {}} locale={locale} />;
}
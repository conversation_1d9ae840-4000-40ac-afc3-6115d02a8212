"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  Card<PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { SiGith<PERSON>, SiGoogle } from "react-icons/si";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function SignForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const t = useTranslations();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const result = await signIn("credentials", {
        email: formData.email,
        password: formData.password,
        redirect: false,
      });

      if (result?.error) {
        setError(t('auth.signin.errors.invalid_credentials'));
      } else if (result?.ok) {
        router.push("/");
        router.refresh();
      }
    } catch (error) {
      setError(t('auth.signin.errors.general_error'));
    } finally {
      setIsLoading(false);
    }
  };

  const showOAuthButtons = process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" || 
                          process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true";

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">
            {t("sign_modal.sign_in_title")}
          </CardTitle>
          <CardDescription>
            {t("sign_modal.sign_in_description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            {showOAuthButtons && (
              <div className="flex flex-col gap-4">
                {process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" && (
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => signIn("google")}
                    disabled={isLoading}
                  >
                    <SiGoogle className="w-4 h-4" />
                    {t("sign_modal.google_sign_in")}
                  </Button>
                )}
                {process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true" && (
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => signIn("github")}
                    disabled={isLoading}
                  >
                    <SiGithub className="w-4 h-4" />
                    {t("sign_modal.github_sign_in")}
                  </Button>
                )}
              </div>
            )}

            {showOAuthButtons && (
              <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
                <span className="relative z-10 bg-background px-2 text-muted-foreground">
                  {t('auth.signin.continue_with')}
                </span>
              </div>
            )}
            
            <form onSubmit={handleEmailSignIn} className="grid gap-6">
              {error && (
                <div className="text-sm text-red-500 text-center">
                  {error}
                </div>
              )}
              <div className="grid gap-2">
                <Label htmlFor="email">{t('auth.signin.email_label')}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  disabled={isLoading}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="password">{t('auth.signin.password_label')}</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="ml-auto text-sm underline-offset-4 hover:underline"
                  >
                    {t('auth.signin.forgot_password')}
                  </Link>
                </div>
                <Input 
                  id="password" 
                  type="password" 
                  required 
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  disabled={isLoading}
                />
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? t('auth.signin.signing_in') : t('auth.signin.signin_button')}
              </Button>
            </form>
            <div className="text-center text-sm">
              {t('auth.signin.no_account')}{" "}
              <Link href="/auth/signup" className="underline underline-offset-4">
                {t('auth.signin.signup_link')}
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 [&_a]:hover:text-primary">
        {t('auth.signin.terms_notice')}{" "}
        <a href="/terms-of-service" target="_blank">
          {t('auth.signin.terms_link')}
        </a>{" "}
        {t('auth.signin.and')}{" "}
        <a href="/privacy-policy" target="_blank">
          {t('auth.signin.privacy_link')}
        </a>
        .
      </div>
    </div>
  );
}
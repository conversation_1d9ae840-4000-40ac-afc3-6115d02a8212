"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, Home, User, Loader2 } from "lucide-react";
import Link from "next/link";

interface PaymentSuccessContentProps {
  orderNo?: string;
  checkoutId?: string;
  locale: string;
}

export default function PaymentSuccessContent({ 
  orderNo, 
  checkoutId, 
  locale 
}: PaymentSuccessContentProps) {
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState<any>(null);
  const [status, setStatus] = useState<string>("processing");
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    if (orderNo) {
      checkOrderStatus();
      // 每3秒检查一次订单状态，最多检查10次
      let attempts = 0;
      const interval = setInterval(async () => {
        attempts++;
        if (attempts > 10) {
          clearInterval(interval);
          return;
        }
        
        const result = await checkOrderStatus();
        if (result?.order?.status === "paid") {
          clearInterval(interval);
        }
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [orderNo]);

  const checkOrderStatus = async () => {
    try {
      const response = await fetch(`/api/order/status?order_no=${orderNo}`);
      const data = await response.json();
      
      if (data.code === 0) {
        setOrder(data.data.order);
        setStatus(data.data.status);
        setLoading(false);
        return data.data;
      } else {
        setError(data.message);
        setLoading(false);
      }
    } catch (err) {
      setError("Failed to check order status");
      setLoading(false);
    }
  };

  // 根据订单信息计算实际的额度
  const getCreditsInfo = () => {
    if (!order) return { credits: 0, period: "" };
    
    const credits = order.credits || 0;
    const interval = order.interval;
    
    if (interval === "month") {
      return { credits: 300, period: locale === "zh" ? "每月" : "per month" };
    } else if (interval === "year") {
      return { credits: 3000, period: locale === "zh" ? "每年" : "per year" };
    }
    
    return { credits, period: "" };
  };

  const { credits, period } = getCreditsInfo();

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <Card>
            <CardContent className="py-16">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
              <p className="text-lg">
                {locale === "zh" ? "正在处理您的支付..." : "Processing your payment..."}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto">
          <Card className="text-center">
            <CardContent className="py-16">
              <p className="text-red-600 mb-4">{error}</p>
              <Button asChild>
                <Link href={`/${locale}`}>
                  {locale === "zh" ? "返回首页" : "Back to Home"}
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const isPaid = order?.status === "paid";
  const isProcessing = status === "processing";

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto">
        <Card className="text-center">
          <CardHeader>
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-green-600">
              {isProcessing 
                ? (locale === 'zh' ? '支付处理中...' : 'Payment Processing...')
                : (locale === 'zh' ? '支付成功！' : 'Payment Successful!')
              }
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-muted-foreground">
              {isProcessing ? (
                <p className="text-lg mb-4">
                  {locale === 'zh' 
                    ? '您的支付正在处理中，请稍候...' 
                    : 'Your payment is being processed, please wait...'}
                </p>
              ) : (
                <>
                  <p className="text-lg mb-4">
                    {locale === 'zh' 
                      ? '感谢您选择 HowLongFresh 专业版！您的订阅已激活。' 
                      : 'Thank you for choosing HowLongFresh Pro! Your subscription is now active.'}
                  </p>
                  
                  {order && (
                    <div className="bg-muted p-4 rounded-lg mb-4">
                      <p className="text-sm">
                        <strong>
                          {locale === 'zh' ? '订单号：' : 'Order ID: '}
                        </strong>
                        {order.order_no}
                      </p>
                      {order.product_name && (
                        <p className="text-sm mt-1">
                          <strong>
                            {locale === 'zh' ? '产品：' : 'Product: '}
                          </strong>
                          {order.product_name}
                        </p>
                      )}
                      {credits > 0 && (
                        <p className="text-sm mt-1">
                          <strong>
                            {locale === 'zh' ? '额度：' : 'Credits: '}
                          </strong>
                          {credits} {locale === 'zh' ? '次' : 'queries'} {period}
                        </p>
                      )}
                    </div>
                  )}

                  <div className="text-left space-y-2 mb-6">
                    <h3 className="font-semibold text-foreground">
                      {locale === 'zh' ? '您现在可以享受：' : 'You now have access to:'}
                    </h3>
                    <ul className="space-y-1 text-sm">
                      <li>• {locale === 'zh' ? `${period}${credits} 次查询` : `${credits} queries ${period}`}</li>
                      <li>• {locale === 'zh' ? '完整食物数据库' : 'Complete food database'}</li>
                      <li>• {locale === 'zh' ? '智能保质期提醒' : 'Smart expiration reminders'}</li>
                      <li>• {locale === 'zh' ? '批量查询功能' : 'Bulk query functionality'}</li>
                      <li>• {locale === 'zh' ? 'API 访问权限' : 'API access'}</li>
                      <li>• {locale === 'zh' ? '优先客服支持' : 'Priority customer support'}</li>
                    </ul>
                  </div>
                </>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild>
                <Link href={`/${locale}`}>
                  <Home className="w-4 h-4 mr-2" />
                  {locale === 'zh' ? '返回首页' : 'Back to Home'}
                </Link>
              </Button>
              {isPaid && (
                <Button variant="outline" asChild>
                  <Link href={`/${locale}/my-credits`}>
                    <User className="w-4 h-4 mr-2" />
                    {locale === 'zh' ? '查看我的额度' : 'View My Credits'}
                  </Link>
                </Button>
              )}
            </div>

            <div className="text-xs text-muted-foreground pt-4 border-t">
              <p>
                {locale === 'zh' 
                  ? '如有任何问题，请联系我们的客服团队。' 
                  : 'If you have any questions, please contact our support team.'}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
import React from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { FOOD_CATEGORIES, FoodCategory } from '@/lib/food-categories';

interface CategoryCardProps {
  category: FoodCategory;
  count?: number;
  locale?: string;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ category, count, locale = 'zh' }) => {
  const t = useTranslations('categories');

  const categoryUrl = locale === 'en' ? `/category/${category.slug}` : `/${locale}/category/${category.slug}`;

  return (
    <Link
      href={categoryUrl}
      className={`
        group relative overflow-hidden rounded-xl border border-gray-200 dark:border-gray-700
        ${category.bgColor} transition-all duration-300 ease-in-out
        hover:shadow-lg hover:scale-105 hover:border-gray-300 dark:hover:border-gray-600
        focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
        min-h-[120px] sm:min-h-[140px] flex items-center justify-center
      `}
    >
      <div className="p-4 sm:p-6 text-center w-full">
        {/* 图标 */}
        <div className="mb-4 flex justify-center">
          <span className="text-4xl md:text-5xl transition-transform duration-300 group-hover:scale-110">
            {category.emoji}
          </span>
        </div>
        
        {/* 分类名称 */}
        <h3 className={`text-lg font-semibold mb-2 ${category.color} transition-colors duration-300`}>
          {locale === 'zh' ? category.name.zh : category.name.en}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
          {locale === 'zh' ? category.description.zh : category.description.en}
        </p>

        {/* 食物数量 */}
        {count !== undefined && (
          <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-700/20 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300">
            {count} {t('count_text')}
          </div>
        )}
        
        {/* 悬停效果箭头 */}
        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <svg 
            className="w-5 h-5 text-gray-500" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9 5l7 7-7 7" 
            />
          </svg>
        </div>
      </div>
    </Link>
  );
};

interface CategoryGridProps {
  categoryCounts?: Record<string, number>;
  className?: string;
  locale?: string;
}

const CategoryGrid: React.FC<CategoryGridProps> = ({
  categoryCounts = {},
  className = "",
  locale = 'zh'
}) => {
  const t = useTranslations('categories');
  return (
    <section className={`py-8 md:py-16 bg-muted/30 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题部分 */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            {t('title')}
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            {t('description')}
          </p>
        </div>

        {/* 10宫格布局 - 优化的响应式设计 */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-5">
          {FOOD_CATEGORIES.map((category) => (
            <CategoryCard
              key={category.id}
              category={category}
              count={categoryCounts[category.slug]}
              locale={locale}
            />
          ))}
        </div>
        
        {/* 底部提示 */}
        <div className="text-center mt-12">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t('not_found')}
            <Link
              href="/"
              className="text-green-600 hover:text-green-700 font-medium ml-1"
            >
              {t('try_search')}
            </Link>
          </p>
        </div>
      </div>
    </section>
  );
};

export default CategoryGrid;

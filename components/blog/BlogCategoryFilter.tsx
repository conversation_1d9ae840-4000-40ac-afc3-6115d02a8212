"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface BlogCategory {
  name: string;
  name_zh: string;
  icon: string;
  count: number;
}

interface BlogCategoryFilterProps {
  categories: BlogCategory[];
  selectedCategory: string | null;
  onCategoryChange: (category: string | null) => void;
  locale?: string;
}

const categoryColors: Record<string, string> = {
  'food_safety': 'bg-red-100 text-red-800 hover:bg-red-200',
  'frozen_foods': 'bg-blue-100 text-blue-800 hover:bg-blue-200',
  'refrigerated_foods': 'bg-cyan-100 text-cyan-800 hover:bg-cyan-200',
  'expiration_dates': 'bg-orange-100 text-orange-800 hover:bg-orange-200',
  'preparation': 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
  'storage_tips': 'bg-purple-100 text-purple-800 hover:bg-purple-200',
};

export default function BlogCategoryFilter({
  categories,
  selectedCategory,
  onCategoryChange,
  locale = 'en'
}: BlogCategoryFilterProps) {
  const isZh = locale.startsWith('zh');
  
  // 计算总数
  const totalCount = categories.reduce((sum, cat) => sum + cat.count, 0);

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      {/* 全部按钮 */}
      <Button
        variant={selectedCategory === null ? "default" : "outline"}
        size="sm"
        onClick={() => onCategoryChange(null)}
        className="flex items-center gap-1"
      >
        <span>All</span>
        <Badge variant="secondary" className="ml-1">
          {totalCount}
        </Badge>
      </Button>

      {/* 分类按钮 */}
      {categories.map((category) => (
        <Button
          key={category.name}
          variant={selectedCategory === category.name ? "default" : "outline"}
          size="sm"
          onClick={() => onCategoryChange(category.name)}
          className={cn(
            "flex items-center gap-1",
            selectedCategory !== category.name && categoryColors[category.name]
          )}
        >
          <span className="text-lg">{category.icon}</span>
          <span>{isZh ? category.name_zh : category.name}</span>
          <Badge 
            variant="secondary" 
            className={cn(
              "ml-1",
              selectedCategory === category.name ? "" : "bg-white/80"
            )}
          >
            {category.count}
          </Badge>
        </Button>
      ))}
    </div>
  );
}
.markdown {
  /* 重要：覆盖 MDEditor 的默认样式 */
  background-color: transparent !important;
  
  /* 确保Overview等标题不会有黑色背景 */
  h2 {
    background-color: transparent !important;
    color: inherit !important;
    padding: 0;
    margin: 1.5rem 0 1rem 0;
    border-bottom: 1px solid var(--border);
  }

  /* 确保所有标题样式正确 */
  h1, h2, h3, h4, h5, h6 {
    background-color: transparent !important;
    color: inherit !important;
  }

  li {
    list-style-type: square;
    word-break: break-word;
  }

  /* 改善移动端显示 */
  @media (max-width: 768px) {
    h1, h2, h3, h4, h5, h6 {
      line-height: 1.3;
      margin-bottom: 1rem;
    }

    p {
      margin-bottom: 1rem;
      line-height: 1.6;
    }

    ul, ol {
      padding-left: 1.5rem;
    }

    li {
      margin-bottom: 0.5rem;
    }
  }

  /* 确保代码块在移动端不溢出 */
  pre {
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  code {
    word-wrap: break-word;
  }

  /* 表格响应式 */
  table {
    width: 100%;
    overflow-x: auto;
    display: block;
    white-space: nowrap;
  }

  @media (max-width: 768px) {
    table {
      font-size: 0.875rem;
    }
  }
}

/* 覆盖 wmde-markdown 的默认样式 */
.wmde-markdown {
  background-color: transparent !important;
  color: inherit !important;
}

.wmde-markdown h2 {
  background-color: transparent !important;
  color: inherit !important;
}

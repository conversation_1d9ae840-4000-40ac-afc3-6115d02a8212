"use client";

import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Clock, Calendar, Tag, AlertCircle, Lightbulb } from "lucide-react";
import Link from "next/link";
import Crumb from "./crumb";
import Markdown from "@/components/markdown";
import { Post } from "@/types/post";
import moment from "moment";
import { cn } from "@/lib/utils";

const categoryColors: Record<string, string> = {
  'food_safety': 'bg-red-100 text-red-800 border-red-200',
  'frozen_foods': 'bg-blue-100 text-blue-800 border-blue-200',
  'refrigerated_foods': 'bg-cyan-100 text-cyan-800 border-cyan-200',
  'expiration_dates': 'bg-orange-100 text-orange-800 border-orange-200',
  'preparation': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'storage_tips': 'bg-purple-100 text-purple-800 border-purple-200',
};

const categoryInfo: Record<string, { name: string; name_zh: string; icon: string }> = {
  'food_safety': { name: 'Food Safety', name_zh: '食品安全', icon: '🛡️' },
  'frozen_foods': { name: 'Frozen Foods', name_zh: '冷冻食品', icon: '❄️' },
  'refrigerated_foods': { name: 'Refrigerated Foods', name_zh: '冷藏食品', icon: '🧊' },
  'expiration_dates': { name: 'Expiration Dates', name_zh: '有效期', icon: '📅' },
  'preparation': { name: 'Preparation', name_zh: '食品准备', icon: '🍳' },
  'storage_tips': { name: 'Storage Tips', name_zh: '储存技巧', icon: '💡' },
};

export default function BlogDetail({ post }: { post: Post }) {
  const isZh = post.locale?.startsWith('zh');
  const categoryData = post.category ? categoryInfo[post.category] : null;
  
  // 从内容中提取关键要点和相关食品
  const contentSections = post.content?.split('##').filter(Boolean) || [];
  const keyPointsSection = contentSections.find(s => s.trim().startsWith('Key Points'));
  const relatedFoodsSection = contentSections.find(s => s.trim().startsWith('Related Foods'));
  
  const keyPoints = keyPointsSection?.split('\n')
    .filter(line => line.trim().startsWith('-'))
    .map(line => line.replace(/^-\s*/, '').trim()) || [];
    
  const relatedFoodsText = relatedFoodsSection?.split('\n')
    .find(line => line.includes('This information applies to:'))
    ?.replace('This information applies to:', '')
    .trim() || '';
  const relatedFoods = relatedFoodsText.split(',').map(f => f.trim()).filter(Boolean);

  return (
    <section className="py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 面包屑导航 */}
        <div className="mb-6">
          <Crumb post={post} />
        </div>

        {/* 主要内容区域 - 使用网格布局 */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* 主内容区域 */}
          <div className="lg:col-span-8">
            {/* 返回FAQ列表按钮 */}
            <Link
              href={`/${post.locale}/posts${post.category ? `?category=${post.category}` : ''}`}
              className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-primary mb-6"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to FAQ List
            </Link>

            {/* 分类标签 */}
            {categoryData && (
              <Badge
                variant="outline"
                className={cn("mb-4", categoryColors[post.category!])}
              >
                <span className="mr-1">{categoryData.icon}</span>
                {isZh ? categoryData.name_zh : categoryData.name}
              </Badge>
            )}

            {/* 标题 */}
            <h1 className="mb-6 text-2xl font-bold md:text-3xl lg:text-4xl leading-tight">
              {post.title}
            </h1>

            {/* 元信息 */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-8">
              {post.author_avatar_url && (
                <Avatar className="h-8 w-8 border">
                  <AvatarImage
                    src={post.author_avatar_url}
                    alt={post.author_name}
                  />
                </Avatar>
              )}
              <div className="flex flex-wrap items-center gap-4">
                {post.author_name && (
                  <span className="font-medium">{post.author_name}</span>
                )}
                <span className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  {post.created_at && moment(post.created_at).format('MMM DD, YYYY')}
                </span>
              </div>
            </div>

            {/* 关键要点卡片 */}
            {keyPoints.length > 0 && (
              <Card className="mb-8 border-primary/20 bg-primary/5">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Lightbulb className="w-5 h-5 text-primary" />
                    <h3 className="font-semibold">Key Points</h3>
                  </div>
                  <ul className="space-y-2">
                    {keyPoints.map((point, idx) => (
                      <li key={idx} className="flex items-start gap-2">
                        <AlertCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{point}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* 主要内容 */}
            <div className="prose prose-lg dark:prose-invert max-w-none mb-8 prose-headings:text-foreground prose-p:text-foreground prose-li:text-foreground prose-strong:text-foreground prose-h2:bg-transparent prose-h2:text-foreground">
              {post.content && <Markdown content={post.content} />}
            </div>

            {/* 相关食品标签 */}
            {relatedFoods.length > 0 && (
              <div className="border-t pt-8">
                <div className="flex items-center gap-2 mb-4">
                  <Tag className="w-5 h-5 text-muted-foreground" />
                  <h3 className="font-semibold">Related Foods</h3>
                </div>
                <div className="flex flex-wrap gap-2">
                  {relatedFoods.map((food, idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {food}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* 返回按钮 */}
            <div className="mt-12 pt-8 border-t">
              <Link
                href={`/${post.locale}/posts${post.category ? `?category=${post.category}` : ''}`}
                className="inline-flex items-center gap-2 px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                View More FAQ
              </Link>
            </div>
          </div>

          {/* 侧边栏区域 - 仅在大屏幕显示 */}
          <div className="lg:col-span-4 hidden lg:block">
            <div className="sticky top-8 space-y-6">
              {/* 文章信息卡片 */}
              <Card>
                <CardContent className="pt-6">
                  <h3 className="font-semibold mb-4">Article Info</h3>
                  <div className="space-y-3 text-sm">
                    {categoryData && (
                      <div className="flex items-center gap-2">
                        <span className="text-muted-foreground">Category:</span>
                        <Badge variant="outline" className={cn("text-xs", categoryColors[post.category!])}>
                          <span className="mr-1">{categoryData.icon}</span>
                          {isZh ? categoryData.name_zh : categoryData.name}
                        </Badge>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">Published:</span>
                      <span>{post.created_at && moment(post.created_at).format('MMM DD, YYYY')}</span>
                    </div>
                    {post.author_name && (
                      <div className="flex items-center gap-2">
                        <span className="text-muted-foreground">Author:</span>
                        <span>{post.author_name}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* 快速导航 */}
              {keyPoints.length > 0 && (
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="font-semibold mb-4">Quick Navigation</h3>
                    <ul className="space-y-2 text-sm">
                      <li>
                        <a href="#overview" className="text-muted-foreground hover:text-primary transition-colors">
                          Overview
                        </a>
                      </li>
                      <li>
                        <a href="#key-points" className="text-muted-foreground hover:text-primary transition-colors">
                          Key Points
                        </a>
                      </li>
                      {relatedFoods.length > 0 && (
                        <li>
                          <a href="#related-foods" className="text-muted-foreground hover:text-primary transition-colors">
                            Related Foods
                          </a>
                        </li>
                      )}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
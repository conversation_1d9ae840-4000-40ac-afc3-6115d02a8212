"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import LoadingSpinner from "@/components/ui/loading-spinner";
import MobileOptimizedInput from "@/components/ui/mobile-optimized-input";
import DragDropUpload from "@/components/ui/drag-drop-upload";
import FoodScanAnimation from "./food-scan-animation";
import { SearchSection, UploadSection } from "@/types/blocks/food-hero";
import { validateImageFile } from "@/lib/food-api";
import { useFoodContext } from "@/contexts/food-context";

interface FoodInputSectionProps {
  searchSection?: SearchSection;
  uploadSection?: UploadSection;
}

export default function FoodInputSection({
  searchSection,
  uploadSection
}: FoodInputSectionProps) {
  const [searchValue, setSearchValue] = useState("");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState<string | null>(null);
  const [searchType, setSearchType] = useState<'text' | 'image'>('text');

  const { searchFood, loading, error, clearError } = useFoodContext();

  const handleSubmit = async () => {
    if (searchValue.trim()) {
      setSearchType('text');
      await searchFood(searchValue.trim());
    } else if (uploadedFile) {
      setSearchType('image');
      await searchFood(uploadedFile);
    }
  };

  const handleFileSelect = (file: File) => {
    setFileError(null);
    setUploadedFile(file);
    setSearchValue(""); // 清空文本输入
    clearError(); // 清空之前的错误
  };

  const handleFileError = (error: string) => {
    setFileError(error);
    setUploadedFile(null);
  };

  const handleExampleClick = (example: string) => {
    setSearchValue(example);
    setUploadedFile(null); // 清空文件上传
    setFileError(null);
    clearError(); // 清空之前的错误
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
    if (e.target.value.trim()) {
      setUploadedFile(null); // 清空文件上传
      setFileError(null);
      clearError(); // 清空之前的错误
    }
  };

  return (
    <>
      {/* 扫描动画 */}
      <FoodScanAnimation isScanning={loading} foodType={searchType} />

      <div className="mt-8 md:mt-12 space-y-6">
        {/* 错误提示 */}
        {(error || fileError) && (
          <div className="max-w-4xl mx-auto p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg animate-fadeInUp">
            <div className="flex items-center gap-2 text-red-700 dark:text-red-400">
              <Icon name="RiErrorWarningLine" className="w-4 h-4" />
              <span className="text-sm">{error || fileError}</span>
            </div>
          </div>
        )}

      <div className="flex flex-col gap-6 md:gap-8 max-w-4xl mx-auto p-6 md:p-10 bg-background/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200 dark:border-gray-700">
        {/* 文本输入 */}
        <div className="w-full animate-fadeInUp">
          <MobileOptimizedInput
            placeholder={searchSection?.placeholder || "Enter food name"}
            value={searchValue}
            onChange={(value) => {
              setSearchValue(value);
              if (value.trim()) {
                setUploadedFile(null);
                setFileError(null);
                clearError();
              }
            }}
            onSubmit={handleSubmit}
            disabled={loading}
            examples={searchSection?.examples?.slice(0, 3) || []}
          />
        </div>

        {/* 分隔符 */}
        <div className="flex items-center justify-center animate-fadeInUp" style={{ animationDelay: '0.1s' }}>
          <span className="text-muted-foreground font-medium text-base">
            {searchSection?.separator_text || "OR"}
          </span>
        </div>

        {/* 文件上传 */}
        <div className="w-full animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
          <DragDropUpload
            onFileSelect={handleFileSelect}
            onError={handleFileError}
            accept={uploadSection?.accept || "image/*"}
            maxSize={uploadSection?.max_size || "5MB"}
            disabled={loading}
            currentFile={uploadedFile}
          />
        </div>

        {/* 提交按钮 */}
        <div className="w-full flex justify-center animate-fadeInUp" style={{ animationDelay: '0.3s' }}>
          <Button
            size="lg"
            onClick={handleSubmit}
            disabled={loading || (!searchValue.trim() && !uploadedFile)}
            className="h-14 px-12 text-lg w-full sm:w-auto"
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                {typeof uploadedFile === 'object' && uploadedFile ? 'Analyzing...' : 'Searching...'}
              </>
            ) : (
              <>
                Check Freshness
                <Icon name="RiSearchLine" className="ml-2 w-5 h-5" />
              </>
            )}
          </Button>
        </div>
      </div>
      </div>
    </>
  );
}

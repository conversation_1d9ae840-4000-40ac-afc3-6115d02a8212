"use client";

import { useEffect, useState } from "react";
import Icon from "@/components/icon";

interface FoodScanAnimationProps {
  isScanning: boolean;
  foodType?: 'text' | 'image';
}

export default function FoodScanAnimation({ isScanning, foodType = 'text' }: FoodScanAnimationProps) {
  const [scanProgress, setScanProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);

  const steps = foodType === 'image'
    ? ['Uploading image...', 'AI analyzing image...', 'Identifying food item...', 'Calculating storage times...', 'Almost done...']
    : ['Searching database...', 'Finding match...', 'Calculating freshness...', 'Almost done...'];

  useEffect(() => {
    if (!isScanning) {
      setScanProgress(0);
      setCurrentStep(0);
      return;
    }

    const interval = setInterval(() => {
      setScanProgress(prev => {
        const newProgress = prev + 2;
        
        // 更新步骤 - 图像识别需要更多时间
        const stepThreshold = foodType === 'image' ? 20 : 25;
        const stepIndex = Math.floor(newProgress / stepThreshold);
        if (stepIndex !== currentStep && stepIndex < steps.length) {
          setCurrentStep(stepIndex);
        }
        
        return newProgress >= 100 ? 100 : newProgress;
      });
    }, 50);

    return () => clearInterval(interval);
  }, [isScanning, currentStep, steps.length]);

  if (!isScanning) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-card p-8 rounded-lg shadow-2xl max-w-md w-full mx-4">
        {/* 扫描图标动画 */}
        <div className="flex justify-center mb-6">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-muted rounded-full flex items-center justify-center">
              <Icon 
                name={foodType === 'image' ? "RiImageLine" : "RiSearchLine"} 
                className="w-8 h-8 text-primary animate-pulse" 
              />
            </div>
            {/* 扫描线动画 */}
            <div className="absolute inset-0 border-4 border-transparent border-t-primary rounded-full animate-spin"></div>
          </div>
        </div>

        {/* 进度条 */}
        <div className="mb-4">
          <div className="w-full bg-muted rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-primary to-green-500 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${scanProgress}%` }}
            ></div>
          </div>
        </div>

        {/* 当前步骤 */}
        <div className="text-center">
          <p className="text-sm text-muted-foreground mb-2">
            {steps[currentStep] || steps[0]}
          </p>
          <p className="text-xs text-muted-foreground">
            {scanProgress}% complete
          </p>
          {foodType === 'image' && currentStep >= 1 && (
            <p className="text-xs text-primary mt-1 animate-pulse">
              🤖 AI processing......
            </p>
          )}
        </div>

        {/* 扫描效果 */}
        <div className="mt-4 flex justify-center space-x-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-2 h-2 bg-primary rounded-full animate-bounce"
              style={{ animationDelay: `${i * 0.2}s` }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
}

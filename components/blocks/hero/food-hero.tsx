"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import HappyUsers from "./happy-users";
import HeroBg from "./bg";
import FoodBg from "./food-bg";
import FoodInputSection from "./food-input-section";
import TrustIndicators from "./trust-indicators";
import FoodResultDisplay from "./food-result";
import { FoodHero as FoodHeroType } from "@/types/blocks/food-hero";
import { useFoodContext } from "@/contexts/food-context";
import Icon from "@/components/icon";
import Link from "next/link";

export default function FoodHero({ hero }: { hero: FoodHeroType }) {
  if (hero.disabled) {
    return null;
  }

  const { result, clearResult } = useFoodContext();

  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }

  // 如果有结果，显示结果页面
  if (result) {
    return (
      <>
        <FoodBg />
        <section className="py-16 md:py-24 relative">
          <div className="container">
            <FoodResultDisplay result={result} onClose={clearResult} />
          </div>
        </section>
      </>
    );
  }

  return (
    <>
      <FoodBg />
      <section className="py-16 md:py-24 relative">
        <div className="container">
          {hero.show_badge && (
            <div className="flex items-center justify-center mb-8">
              <img
                src="/imgs/badges/phdaily.svg"
                alt="phdaily"
                className="h-10 object-cover"
              />
            </div>
          )}
          
          <div className="text-center">
            {hero.announcement && (
              <a
                href={hero.announcement.url}
                className="mx-auto mb-3 inline-flex items-center gap-3 rounded-full border px-2 py-1 text-sm"
              >
                {hero.announcement.label && (
                  <Badge>{hero.announcement.label}</Badge>
                )}
                {hero.announcement.title}
              </a>
            )}

            {texts && texts.length > 1 ? (
              <h1 className="mx-auto mb-3 mt-2 max-w-4xl text-balance text-3xl sm:text-4xl font-bold lg:mb-7 lg:text-7xl">
                {texts[0]}
                <span className="bg-gradient-to-r from-green-600 via-green-500 to-green-600 bg-clip-text text-transparent">
                  {highlightText}
                </span>
                {texts[1]}
              </h1>
            ) : (
              <h1 className="mx-auto mb-3 mt-2 max-w-4xl text-balance text-3xl sm:text-4xl font-bold lg:mb-7 lg:text-7xl">
                {hero.title}
              </h1>
            )}

            <p
              className="mx-auto max-w-3xl text-muted-foreground text-base lg:text-xl mb-6"
              dangerouslySetInnerHTML={{ __html: hero.description || "" }}
            />

            <FoodInputSection
              searchSection={hero.search_section}
              uploadSection={hero.upload_section}
            />

            {hero.tip && (
              <p className="mt-8 text-md text-muted-foreground">{hero.tip}</p>
            )}
            
            {hero.show_happy_users && <HappyUsers />}
            
            <TrustIndicators stats={hero.trust_stats} />
          </div>
        </div>
      </section>
    </>
  );
}

"use client";

import { useEffect, useState } from "react";
import Icon from "@/components/icon";
import AnimatedCounter from "@/components/ui/animated-counter";
import { TrustStat } from "@/types/blocks/food-hero";

interface TrustIndicatorsProps {
  stats?: TrustStat[];
}

export default function TrustIndicators({ stats }: TrustIndicatorsProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 500);
    return () => clearTimeout(timer);
  }, []);

  if (!stats || stats.length === 0) return null;

  // 提取数字值用于动画
  const extractNumber = (value: string): number => {
    const match = value.match(/[\d,]+/);
    return match ? parseInt(match[0].replace(/,/g, '')) : 0;
  };

  return (
    <div className="mt-12 pt-8 border-t border-muted">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const numericValue = extractNumber(stat.value);
          const suffix = stat.value.replace(/[\d,]+/, '');

          return (
            <div
              key={index}
              className="text-center animate-fadeInUp"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {stat.icon && (
                <div className="mb-2 flex justify-center">
                  <Icon
                    name={stat.icon}
                    className="w-6 h-6 text-primary animate-pulse-soft"
                  />
                </div>
              )}
              <div className="text-2xl font-bold text-foreground">
                {isVisible && numericValue > 0 ? (
                  <AnimatedCounter
                    value={numericValue}
                    suffix={suffix}
                    duration={1000 + index * 200}
                  />
                ) : (
                  stat.value
                )}
              </div>
              <div className="text-sm text-muted-foreground">{stat.title}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

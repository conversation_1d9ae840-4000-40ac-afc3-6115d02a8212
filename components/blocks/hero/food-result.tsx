"use client";

import { FoodResult } from "@/types/food";
import { formatStorageTime, getStorageColor } from "@/lib/food-api";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { useTranslations } from 'next-intl';

interface FoodResultProps {
  result: FoodResult;
  onClose: () => void;
}

export default function FoodResultDisplay({ result, onClose }: FoodResultProps) {
  const t = useTranslations('food_result');
  const storageOptions = [
    {
      type: t('refrigerated'),
      days: result.storage.refrigerated,
      icon: "RiSnowflakeLine",
      description: t('refrigerated_desc')
    },
    {
      type: t('frozen'),
      days: result.storage.frozen,
      icon: "RiSnowflakeFill",
      description: t('frozen_desc')
    },
    {
      type: t('room_temperature'),
      days: result.storage.room_temperature,
      icon: "RiTempHotLine",
      description: t('room_temperature_desc')
    }
  ].filter(option => option.days && option.days > 0);

  return (
    <div className="mt-8 p-6 bg-card border rounded-lg shadow-lg max-w-2xl mx-auto animate-fadeInUp">
      {/* Header */}
      <div className="flex items-center justify-between mb-4 animate-slideInRight">
        <div className="flex items-center gap-3">
          <h3 className="text-2xl font-bold text-foreground">{result.name}</h3>
          <Badge variant="secondary" className="animate-pulse-soft">{result.category}</Badge>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="hover:scale-110 transition-transform"
          aria-label={t('close_button')}
        >
          <Icon name="RiCloseLine" className="w-4 h-4" />
        </Button>
      </div>

      {/* USDA Authority Badge */}
      {result.isUSDAData && (
        <div className="mb-4 animate-fadeInUp" style={{ animationDelay: '0.1s' }}>
          <div className="flex items-center gap-2 text-sm">
            <div className="flex items-center gap-2 px-3 py-1.5 bg-green-50 border border-green-200 rounded-full">
              <img
                src="/imgs/badges/usda-verified.svg"
                alt="USDA Verified"
                className="w-5 h-5"
              />
              <span className="text-green-700 font-medium">USDA Verified Data</span>
            </div>
          </div>
        </div>
      )}

      {/* Storage Information */}
      <div className="mb-6 animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
        <h4 className="text-lg font-semibold mb-3 flex items-center gap-2">
          <Icon name="RiTimeLine" className="w-5 h-5 animate-pulse-soft" />
          Storage Duration
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {storageOptions.map((option, index) => (
            <div
              key={index}
              className="p-4 bg-muted/50 rounded-lg hover:bg-muted/70 transition-all duration-300 hover:scale-105 animate-fadeInUp"
              style={{ animationDelay: `${0.3 + index * 0.1}s` }}
            >
              <div className="flex items-center gap-2 mb-2">
                <Icon name={option.icon} className="w-5 h-5 text-primary" />
                <span className="font-medium">{option.type}</span>
              </div>
              <div className={`text-2xl font-bold ${getStorageColor(option.days)}`}>
                {formatStorageTime(option.days)}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {option.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Storage Tips */}
      {result.tips && result.tips.length > 0 && (
        <div className="mb-4">
          <h4 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <Icon name="RiLightbulbLine" className="w-5 h-5" />
            {t('storage_tips')}
          </h4>
          <ul className="space-y-2">
            {result.tips.map((tip, index) => (
              <li key={index} className="flex items-start gap-2 text-sm">
                <Icon name="RiCheckLine" className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>{tip}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
        <Button onClick={onClose} className="flex-1 min-h-[44px]" aria-label={t('search_another')}>
          <Icon name="RiSearchLine" className="w-4 h-4 mr-2" />
          {t('search_another')}
        </Button>
        <Button variant="outline" onClick={() => window.print()} aria-label={t('print_info')} className="sm:w-auto w-full min-h-[44px]">
          <Icon name="RiPrinterLine" className="w-4 h-4 mr-2" />
          {t('print')}
        </Button>
      </div>
    </div>
  );
}

"use client";

export default function FoodBg() {
  return (
    <div className="absolute inset-0 -z-50 overflow-hidden will-change-transform">
      {/* 渐变背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-50 dark:from-green-950/20 via-background to-blue-50 dark:to-blue-950/20"></div>
      
      {/* 装饰性食物图标 */}
      <div className="absolute inset-0 opacity-5">
        {/* 苹果图标 */}
        <div className="absolute top-20 left-10 w-8 h-8 text-green-500">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12.75 2.25a.75.75 0 00-1.5 0v2.25H9a.75.75 0 000 1.5h2.25v2.25a.75.75 0 001.5 0V6H15a.75.75 0 000-1.5h-2.25V2.25z"/>
            <path d="M12 9a3.75 3.75 0 100 7.5A3.75 3.75 0 0012 9z"/>
          </svg>
        </div>
        
        {/* 胡萝卜图标 */}
        <div className="absolute top-32 right-20 w-6 h-6 text-orange-500 rotate-45">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2.25a.75.75 0 01.75.75v2.25h2.25a.75.75 0 010 1.5H12.75v2.25a.75.75 0 01-1.5 0V6.75H9a.75.75 0 010-1.5h2.25V3a.75.75 0 01.75-.75z"/>
          </svg>
        </div>
        
        {/* 叶子图标 */}
        <div className="absolute bottom-32 left-20 w-10 h-10 text-green-400">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25z"/>
          </svg>
        </div>
        
        {/* 更多装饰元素 */}
        <div className="absolute top-1/2 right-10 w-4 h-4 text-blue-400 animate-pulse">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2.25a.75.75 0 01.75.75v2.25h2.25a.75.75 0 010 1.5H12.75v2.25a.75.75 0 01-1.5 0V6.75H9a.75.75 0 010-1.5h2.25V3a.75.75 0 01.75-.75z"/>
          </svg>
        </div>
      </div>
      
      {/* 优化后的浮动粒子效果 - 减少数量并使用CSS动画 */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-green-300 rounded-full opacity-20"
            style={{
              left: `${(i * 12.5) + Math.random() * 10}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${8 + i * 0.5}s ease-in-out ${i * 0.6}s infinite`
            }}
          ></div>
        ))}
      </div>
      
      {/* 网格图案 */}
      <div className="absolute inset-0 opacity-5">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" className="text-green-500" />
        </svg>
      </div>
    </div>
  );
}

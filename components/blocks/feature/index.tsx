import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";

export default function Feature({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-12 md:py-20 bg-gradient-to-b from-muted/20 to-background">
      <div className="container">
        <div className="mx-auto flex max-w-screen-md flex-col items-center gap-2">
          <h2 className="mb-2 text-pretty text-3xl font-bold lg:text-4xl">
            {section.title}
          </h2>
          <p className="mb-8 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg">
            {section.description}
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {section.items?.map((item, i) => (
            <div key={i} className="flex flex-col text-center group hover:scale-105 transition-transform duration-300">
              {item.icon && (
                <div className="mb-5 mx-auto flex size-16 items-center justify-center rounded-full bg-gradient-to-br from-green-100 dark:from-green-900/30 to-green-50 dark:to-green-900/20 border border-green-200 dark:border-green-800 group-hover:shadow-lg transition-shadow duration-300">
                  <Icon name={item.icon} className="size-8 text-green-600 dark:text-green-400" />
                </div>
              )}
              <h3 className="mb-2 text-xl font-semibold text-foreground">{item.title}</h3>
              <p className="text-muted-foreground text-sm">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

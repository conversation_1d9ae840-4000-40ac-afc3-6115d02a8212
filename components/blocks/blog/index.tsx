"use client";

import { ArrowR<PERSON>, Search, X } from "lucide-react";
import { Blog as BlogType } from "@/types/blocks/blog";
import BlogCategoryFilter, { BlogCategory } from "@/components/blog/BlogCategoryFilter";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useState, useMemo, useEffect, useRef } from "react";

interface ExtendedBlogType extends BlogType {
  categories?: BlogCategory[];
  selectedCategory?: string | null;
}

interface BlogProps {
  blog: ExtendedBlogType;
  locale?: string;
}

const categoryColors: Record<string, string> = {
  'food_safety': 'bg-red-100 text-red-800 border-red-200',
  'frozen_foods': 'bg-blue-100 text-blue-800 border-blue-200',
  'refrigerated_foods': 'bg-cyan-100 text-cyan-800 border-cyan-200',
  'expiration_dates': 'bg-orange-100 text-orange-800 border-orange-200',
  'preparation': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'storage_tips': 'bg-purple-100 text-purple-800 border-purple-200',
};

export default function Blog({ blog, locale }: BlogProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);

  if (blog.disabled) {
    return null;
  }

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + K 聚焦搜索框
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        searchInputRef.current?.focus();
      }
      // Escape 清空搜索
      if (e.key === 'Escape' && searchTerm) {
        setSearchTerm("");
        searchInputRef.current?.blur();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [searchTerm]);

  const handleCategoryChange = (category: string | null) => {
    const params = new URLSearchParams(searchParams.toString());
    if (category) {
      params.set('category', category);
    } else {
      params.delete('category');
    }
    router.push(`${pathname}?${params.toString()}`);
  };

  // 过滤博客文章
  const filteredItems = useMemo(() => {
    if (!blog.items) return [];

    if (!searchTerm.trim()) {
      return blog.items;
    }

    const searchLower = searchTerm.toLowerCase();
    return blog.items.filter(item =>
      item.title?.toLowerCase().includes(searchLower) ||
      item.description?.toLowerCase().includes(searchLower)
    );
  }, [blog.items, searchTerm]);

  // 高亮搜索关键词
  const highlightText = (text: string, searchTerm: string) => {
    if (!searchTerm.trim()) return text;

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 text-yellow-900 px-1 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  return (
    <section className="w-full py-16">
      <div className="container flex flex-col items-center gap-8 lg:px-16">
        <div className="text-center">
          <p className="mb-6 text-xs font-medium uppercase tracking-wider">
            {blog.label}
          </p>
          <h2 className="mb-3 text-pretty text-3xl font-semibold md:mb-4 md:text-4xl lg:mb-6 lg:max-w-3xl lg:text-5xl">
            {blog.title}
          </h2>
          <p className="mb-8 text-muted-foreground md:text-base lg:max-w-2xl lg:text-lg">
            {blog.description}
          </p>
        </div>

        {/* 搜索框 */}
        <div className="w-full max-w-md relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            ref={searchInputRef}
            type="text"
            placeholder="Search FAQ articles... (Ctrl+K)"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-10 py-2 w-full"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm("")}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>

        {/* 分类过滤器 */}
        {blog.categories && blog.categories.length > 0 && (
          <BlogCategoryFilter
            categories={blog.categories}
            selectedCategory={blog.selectedCategory || null}
            onCategoryChange={handleCategoryChange}
            locale={locale}
          />
        )}

        {/* 搜索结果统计 */}
        {searchTerm && (
          <div className="w-full text-center text-sm text-muted-foreground">
            Found {filteredItems.length} article{filteredItems.length !== 1 ? 's' : ''} for "{searchTerm}"
          </div>
        )}

        {/* 博客文章列表 */}
        <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems?.map((item, idx) => (
            <a
              key={idx}
              href={item.url || `/${item.locale}/posts/${item.slug}`}
              target={item.target || "_self"}
              className="group"
            >
              <div className="flex flex-col overflow-clip rounded-xl border border-border hover:shadow-lg transition-shadow duration-200 h-full">
                {/* 暂时注释掉图片，等待更多图片资源 */}
                {/* {item.cover_url && (
                  <div className="h-48 overflow-hidden">
                    <img
                      src={item.cover_url}
                      alt={item.title || ""}
                      className="h-full w-full object-cover object-center group-hover:scale-105 transition-transform duration-200"
                    />
                  </div>
                )} */}
                <div className="flex-1 px-6 py-5">
                  {/* 分类标签 - Commented out as BlogItem doesn't have category field */}
                  {/* {item.category && (
                    <Badge 
                      variant="outline" 
                      className={cn("mb-3", categoryColors[item.category])}
                    >
                      <span className="mr-1">{item.category_icon}</span>
                      {item.category.replace(/_/g, ' ').charAt(0).toUpperCase() + item.category.slice(1).replace(/_/g, ' ')}
                    </Badge>
                  )} */}
                  
                  <h3 className="mb-3 text-lg font-semibold line-clamp-2 group-hover:text-primary transition-colors">
                    {searchTerm ? highlightText(item.title || "", searchTerm) : item.title}
                  </h3>
                  <p className="mb-4 text-sm text-muted-foreground line-clamp-3">
                    {searchTerm ? highlightText(item.description || "", searchTerm) : item.description}
                  </p>
                  {blog.read_more_text && (
                    <p className="flex items-center text-sm font-medium text-primary hover:underline">
                      {blog.read_more_text}
                      <ArrowRight className="ml-1 size-4" />
                    </p>
                  )}
                </div>
              </div>
            </a>
          ))}
        </div>

        {/* 无结果提示 */}
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-medium mb-2">
              {searchTerm ? "No articles found" : "No FAQ articles found in this category"}
            </h3>
            <p className="text-muted-foreground">
              {searchTerm
                ? `Try searching for different keywords or browse by category`
                : "Try selecting a different category or use the search function"
              }
            </p>
          </div>
        )}
      </div>
    </section>
  );
}
import { Badge } from "@/components/ui/badge";
import { Section as SectionType } from "@/types/blocks/section";
import PopularFAQs from "@/components/faq/PopularFAQs";
import { getPopularFAQs } from "@/lib/faq-service";
import { Suspense } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { getTranslations } from 'next-intl/server';

// FAQ加载组件
async function FAQContent({ locale }: { locale: string }) {
  try {
    const popularFAQs = await getPopularFAQs(6);

    if (popularFAQs.length > 0) {
      return (
        <PopularFAQs
          faqs={popularFAQs}
          maxItems={6}
          showViewAll={true}
          compact={true}
          locale={locale}
        />
      );
    }
  } catch (error) {
    console.error('Failed to load FAQ data:', error);
  }

  return null;
}

// FAQ加载骨架屏
function FAQSkeleton() {
  return (
    <div className="grid md:grid-cols-2 gap-8">
      {[1, 2, 3, 4, 5, 6].map((i) => (
        <div key={i} className="flex items-start gap-3">
          <Skeleton className="w-8 h-8 rounded-full flex-shrink-0" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-3 w-4/5" />
            <Skeleton className="h-3 w-3/5" />
          </div>
        </div>
      ))}
    </div>
  );
}

export default function FAQ({ section, locale = 'zh' }: { section: SectionType; locale?: string }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-12 md:py-20 bg-muted/20">
      <div className="container">
        <div className="text-center">
          {section.label && (
            <Badge className="text-xs font-medium">{section.label}</Badge>
          )}
          <h2 className="mt-4 text-4xl font-semibold">{section.title}</h2>
          <p className="mt-6 font-medium text-muted-foreground">
            {section.description}
          </p>
        </div>


        {/* 原有的静态FAQ内容（如果存在） */}
        {section.items && section.items.length > 0 && (
          <div className="mx-auto mt-14 max-w-4xl grid gap-6">
            {section.items.map((item, index) => (
              <div key={index} className="bg-card rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                <div className="flex gap-4">
                  <span className="flex size-8 shrink-0 items-center justify-center rounded-full bg-green-100 font-semibold text-sm text-green-600">
                    {index + 1}
                  </span>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg mb-2">{item.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {item.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
}

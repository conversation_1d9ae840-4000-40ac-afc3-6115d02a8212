import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import Link from "next/link";
import { useTranslations } from "next-intl";

interface FeedbackCTAProps {
  title?: string;
  description?: string;
  buttonText?: string;
  buttonUrl?: string;
  locale?: string;
}

export default function FeedbackCTA({
  title,
  description,
  buttonText,
  buttonUrl,
  locale = "zh"
}: FeedbackCTAProps) {
  const t = useTranslations("landing.feedback_cta");
  // 构建正确的URL
  const feedbackUrl = buttonUrl || `/${locale}/feedback`;
  
  // 使用国际化文本或传入的自定义文本
  const finalTitle = title || t("title");
  const finalDescription = description || t("description");
  const finalButtonText = buttonText || t("button_text");

  return (
    <section className="py-8 md:py-16 bg-gradient-to-br from-green-50 dark:from-green-950/20 to-blue-50 dark:to-blue-950/20">
      <div className="container max-w-4xl mx-auto text-center">
        <div className="space-y-6">
          {/* 图标 */}
          <div className="flex justify-center">
            <div className="p-4 bg-background rounded-full shadow-lg dark:shadow-gray-800/50">
              <Icon name="RiMessage3Line" className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
          </div>
          
          {/* 标题和描述 */}
          <div className="space-y-3">
            <h2 className="text-3xl font-bold text-foreground">{finalTitle}</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {finalDescription}
            </p>
          </div>
          
          {/* 按钮 */}
          <div className="pt-4">
            <Link href={feedbackUrl}>
              <Button size="lg" className="shadow-lg hover:shadow-xl transition-shadow">
                {finalButtonText}
                <Icon name="RiArrowRightLine" className="ml-2 w-4 h-4" />
              </Button>
            </Link>
          </div>
        </div>
        
        {/* 装饰性元素 */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-green-100 dark:bg-green-900/20 rounded-full blur-3xl opacity-50"></div>
          <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-blue-100 dark:bg-blue-900/20 rounded-full blur-3xl opacity-50"></div>
        </div>
      </div>
    </section>
  );
}
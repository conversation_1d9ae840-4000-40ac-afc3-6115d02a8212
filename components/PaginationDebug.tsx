'use client';

interface PaginationDebugProps {
  page: number;
  pageSize: number;
  total: number;
  hasMore: boolean;
  foodsCount: number;
}

export default function PaginationDebug({
  page,
  pageSize,
  total,
  hasMore,
  foodsCount
}: PaginationDebugProps) {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const totalPages = Math.ceil(total / pageSize);

  return (
    <div className="mt-4 p-4 bg-gray-100 rounded-lg text-sm">
      <h4 className="font-bold mb-2">调试信息：</h4>
      <ul className="space-y-1">
        <li>当前页: {page}</li>
        <li>每页数量: {pageSize}</li>
        <li>总数据量: {total}</li>
        <li>总页数: {totalPages}</li>
        <li>当前页数据量: {foodsCount}</li>
        <li>是否有下一页: {hasMore ? '是' : '否'}</li>
        <li>计算公式: {page} * {pageSize} = {page * pageSize} {hasMore ? '<' : '>='} {total}</li>
      </ul>
    </div>
  );
}
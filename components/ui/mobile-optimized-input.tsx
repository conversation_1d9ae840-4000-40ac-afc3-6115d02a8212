"use client";

import { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { useTranslations } from "next-intl";

interface MobileOptimizedInputProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  disabled?: boolean;
  examples?: string[];
}

export default function MobileOptimizedInput({
  placeholder,
  value,
  onChange,
  onSubmit,
  disabled = false,
  examples = []
}: MobileOptimizedInputProps) {
  const t = useTranslations('landing.hero');
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // 移动端键盘优化
  useEffect(() => {
    if (isFocused && inputRef.current) {
      // 滚动到输入框位置
      setTimeout(() => {
        inputRef.current?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
      }, 300);
    }
  }, [isFocused]);

  return (
    <div className="w-full">
      {/* 输入框容器 */}
      <div className={`relative transition-all duration-300 ${
        isFocused ? 'transform scale-105' : ''
      }`}>
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyPress={(e) => e.key === 'Enter' && !disabled && onSubmit()}
          className={`h-16 text-lg pr-12 transition-all duration-300 ${
            isFocused 
              ? 'ring-2 ring-primary ring-offset-2 shadow-lg' 
              : 'shadow-md'
          }`}
          disabled={disabled}
        />
        
        {/* 清除按钮 */}
        {value && (
          <button
            onClick={() => onChange('')}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-muted transition-colors"
            disabled={disabled}
          >
            <Icon name="RiCloseLine" className="w-4 h-4 text-muted-foreground" />
          </button>
        )}
      </div>

      {/* 示例按钮 - 移动端优化 */}
      {examples.length > 0 && (
        <div className="mt-3 flex flex-wrap gap-2">
          {examples.map((example, index) => (
            <button
              key={index}
              onClick={() => onChange(example)}
              disabled={disabled}
              className="px-3 py-2 text-sm bg-muted rounded-full hover:bg-muted/80 active:scale-95 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation"
            >
              {example}
            </button>
          ))}
        </div>
      )}

      {/* 移动端提示 */}
      <div className="mt-2 text-xs text-muted-foreground text-center sm:hidden">
        {t('mobile_hint') || 'Tap to search or choose from examples above'}
      </div>
    </div>
  );
}

"use client";

import React from 'react';
import { Button } from "@/components/ui/button";
import { ExternalLink, FileText, ArrowRight } from "lucide-react";
import Link from "next/link";
import { generateFAQLink, generateSourceLink } from "@/lib/faq-utils";
import { useTranslations } from 'next-intl';

interface ViewSourceButtonProps {
  /** 内容类型 */
  type: 'faq' | 'blog';
  /** 内容ID */
  id: string;
  /** 语言环境 */
  locale?: string;
  /** 按钮文本，默认为"查看原文" */
  text?: string;
  /** 按钮变体 */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  /** 按钮大小 */
  size?: "default" | "sm" | "lg" | "icon";
  /** 是否显示图标 */
  showIcon?: boolean;
  /** 自定义图标 */
  icon?: React.ReactNode;
  /** 是否在新窗口打开 */
  openInNewTab?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 点击事件回调 */
  onClick?: () => void;
}

/**
 * 查看原文按钮组件
 * 用于跳转到FAQ页面的特定问题或博客文章
 */
export default function ViewSourceButton({
  type,
  id,
  locale = 'zh',
  text = "查看原文",
  variant = "outline",
  size = "sm",
  showIcon = true,
  icon,
  openInNewTab = false,
  className = "",
  onClick
}: ViewSourceButtonProps) {
  // 生成链接
  const href = generateSourceLink({ type, id, locale });

  // 默认图标
  const defaultIcon = type === 'faq' ? <FileText className="w-4 h-4" /> : <ExternalLink className="w-4 h-4" />;
  const displayIcon = icon || defaultIcon;

  // 处理点击事件
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={`inline-flex items-center gap-2 ${className}`}
      onClick={handleClick}
      asChild
    >
      <Link 
        href={href}
        target={openInNewTab ? "_blank" : "_self"}
        rel={openInNewTab ? "noopener noreferrer" : undefined}
      >
        {showIcon && displayIcon}
        <span>{text}</span>
        {!showIcon && <ArrowRight className="w-4 h-4 ml-1" />}
      </Link>
    </Button>
  );
}

/**
 * FAQ查看原文按钮的快捷组件
 */
export function ViewFAQButton({
  faqId,
  locale = 'zh',
  text,
  ...props
}: Omit<ViewSourceButtonProps, 'type' | 'id'> & { faqId: string; text?: string }) {
  const t = useTranslations('faq');

  return (
    <ViewSourceButton
      type="faq"
      id={faqId}
      locale={locale}
      text={text || t('view_original')}
      icon={<FileText className="w-4 h-4" />}
      {...props}
    />
  );
}

/**
 * 博客查看原文按钮的快捷组件
 */
export function ViewBlogButton({
  blogId,
  locale = 'zh',
  text,
  ...props
}: Omit<ViewSourceButtonProps, 'type' | 'id'> & { blogId: string; text?: string }) {
  const t = useTranslations('faq');

  return (
    <ViewSourceButton
      type="blog"
      id={blogId}
      locale={locale}
      text={text || t('read_more')}
      icon={<ExternalLink className="w-4 h-4" />}
      {...props}
    />
  );
}

/**
 * 带有高亮效果的FAQ按钮
 */
export function HighlightFAQButton({
  faqId,
  locale = 'zh',
  text,
  ...props
}: Omit<ViewSourceButtonProps, 'type' | 'id'> & { faqId: string; text?: string }) {
  const t = useTranslations('faq');

  return (
    <ViewSourceButton
      type="faq"
      id={faqId}
      locale={locale}
      text={text || t('view_original')}
      variant="default"
      className="bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200"
      icon={<FileText className="w-4 h-4" />}
      {...props}
    />
  );
}

/**
 * 紧凑型查看原文按钮
 */
export function CompactViewSourceButton({
  type,
  id,
  locale = 'zh',
  ...props
}: Pick<ViewSourceButtonProps, 'type' | 'id' | 'locale'> & Partial<ViewSourceButtonProps>) {
  const t = useTranslations('faq');

  return (
    <ViewSourceButton
      type={type}
      id={id}
      locale={locale}
      text={t('view_short')}
      variant="ghost"
      size="sm"
      showIcon={true}
      className="h-8 px-2 text-xs"
      {...props}
    />
  );
}

/**
 * 卡片底部的查看原文按钮
 */
export function CardViewSourceButton({
  type,
  id,
  locale = 'zh',
  ...props
}: Pick<ViewSourceButtonProps, 'type' | 'id' | 'locale'> & Partial<ViewSourceButtonProps>) {
  const t = useTranslations('faq');

  return (
    <ViewSourceButton
      type={type}
      id={id}
      locale={locale}
      text={t('view_original')}
      variant="outline"
      size="sm"
      showIcon={true}
      className="w-full justify-center border-dashed hover:border-solid"
      {...props}
    />
  );
}

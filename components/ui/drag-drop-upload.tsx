"use client";

import { useState, useRef, DragEvent } from "react";
import Icon from "@/components/icon";
import { validateImageFile } from "@/lib/food-api";
import { useTranslations } from "next-intl";

interface DragDropUploadProps {
  onFileSelect: (file: File) => void;
  onError: (error: string) => void;
  accept?: string;
  maxSize?: string;
  disabled?: boolean;
  currentFile?: File | null;
}

export default function DragDropUpload({
  onFileSelect,
  onError,
  accept = "image/*",
  maxSize = "5MB",
  disabled = false,
  currentFile = null
}: DragDropUploadProps) {
  const t = useTranslations('landing.hero.upload');
  const [isDragOver, setIsDragOver] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileSelection = (file: File) => {
    const validation = validateImageFile(file);
    if (!validation.valid) {
      onError(validation.error || 'Invalid file');
      return;
    }
    
    onFileSelect(file);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelection(file);
    }
  };

  const handleClick = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  return (
    <div className="w-full">
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileInputChange}
        disabled={disabled}
        className="hidden"
      />
      
      <div
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={`
          relative h-40 sm:h-20 border-2 border-dashed rounded-lg 
          flex flex-col sm:flex-row items-center justify-center gap-3 
          cursor-pointer transition-all duration-300 touch-manipulation
          ${disabled 
            ? 'border-muted-foreground/10 cursor-not-allowed bg-muted/20' 
            : isDragOver 
              ? 'border-primary bg-primary/10 scale-105' 
              : currentFile 
                ? 'border-green-300 bg-green-50' 
                : isHovered
                  ? 'border-primary/50 bg-primary/5 scale-102'
                  : 'border-muted-foreground/25 hover:border-muted-foreground/50'
          }
        `}
      >
        {/* 上传图标和文本 */}
        <div className="flex items-center gap-3 text-muted-foreground">
          <Icon 
            name={currentFile ? "RiCheckLine" : "RiImageLine"} 
            className={`w-7 h-7 ${currentFile ? 'text-green-600' : ''}`} 
          />
          <div className="text-center sm:text-left">
            <span className="text-base font-medium">
              {currentFile 
                ? currentFile.name 
                : isDragOver 
                  ? t('drop_here') 
                  : t('upload_photo')
              }
            </span>
            <div className="text-sm text-muted-foreground sm:hidden">
              {t('tap_to_select')}
            </div>
          </div>
        </div>

        {/* 拖拽提示 - 仅桌面端显示 */}
        {!currentFile && (
          <div className="hidden sm:block text-sm text-muted-foreground">
            {isDragOver ? t('drop_it') : t('or_drag_drop')}
          </div>
        )}

        {/* 加载动画覆盖层 */}
        {disabled && (
          <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 rounded-lg flex items-center justify-center">
            <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>

      {/* 文件信息 */}
      <div className="mt-3 text-sm text-muted-foreground text-center">
        {t('max_size')}: {maxSize} • {t('formats')}: JPEG, PNG, WebP
      </div>
    </div>
  );
}

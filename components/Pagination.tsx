import React from 'react';
import Link from 'next/link';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
  queryParams?: Record<string, string>;
  locale: string;
  previousText: string;
  nextText: string;
}

export default function Pagination({
  currentPage,
  totalPages,
  baseUrl,
  queryParams = {},
  locale,
  previousText,
  nextText
}: PaginationProps) {
  // 不显示分页如果只有一页
  if (totalPages <= 1) {
    return null;
  }

  // 生成URL
  const generateUrl = (page: number) => {
    const params = new URLSearchParams(queryParams);
    if (page > 1) {
      params.set('page', page.toString());
    } else {
      params.delete('page');
    }
    const queryString = params.toString();
    return `/${locale}${baseUrl}${queryString ? `?${queryString}` : ''}`;
  };

  // 生成页码数组
  const generatePageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisible = 7; // 最多显示7个页码按钮
    
    if (totalPages <= maxVisible) {
      // 如果总页数小于等于最大显示数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 否则使用省略号
      if (currentPage <= 4) {
        // 当前页靠近开始
        for (let i = 1; i <= 5; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 3) {
        // 当前页靠近结束
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 当前页在中间
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const pageNumbers = generatePageNumbers();

  return (
    <div className="mt-12 flex justify-center">
      <nav className="flex items-center space-x-1" aria-label="Pagination">
        {/* 上一页按钮 */}
        {currentPage > 1 ? (
          <Link
            href={generateUrl(currentPage - 1)}
            className="px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground border rounded-md hover:bg-accent transition-colors"
          >
            {previousText}
          </Link>
        ) : (
          <span className="px-3 py-2 text-sm font-medium text-muted-foreground/50 border rounded-md cursor-not-allowed">
            {previousText}
          </span>
        )}

        {/* 页码 */}
        {pageNumbers.map((pageNum, index) => {
          if (pageNum === '...') {
            return (
              <span key={`ellipsis-${index}`} className="px-2 py-2 text-gray-500">
                ...
              </span>
            );
          }

          const page = pageNum as number;
          const isActive = page === currentPage;

          return isActive ? (
            <span
              key={page}
              className="px-3 py-2 text-sm font-medium text-white bg-green-600 border border-green-600 rounded-md"
            >
              {page}
            </span>
          ) : (
            <Link
              key={page}
              href={generateUrl(page)}
              className="px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground border rounded-md hover:bg-accent transition-colors"
            >
              {page}
            </Link>
          );
        })}

        {/* 下一页按钮 */}
        {currentPage < totalPages ? (
          <Link
            href={generateUrl(currentPage + 1)}
            className="px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground border rounded-md hover:bg-accent transition-colors"
          >
            {nextText}
          </Link>
        ) : (
          <span className="px-3 py-2 text-sm font-medium text-muted-foreground/50 border rounded-md cursor-not-allowed">
            {nextText}
          </span>
        )}
      </nav>
    </div>
  );
}
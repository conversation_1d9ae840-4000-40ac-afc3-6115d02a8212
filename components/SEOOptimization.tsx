import React from 'react';
import Head from 'next/head';
import { FoodCategory } from '@/lib/food-categories';

interface CategorySEOProps {
  category: FoodCategory;
  locale: string;
  totalFoods: number;
}

/**
 * 分类页面SEO优化组件
 */
export const CategorySEO: React.FC<CategorySEOProps> = ({ 
  category, 
  locale, 
  totalFoods 
}) => {
  const isZh = locale === 'zh';
  const categoryName = isZh ? category.name.zh : category.name.en;
  const categoryDescription = isZh ? category.description.zh : category.description.en;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": `${categoryName} - 食物保鲜指南`,
    "description": categoryDescription,
    "url": `https://howlongfresh.site/${locale}/category/${category.slug}`,
    "inLanguage": locale,
    "mainEntity": {
      "@type": "ItemList",
      "name": `${categoryName}保存指南`,
      "description": `关于${categoryName}的保存方法、保质期和储存建议`,
      "numberOfItems": totalFoods,
      "itemListOrder": "https://schema.org/ItemListOrderAscending"
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": isZh ? "首页" : "Home",
          "item": `https://howlongfresh.site/${locale}`
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": isZh ? "分类浏览" : "Categories",
          "item": `https://howlongfresh.site/${locale}#categories`
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": categoryName,
          "item": `https://howlongfresh.site/${locale}/category/${category.slug}`
        }
      ]
    },
    "publisher": {
      "@type": "Organization",
      "name": "HowLongFresh",
      "url": "https://howlongfresh.site",
      "logo": {
        "@type": "ImageObject",
        "url": "https://howlongfresh.site/logo.png"
      }
    }
  };

  return (
    <Head>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      
      {/* Open Graph 标签 */}
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content="HowLongFresh" />
      <meta property="og:locale" content={locale} />
      <meta property="og:image" content={`https://howlongfresh.site/api/og/category/${category.slug}`} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={`${categoryName}保鲜指南`} />
      
      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@HowLongFresh" />
      <meta name="twitter:image" content={`https://howlongfresh.site/api/og/category/${category.slug}`} />
      
      {/* 额外的SEO标签 */}
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="googlebot" content="index, follow" />
      <link rel="canonical" href={`https://howlongfresh.site/${locale}/category/${category.slug}`} />
      
      {/* 预加载关键资源 */}
      <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="" />
      <link rel="dns-prefetch" href="//images.unsplash.com" />
      <link rel="preconnect" href="https://images.unsplash.com" crossOrigin="" />
    </Head>
  );
};

interface FoodItemSEOProps {
  foodName: string;
  category: string;
  storage: {
    refrigerated?: number;
    frozen?: number;
    room_temperature?: number;
  };
  tips: string[];
  isUSDAData?: boolean;
}

/**
 * 食物详情页SEO优化组件
 */
export const FoodItemSEO: React.FC<FoodItemSEOProps> = ({
  foodName,
  category,
  storage,
  tips,
  isUSDAData = false
}) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Recipe",
    "name": `${foodName}保存指南`,
    "description": `${foodName}的保存方法、保质期和储存建议`,
    "image": `https://howlongfresh.site/api/og/food/${encodeURIComponent(foodName)}`,
    "author": {
      "@type": "Organization",
      "name": isUSDAData ? "USDA" : "HowLongFresh"
    },
    "publisher": {
      "@type": "Organization",
      "name": "HowLongFresh",
      "url": "https://howlongfresh.site"
    },
    "recipeCategory": category,
    "keywords": [
      foodName,
      "保鲜",
      "保质期",
      "储存",
      "食物安全",
      category
    ].join(", "),
    "recipeInstructions": tips.map((tip, index) => ({
      "@type": "HowToStep",
      "name": `储存步骤 ${index + 1}`,
      "text": tip
    })),
    "nutrition": {
      "@type": "NutritionInformation",
      "description": `${foodName}的营养信息和储存建议`
    },
    "aggregateRating": isUSDAData ? {
      "@type": "AggregateRating",
      "ratingValue": "5",
      "ratingCount": "1",
      "bestRating": "5",
      "worstRating": "1"
    } : undefined
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
};

/**
 * 主页SEO优化组件
 */
export const HomeSEO: React.FC<{ locale: string }> = ({ locale }) => {
  const isZh = locale === 'zh';
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "HowLongFresh",
    "alternateName": isZh ? "食物保鲜指南" : "Food Freshness Guide",
    "url": "https://howlongfresh.site",
    "description": isZh 
      ? "AI驱动的食物保鲜指南，提供准确的食物保质期信息和储存建议"
      : "AI-powered food freshness guide providing accurate shelf life information and storage tips",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://howlongfresh.site/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "HowLongFresh",
      "url": "https://howlongfresh.site",
      "logo": {
        "@type": "ImageObject",
        "url": "https://howlongfresh.site/logo.png",
        "width": 512,
        "height": 512
      },
      "sameAs": [
        "https://twitter.com/HowLongFresh",
        "https://github.com/howlongfresh"
      ]
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
};

/**
 * 性能优化组件 - 预加载关键资源
 */
export const PerformanceOptimization: React.FC = () => {
  return (
    <Head>
      {/* 预加载关键字体 */}
      <link
        rel="preload"
        href="/fonts/inter-var.woff2"
        as="font"
        type="font/woff2"
        crossOrigin=""
      />
      
      {/* DNS预解析 */}
      <link rel="dns-prefetch" href="//images.unsplash.com" />
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      
      {/* 预连接关键第三方域名 */}
      <link rel="preconnect" href="https://images.unsplash.com" crossOrigin="" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
      
      {/* 资源提示 */}
      <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      
      {/* 预加载关键CSS */}
      <link rel="preload" href="/css/critical.css" as="style" />
    </Head>
  );
};

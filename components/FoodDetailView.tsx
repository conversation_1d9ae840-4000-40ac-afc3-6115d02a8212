'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, Clock, Thermometer, Snowflake, Home, CheckCircle, Lightbulb } from 'lucide-react';
import { FoodResult } from '@/types/food';
import { getFoodImageUrl } from '@/lib/food-images';
import { formatStorageTime as formatStorageTimeEN, formatStorageTimeChinese } from '@/lib/food-api';
import { useTranslations } from 'next-intl';
import { getFoodNameTranslation, getCategoryTranslation } from '@/lib/food-translations';

interface FoodDetailViewProps {
  food: FoodResult;
  locale: string;
}

const FoodDetailView: React.FC<FoodDetailViewProps> = ({ food, locale }) => {
  const t = useTranslations('food_detail');
  const tb = useTranslations('breadcrumb');
  const imageUrl = getFoodImageUrl(food.name, food.category);

  // Get translated food name and category
  const translatedFoodName = getFoodNameTranslation(food.name, locale);
  const translatedCategory = getCategoryTranslation(food.category, locale);

  // Format storage time based on locale
  const formatStorageTime = (days: number | undefined) => {
    if (!days || days <= 0) return null;
    
    if (locale === 'zh') {
      return formatStorageTimeChinese(days);
    } else {
      return formatStorageTimeEN(days);
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb navigation */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
        <Link href={`/${locale}`} className="hover:text-gray-900">
          {tb('home')}
        </Link>
        <span>/</span>
        <Link href={`/${locale}/#categories`} className="hover:text-gray-900">
          {tb('categories')}
        </Link>
        <span>/</span>
        <span className="text-gray-900 font-medium">{translatedFoodName}</span>
      </nav>

      {/* Main content */}
      <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-lg overflow-hidden">
        {/* Header area */}
        <div className="relative">
          {/* Back button */}
          <button
            onClick={() => window.history.back()}
            className="absolute top-4 left-4 z-10 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full p-2 shadow-md hover:bg-white dark:hover:bg-gray-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-700" />
          </button>

          {/* Placeholder area */}
          <div className="relative h-32 bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
            {/* USDA certification badge */}
            {food.isUSDAData && (
              <div className="bg-green-600 text-white text-sm font-medium px-3 py-1 rounded-full flex items-center space-x-2">
                <CheckCircle className="w-4 h-4" />
                <span>{t('usda_certified')}</span>
              </div>
            )}
          </div>

          {/* Food basic information */}
          <div className="p-6 md:p-8">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                  {translatedFoodName}
                </h1>
                <p className="text-lg text-gray-600">
                  {translatedCategory} • {t('confidence')}: {Math.round((food.confidence || 0.95) * 100)}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Storage information cards */}
        <div className="px-6 md:px-8 pb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            {/* Room temperature storage */}
            <div className="bg-orange-50 dark:bg-orange-900/20 rounded-xl p-6 border border-orange-100 dark:border-orange-800">
              <div className="flex items-center mb-3">
                <Home className="w-6 h-6 text-orange-600 mr-2" />
                <h3 className="text-lg font-semibold text-orange-900 dark:text-orange-200">{t('room_temp')}</h3>
              </div>
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {formatStorageTime(food.storage.room_temperature) || (
                  <span className="text-lg text-gray-500">{t('not_applicable')}</span>
                )}
              </div>
              <p className="text-sm text-orange-700 dark:text-orange-300">
                {t('room_temp_range')}
              </p>
            </div>

            {/* Refrigerated storage */}
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-100 dark:border-blue-800">
              <div className="flex items-center mb-3">
                <Thermometer className="w-6 h-6 text-blue-600 mr-2" />
                <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-200">{t('refrigerator')}</h3>
              </div>
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {formatStorageTime(food.storage.refrigerated) || (
                  <span className="text-lg text-gray-500">{t('not_applicable')}</span>
                )}
              </div>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {t('refrigerator_range')}
              </p>
            </div>

            {/* Frozen storage */}
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-xl p-6 border border-purple-100 dark:border-purple-800">
              <div className="flex items-center mb-3">
                <Snowflake className="w-6 h-6 text-purple-600 mr-2" />
                <h3 className="text-lg font-semibold text-purple-900 dark:text-purple-200">{t('freezer')}</h3>
              </div>
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {formatStorageTime(food.storage.frozen) || (
                  <span className="text-lg text-gray-500">{t('not_applicable')}</span>
                )}
              </div>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                {t('freezer_range')}
              </p>
            </div>
          </div>

          {/* Storage tips */}
          {food.tips && food.tips.length > 0 && (
            <div className="bg-green-50 dark:bg-green-900/20 rounded-xl p-6 border border-green-100 dark:border-green-800">
              <div className="flex items-center mb-4">
                <Lightbulb className="w-6 h-6 text-green-600 mr-2" />
                <h3 className="text-xl font-semibold text-green-900 dark:text-green-200">{t('storage_tips')}</h3>
              </div>
              <ul className="space-y-3">
                {food.tips.map((tip, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-green-800 dark:text-green-200">{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Data source information */}
          <div className="mt-8 p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2" />
                <span>{t('data_source')}: {food.source === 'USDA' ? t('data_source_usda') : t('data_source_local')}</span>
              </div>
              <div>
                {t('confidence')}: {Math.round((food.confidence || 0.95) * 100)}%
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-4 mt-8">
            <button
              onClick={() => window.print()}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
              </svg>
              {t('print')}
            </button>
            <Link
              href={`/${locale}`}
              className="flex-1 bg-muted hover:bg-muted/80 text-foreground font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              {t('search_other')}
            </Link>
          </div>
        </div>
      </div>

      {/* Disclaimer */}
      <div className="mt-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <p className="text-sm text-yellow-800 dark:text-yellow-200">
          <strong>{t('food_safety_disclaimer')}：</strong>
          {t('disclaimer_text')}
        </p>
      </div>
    </div>
  );
};

export default FoodDetailView;
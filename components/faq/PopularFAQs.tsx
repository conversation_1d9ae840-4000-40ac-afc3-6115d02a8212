"use client";

import React from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { FAQItem } from '@/types/faq';

interface PopularFAQsProps {
  faqs: FAQItem[];
  title?: string;
  description?: string;
  maxItems?: number;
  showViewAll?: boolean;
  compact?: boolean;
  locale?: string;
}

export default function PopularFAQs({
  faqs,
  title,
  description,
  maxItems = 6,
  showViewAll = true,
  compact = false,
  locale = 'zh'
}: PopularFAQsProps) {
  const t = useTranslations('faq');

  // 使用翻译作为默认值
  const displayTitle = title || t('popular_title');
  const displayDescription = description || t('popular_description');

  // 限制显示的FAQ数量
  const displayFAQs = faqs.slice(0, maxItems);

  if (displayFAQs.length === 0) {
    return null;
  }

  return (
    <div className="w-full space-y-6">
      {/* 标题部分 */}
      {!compact && (
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-bold text-gray-900">{displayTitle}</h2>
          <p className="text-gray-600">{displayDescription}</p>
        </div>
      )}

      {/* 两栏FAQ布局 */}
      <div className="grid md:grid-cols-2 gap-8">
        {displayFAQs.map((faq, index) => (
          <div key={faq.id} className="space-y-4">
            <div className="flex items-start gap-3">
              <span className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-600 text-sm font-bold flex-shrink-0 mt-1">
                {index + 1}
              </span>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-3 text-base leading-relaxed">
                  {faq.question}
                </h3>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {faq.answer_summary || (faq.answer.length > 150 ? faq.answer.substring(0, 150) + "..." : faq.answer)}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 查看全部按钮 */}
      {showViewAll && faqs.length > maxItems && (
        <div className="text-center">
          <Link href={locale === 'zh' ? '/zh/faq' : '/faq'}>
            <Button variant="outline" className="group">
              {t('view_all_count', { count: faqs.length })}
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      )}

      {/* 底部提示 */}
      {!compact && (
        <div className="text-center text-sm text-gray-500">
          <p>
            {t.rich('source_note', {
              usda: (chunks) => <span className="font-medium">{chunks}</span>,
              stilltasty: (chunks) => <span className="font-medium">{chunks}</span>
            })}
          </p>
        </div>
      )}
    </div>
  );
}

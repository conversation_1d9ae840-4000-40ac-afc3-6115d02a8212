"use client";

import React, { useState, useMemo, useEffect, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Search, ChevronDown, ChevronUp, ExternalLink, Tag } from "lucide-react";
import { ViewFAQButton } from "@/components/ui/view-source-button";

// FAQ数据类型定义
interface FAQItem {
  id: string;
  question: string;
  answer: string;
  answer_summary: string;
  category: string;
  category_zh: string;
  category_icon: string;
  related_foods: string[];
  tags: string[];
  key_points: string[];
  source: {
    name: string;
    url: string;
  };
}

interface FAQCategory {
  name: string;
  name_zh: string;
  icon: string;
  count: number;
}

interface FAQListProps {
  faqs: FAQItem[];
  categories: FAQCategory[];
  title?: string;
  description?: string;
  showSearch?: boolean;
  showCategories?: boolean;
  maxItems?: number;
  highlightId?: string; // 新增：用于高亮显示特定FAQ的ID
  locale?: string; // 新增：语言环境
}

export default function FAQList({
  faqs,
  categories,
  title,
  description,
  showSearch = true,
  showCategories = true,
  maxItems,
  highlightId,
  locale = 'zh'
}: FAQListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());
  const highlightedFAQRef = useRef<HTMLDivElement>(null);
  const t = useTranslations('faq');

  // 使用翻译作为默认值
  const displayTitle = title || t('title');
  const displayDescription = description || t('description');

  // 过滤FAQ
  const filteredFAQs = useMemo(() => {
    let filtered = faqs;

    // 分类过滤
    if (selectedCategory) {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    // 搜索过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(faq => 
        faq.question.toLowerCase().includes(term) ||
        faq.answer.toLowerCase().includes(term) ||
        faq.related_foods.some(food => food.toLowerCase().includes(term)) ||
        faq.tags.some(tag => tag.toLowerCase().includes(term))
      );
    }

    // 限制数量
    if (maxItems) {
      filtered = filtered.slice(0, maxItems);
    }

    return filtered;
  }, [faqs, selectedCategory, searchTerm, maxItems]);

  // 切换FAQ展开状态
  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  // 清除所有过滤器
  const clearFilters = () => {
    setSearchTerm("");
    setSelectedCategory(null);
  };

  // 处理高亮FAQ
  useEffect(() => {
    if (highlightId) {
      // 自动展开高亮的FAQ
      setOpenItems(prev => new Set([...prev, highlightId]));

      // 滚动到高亮的FAQ
      setTimeout(() => {
        if (highlightedFAQRef.current) {
          highlightedFAQRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100);
    }
  }, [highlightId]);

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* 标题部分 */}
      <div className="text-center space-y-4">
        <h2 className="text-3xl font-bold text-gray-900">{displayTitle}</h2>
        <p className="text-lg text-gray-600">{displayDescription}</p>
      </div>

      {/* 搜索和过滤器 */}
      {(showSearch || showCategories) && (
        <Card>
          <CardContent className="p-6 space-y-4">
            {/* 搜索框 */}
            {showSearch && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={t('search_placeholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            )}

            {/* 分类过滤器 */}
            {showCategories && categories.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">{t('all_categories')}：</label>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant={selectedCategory === null ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(null)}
                  >
                    {t('all_categories')} ({faqs.length})
                  </Button>
                  {categories.map((category) => (
                    <Button
                      key={category.name}
                      variant={selectedCategory === category.name ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category.name)}
                      className="flex items-center gap-1"
                    >
                      <span>{category.icon}</span>
                      {locale === 'zh' ? category.name_zh : category.name} ({category.count})
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* 活动过滤器显示 */}
            {(searchTerm || selectedCategory) && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>当前过滤器：</span>
                {searchTerm && (
                  <Badge variant="secondary">搜索: "{searchTerm}"</Badge>
                )}
                {selectedCategory && (
                  <Badge variant="secondary">
                    分类: {categories.find(c => c.name === selectedCategory)?.name_zh}
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-blue-600 hover:text-blue-800"
                >
                  清除过滤器
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 结果统计 */}
      <div className="text-sm text-gray-600">
        显示 {filteredFAQs.length} 个结果
        {searchTerm || selectedCategory ? ` (共 ${faqs.length} 个FAQ)` : ''}
      </div>

      {/* FAQ列表 */}
      <div className="space-y-4">
        {filteredFAQs.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-gray-500">{t('no_results')}</p>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="mt-4"
              >
                {t('clear_filters')}
              </Button>
            </CardContent>
          </Card>
        ) : (
          filteredFAQs.map((faq) => {
            const isHighlighted = highlightId === faq.id;
            return (
              <Card
                key={faq.id}
                ref={isHighlighted ? highlightedFAQRef : null}
                className={`overflow-hidden transition-all duration-300 ${
                  isHighlighted
                    ? 'ring-2 ring-blue-500 ring-offset-2 bg-blue-50/50'
                    : ''
                }`}
              >
                <Collapsible
                  open={openItems.has(faq.id)}
                  onOpenChange={() => toggleItem(faq.id)}
                >
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{faq.category_icon}</span>
                          <Badge variant="outline" className="text-xs">
                            {locale === 'zh' ? faq.category_zh : faq.category}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {faq.source.name}
                          </Badge>
                        </div>
                        <CardTitle className="text-left text-lg font-semibold text-gray-900">
                          {faq.question}
                        </CardTitle>
                        {faq.answer_summary && (
                          <p className="text-sm text-gray-600 text-left">
                            {faq.answer_summary}
                          </p>
                        )}
                      </div>
                      <div className="ml-4 flex-shrink-0">
                        {openItems.has(faq.id) ? (
                          <ChevronUp className="h-5 w-5 text-gray-400" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="pt-0 space-y-4">
                    {/* 详细答案 */}
                    <div className="prose prose-sm max-w-none">
                      <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                        {faq.answer}
                      </p>
                    </div>

                    {/* 关键要点 */}
                    {faq.key_points && faq.key_points.length > 0 && (
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-blue-900 mb-2">{t('key_points')}</h4>
                        <ul className="space-y-1">
                          {faq.key_points.map((point, index) => (
                            <li key={index} className="text-sm text-blue-800 flex items-start gap-2">
                              <span className="text-blue-600 mt-1">•</span>
                              {point}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* 相关食物和标签 */}
                    <div className="flex flex-wrap gap-4 text-sm">
                      {faq.related_foods && faq.related_foods.length > 0 && (
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-700">{t('related_foods')}</span>
                          <div className="flex flex-wrap gap-1">
                            {faq.related_foods.slice(0, 5).map((food) => (
                              <Badge key={food} variant="outline" className="text-xs">
                                {food}
                              </Badge>
                            ))}
                            {faq.related_foods.length > 5 && (
                              <Badge variant="outline" className="text-xs">
                                +{faq.related_foods.length - 5}
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}

                      {faq.tags && faq.tags.length > 0 && (
                        <div className="flex items-center gap-2">
                          <Tag className="h-3 w-3 text-gray-500" />
                          <div className="flex flex-wrap gap-1">
                            {faq.tags.slice(0, 3).map((tag) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* 查看原文按钮 - 指向我们自己的FAQ页面 */}
                    <div className="pt-2 border-t">
                      <ViewFAQButton
                        faqId={faq.id}
                        locale={locale}
                        text={t('view_original')}
                        variant="ghost"
                        size="sm"
                        className="text-blue-600 hover:text-blue-800 p-0 h-auto"
                      />
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
            );
          })
        )}
      </div>

      {/* 加载更多按钮（如果有限制） */}
      {maxItems && faqs.length > maxItems && (
        <div className="text-center">
          <Button variant="outline">
            {t('load_more', { count: faqs.length - maxItems })}
          </Button>
        </div>
      )}
    </div>
  );
}
